<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.VOrganizeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code
        , name, parent_code, create_user
    </sql>

    <select id="treeOrganize" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select code as key, name as title, parent_code as parent_key, code as value
        from v_organize
        where code != '000000'
    </select>

    <select id="treeOrganizeUser" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select *
        from (select code        as "key",
                     name        as title,
                     parent_code as parent_key,
                     0           as selectable,
                     case when organ_level = 0 then 2 else 1 end as extra
              from v_organize
              union all
              select code as "key", username as title, t2.organize_code as parent_key, 1 as selectable, 3 as extra
              from sys_user t1
                       left join sys_organize_user t2 on t1.code = t2.user_code
              where t2.organize_code is not null
                and t1.user_type != '0') t
        order by t.key
    </select>

    <select id="listAllChild" resultType="java.lang.String">
        WITH RECURSIVE area_tree AS (SELECT code, parent_code
            FROM v_organize
            WHERE code = #{organizeCode}
            UNION ALL
            SELECT t1.code, t1.parent_code
            FROM v_organize t1
            JOIN area_tree t2 ON t1.parent_code = t2.code)
        SELECT code
        FROM area_tree
        order by code
    </select>
</mapper>

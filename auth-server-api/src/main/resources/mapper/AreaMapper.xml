<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.AreaMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        version,
        gmt_create,
        gmt_update,
        code, name, parent_code, organ_level, create_user
    </sql>

    <update id="updateParentName">
        update sys_area
        set parent_name = #{parentName}
        where parent_code = #{parentCode}
    </update>

    <select id="listLeafAreas" resultType="com.zjhh.user.dao.entity.Area">
        select code, parent_code from sys_area t1
        where code in
        <foreach collection="areaCodes" item="areaCode" open="(" close=")" separator=",">
            #{areaCode}
        </foreach>
        and not exists(select 1 from sys_area t2 where t2.parent_code = t1.code)
    </select>

    <select id="getAreaChild" resultType="java.lang.String">
        WITH RECURSIVE area_tree AS (SELECT code, parent_code
                                     FROM sys_area
                                     WHERE code = #{areaCode}
                                     UNION ALL
                                     SELECT t1.code, t1.parent_code
                                     FROM sys_area t1
                                              JOIN area_tree t2 ON t1.parent_code = t2.code)
        SELECT code
        FROM area_tree
        WHERE NOT EXISTS(select 1
                         from sys_area t
                         where area_tree.code = t.parent_code)
    </select>

    <select id="getAreaParent" resultType="java.lang.String">
        WITH RECURSIVE area_tree AS (SELECT code, parent_code
        FROM sys_area
        WHERE code in
        <foreach collection="areaCodes" item="areaCode" open="(" close=")" separator=",">
            #{areaCode}
        </foreach>
        UNION ALL
        SELECT t1.code, t1.parent_code
        FROM sys_area t1
        JOIN area_tree t2 ON t1.code = t2.parent_code)
        SELECT code
        FROM area_tree
        where area_tree.code not in
        <foreach collection="areaCodes" item="areaCode" open="(" close=")" separator=",">
            #{areaCode}
        </foreach>
    </select>
</mapper>

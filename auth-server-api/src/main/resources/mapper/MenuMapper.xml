<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.MenuMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        uuid,
        version,
        gmt_create,
        gmt_update,
        name, router, menu_type, parent_uuid, child_menu, path, icon, sort, redirect
    </sql>

    <select id="findMaxChildMenu" resultType="int">
        select max(child_menu)
        from sys_menu
    </select>


    <select id="findMenuTree" resultType="com.zjhh.user.vo.TreeVo">
        select code as key, name as title, parent_code as parentKey, router as extra
        from sys_menu
        where menu_type != 2
        and (router = 'root' or router = 'menu' or router = 'navMenu' or router in
        (select distinct component from sys_component_tab))
        order by sort::decimal
    </select>

    <select id="listButton" resultType="java.lang.String">
        select perm
        from sys_menu t1
                 left join sys_role_menu t2 on t1.code = t2.menu_code
                 left join sys_user_role t3 on t2.role_code = t3.role_code
        where t3.user_code = #{userCode}
          and t1.parent_code = #{menuCode}
          and t1.menu_type = 2
    </select>

    <select id="findDescMenuTree" resultType="com.zjhh.user.vo.TreeVo">
        select code as key, name as title, parent_code as parentKey, router as extra
        from sys_menu
        where menu_type != 2
        order by sort::decimal
    </select>

    <select id="findOneMenuByComponent" resultType="java.lang.String">
        select code
        from sys_menu
        where router = #{component}
        order by id limit 1
    </select>

    <select id="listSelectMapMenu" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select code as "key",
               name as title,
               code as value,
               parent_code    as parent_key,
               case
                   when router in ('menu', 'navMenu', 'root') then 0
                   else 1
                end as selectable
        from sys_menu_map
        where menu_type != '2'
        order by cast(sort as decimal)
    </select>

    <select id="listAllChildMenu" resultType="com.zjhh.user.dto.MenuDto">
        with recursive t as (select *
                             from sys_menu
                             where code = #{menuCode}
                             union
                             select c.*
                             from sys_menu c,
                                  t
                             where c.parent_code = t.code and c.menu_type != '2')
        select t.code as menu_code, t.name as menu_name, t.sort
        from t order by t.sort
    </select>

    <select id="findJumpMenuTree" resultType="com.zjhh.user.vo.TreeVo">
        select code as key, name as title, parent_code as parentKey, router as extra
        from sys_menu
        where menu_type != 2
        order by sort::decimal
    </select>


</mapper>

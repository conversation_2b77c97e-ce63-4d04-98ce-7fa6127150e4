<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.BuryingPointMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_code, menu_code, open_time, close_time, version, gmt_create, gmt_update
    </sql>

    <select id="findLastOpen" resultType="com.zjhh.user.dao.entity.BuryingPoint">
        select id,
               version
        from sys_burying_point
        where user_code = #{userCode}
          and menu_code = #{menuCode}
          and cur_token = #{curToken}
          and ip = #{ip}
          and close_time is null
          and gmt_create >= #{time}
        order by gmt_create desc limit 1
    </select>

    <select id="page" resultType="com.zjhh.user.vo.BuryingPointVo">
        select t1.id,
        t1.user_code,
        t2.username,
        t1.menu_code,
        t1.menu_name,
        t1.open_time,
        t1.close_time,
        t1.ip
        from sys_burying_point t1
        left join sys_user t2 on t1.user_code = t2.code
        where 1 = 1
        <if test="req.searchKey != null and req.searchKey != ''">
            and (t2.username like concat('%', #{req.searchKey}, '%') or t1.menu_name like concat('%', #{req.searchKey},
            '%'))
        </if>
        <if test="req.startTime != null and req.startTime != ''">
            and t1.open_time >= concat(#{req.startTime}, ' 00:00:00')::timestamp
        </if>
        <if test="req.endTime != null and req.endTime != ''">
            and t1.open_time &lt;= concat(#{req.endTime}, ' 23:59:59')::timestamp
        </if>
        order by t1.gmt_create desc
    </select>

</mapper>

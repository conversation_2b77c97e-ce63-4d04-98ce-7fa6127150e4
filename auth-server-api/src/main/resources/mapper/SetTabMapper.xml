<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.SetTabMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , uuid, tab_uuid, tab_name, menu_code, showed, sort, version, gmt_create, gmt_update
    </sql>

    <select id="listSysTab" resultType="com.zjhh.user.vo.systemshow.SysTabVo">
        select t1.uuid                          as tab_uuid,
               t1.tab_id,
               coalesce(t2.tab_name, t1.tab_name) as tab_name,
               t1.display_area,
               t1.parent_uuid,
               t1.leaf,
               t1.desc,
               t2.tag_explain,
               coalesce(t2.showed, t1.showed)          as showed,
               coalesce(t2.sort, t1.sort)         as sort
        from sys_component_tab t1
                 left join sys_set_tab t2 on t1.uuid = t2.tab_uuid and t2.menu_code = #{menuCode}
        where t1.component = #{component}
        order by coalesce(t2.sort, t1.sort)
    </select>

    <select id="findCharsTab" resultType="com.zjhh.user.dto.SysTabDto">
        select t1.uuid,
               t1.tab_id                        as tag,
               coalesce(t2.tab_name, t1.tab_name) as tag_name,
               t2.tag_explain
        from sys_component_tab t1
                 left join sys_set_tab t2 on t1.uuid = t2.tab_uuid and t2.menu_code = #{menuCode}
        where t1.component = #{component}
          and coalesce(t2.showed, t1.showed) = true
          and t1.display_area = 1
        order by coalesce(t2.sort, t1.sort), t1.tab_id
    </select>

    <select id="findTableTab" resultType="com.zjhh.user.dto.SysTabDto">
        select t1.uuid,
               t1.tab_id                        as tag,
               coalesce(t2.tab_name, t1.tab_name) as tag_name,
               t2.tag_explain
        from sys_component_tab t1
                 left join sys_set_tab t2 on t1.uuid = t2.tab_uuid and t2.menu_code = #{menuCode}
        where t1.component = #{component}
          and coalesce(t2.showed, t1.showed) = true
          and t1.display_area = 2
        order by coalesce(t2.sort, t1.sort), t1.tab_id
    </select>

    <select id="findButtons" resultType="com.zjhh.user.dto.SysBtnDto">
        select t1.button_id                           as tag,
               coalesce(t2.button_name, t1.button_name) as tag_name,
               coalesce(t2.display_set, t1.display_set) as display_set
        from sys_component_button t1
                 left join sys_set_button t2 on t1.uuid = t2.button_uuid and t2.menu_code = #{menuCode}
        where t1.component = #{component}
          and coalesce(t2.display_set, t1.display_set) != 3
        order by coalesce(t2.sort, t1.sort), t1.button_id
    </select>

    <select id="findSearches" resultType="com.zjhh.user.dto.SysSearchDto">
        select t1.search_id                               as tag,
               coalesce(t2.search_name, t1.search_name)     as tag_name,
               coalesce(t2.default_value, t1.default_value) as default_value,
               coalesce(t2.display_set, t1.display_set)     as displaySet,
               t1.search_type
        from sys_component_search t1
                 left join sys_set_search t2
                           on t1.uuid = t2.search_uuid and t2.menu_code = #{menuCode}
        where t1.component = #{component}
        order by coalesce(t2.sort, t1.sort), t1.search_id
    </select>

    <select id="findAllTab" resultType="com.zjhh.user.dto.SysTabDto">
        select t1.uuid,
               t1.parent_uuid,
               t1.display_area,
               t1.tab_id                        as tag,
               t1.leaf,
               coalesce(t2.tab_name, t1.tab_name) as tag_name,
               t2.tag_explain
        from sys_component_tab t1
                 left join sys_set_tab t2 on t1.uuid = t2.tab_uuid and t2.menu_code = #{menuCode}
        where t1.component = #{component}
          and coalesce(t2.showed, t1.showed) = true
        order by coalesce(t2.sort, t1.sort), t1.tab_id
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.RoleMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        version,
        gmt_create,
        gmt_update,
        code, name, description
    </sql>

    <select id="listUser" resultType="java.lang.String">
        select code
        from sys_user
        where id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and code not in (select user_code from sys_user_role where role_code = #{roleCode})
    </select>

    <select id="listUserRole" resultType="com.zjhh.user.dao.entity.Role">
        select t2.id,
               t2.code,
               t2.name,
               t2.role_key,
               t2.description,
               t2.create_user,
               t2.version,
               t2.gmt_create,
               t2.gmt_update
        from sys_user_role t1
                 left join sys_role t2 on t1.role_code = t2.code
        where t1.user_code = #{userCode}
    </select>
</mapper>

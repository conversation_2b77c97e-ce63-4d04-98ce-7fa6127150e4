<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.workflow.dao.mapper.IFlowableProcessInstanceDao">
    <select id="getPagerModel"
            resultType="com.zjhh.workflow.vo.ProcessInstanceVo">
        SELECT DISTINCT
        t1.ID_ AS processInstanceId,
        t1.PROC_DEF_ID_ as processDefinitionId,
        t1.NAME_ AS formName,
        t1.TENANT_ID_ AS systemSn,
        t1.BUSINESS_KEY_ AS businessKey,
        t1.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        t2.ID_ AS starterId,
        t2.FIRST_ as starter
        FROM
        ACT_HI_PROCINST t1
        LEFT JOIN act_id_user t2 ON t1.START_USER_ID_ = t2.ID_
        <where>
            <if test="userCode != null">and t1.START_USER_ID_ = #{params.userCode}</if>
            <if test="userName != null">and t2.FIRST_ like CONCAT('%',#{params.userName},'%')</if>
            <if test="formName != null">and t1.NAME_ like CONCAT('%',#{params.formName},'%')</if>
        </where>
    </select>
</mapper>

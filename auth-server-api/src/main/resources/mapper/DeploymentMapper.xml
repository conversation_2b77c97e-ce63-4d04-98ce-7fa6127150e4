<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.workflow.dao.mapper.DeploymentMapper">

    <update id="updateResource">
        update ACT_GE_BYTEARRAY
        set BYTES_ = (select BYTES_ from ACT_GE_BYTEARRAY where DEPLOYMENT_ID_ = #{id} and name_ like '%xml')
        where DEPLOYMENT_ID_ = #{oldId} and  name_ like '%xml'
    </update>
    <select id="getOldId" resultType="java.lang.String">
        select DEPLOYMENT_ID_
        from ACT_RE_PROCDEF
        where VERSION_ =
              (select VERSION_ - 1 from ACT_RE_PROCDEF where DEPLOYMENT_ID_ = #{id})
          and KEY_ = #{key}
    </select>
    <select id="getFlowKey" resultType="java.lang.String">
     select key_
        from ACT_RE_PROCDEF where DEPLOYMENT_ID_ = #{id}
    </select>
    <select id="getDefId" resultType="java.lang.String">
        select id_
        from ACT_RE_PROCDEF where deployment_id_ = #{id}
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.UserRoleMapper">

    <!-- 通用查询结果列 -->
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        version,
        gmt_create,
        gmt_update,
        user_code, role_code
    </sql>

    <select id="listRole" resultType="com.zjhh.user.vo.RoleVo">
        select sur.role_code,
               sr.role_key,
               sr.name,
               sr.description
        from sys_role sr
                 left join sys_user_role sur on sr.code = sur.role_code
        where sur.user_code = #{userCode}
    </select>

    <select id="pageRoleUser" resultType="com.zjhh.user.vo.UserVo">
        select su.id,
        su.login_name,
        su.username,
        su.locked,
        sur.user_code
        from sys_user_role sur
        left join sys_user su on su.code = sur.user_code
        left join sys_role sr on sr.code = sur.role_code
        where sr.id = #{req.roleId} and su.id is not null
        <if test="req.searchKey != null and req.searchKey != ''">
            and (su.login_name like CONCAT('%', #{req.searchKey}, '%') or su.username like CONCAT('%', #{req.searchKey},
            '%'))
        </if>
        order by su.gmt_create desc
    </select>

    <select id="listRoleKeys" resultType="java.lang.String">
        select t2.role_key
        from sys_user_role t1
                 left join sys_role t2 on t1.role_code = t2.code
        where t1.user_code = #{userCode}
    </select>


</mapper>

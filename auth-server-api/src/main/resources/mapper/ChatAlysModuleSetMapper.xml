<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.aialys.dao.mapper.ChatAlysModuleSetMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, module_id, opened, prompt_template_id, prompt, llm_id, create_user, create_time, update_time
    </sql>

    <resultMap id="AlysModuleVo" type="com.zjhh.aialys.vo.AlysModuleVo">
        <result column="module_id" property="moduleId"/>
        <result column="module" property="module"/>
        <collection property="tabs" ofType="com.zjhh.aialys.vo.AlysTabVo">
            <result column="tab_id" property="tabId"/>
            <result column="tab" property="tab"/>
        </collection>
    </resultMap>

    <select id="listAlysModule" resultMap="AlysModuleVo">
        select t1.module_id,
               t2.module,
               t4.tab_id,
               t3.tab
        from chat_alys_module_set t1
                 inner join chat_alys_module t2 on t1.module_id = t2.id
                 left join chat_alys_tab t3 on t3.module_id = t1.module_id
                 left join chat_alys_tab_set t4 on t4.tab_id = t3.id
        where t2.parent_id = (select id from chat_alys_module t where t.module = #{component})
          and t1.opened = true
    </select>

</mapper>

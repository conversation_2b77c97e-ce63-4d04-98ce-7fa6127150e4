<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.UserMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        uuid,
        version,
        gmt_create,
        gmt_update,
        true_name, login_name, login_password, mobile, locked, salt, create_info, user_info, manager
    </sql>

    <resultMap id="UserVo" type="com.zjhh.user.vo.UserVo">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="username" property="username"/>
        <result column="login_name" property="loginName"/>
        <result column="locked" property="locked"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="mobile" property="mobile"/>
        <result column="avatar" property="avatar"/>
        <result column="user_type" property="userType"/>
        <result column="new_user" property="newUser"/>
        <result column="create_user" property="createUser"/>
        <result column="organize_name" property="organizeName"/>
    </resultMap>

    <resultMap id="UserDetailVo" type="com.zjhh.user.vo.UserDetailVo">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="username" property="username"/>
        <result column="login_name" property="loginName"/>
        <result column="locked" property="locked"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="mobile" property="mobile"/>
        <result column="avatar" property="avatar"/>
        <result column="description" property="description"/>
        <result column="user_type" property="userType"/>
        <result column="new_user" property="newUser"/>
        <result column="create_user" property="createUser"/>
        <result column="email" property="email"/>
    </resultMap>

    <select id="selectPageUserVo" resultMap="UserVo">
        select su.id,
        su.code,
        su.username,
        su.login_name,
        su.mobile,
        su.locked,
        su.description,
        su.user_type,
        su.new_user,
        su.create_user,
        su.gmt_create,
        su.sync_zwdd,
        su.avatar,
        vo.name as organize_name
        from sys_user su
        left join sys_organize_user sou on su.code = sou.user_code
        left join v_organize vo on vo.code = sou.organize_code
        where 1 = 1
        <if test="req.organizeCode != null and req.organizeCode != ''">
            and vo.code in
            <foreach collection="req.organizeCodes" item="organizeCode" open="(" close=")" separator=",">
                #{organizeCode}
            </foreach>
        </if>
        <if test="req.loginName != null and req.loginName != ''">
            and su.login_name like CONCAT('%', #{req.loginName}, '%')
        </if>
        <if test="req.username != null and req.username != ''">
            and su.username like CONCAT('%', #{req.username}, '%')
        </if>
        <if test="req.locked != null">
            and su.locked = #{req.locked}
        </if>
        <if test="req.isSuperManager != null and req.isSuperManager == false">
            and su.user_type != '0'
        </if>
    </select>

    <select id="listRoleVo" resultType="com.zjhh.user.vo.RoleVo">
        select sr.code as roleCode,
        sr.role_key as roleKey,
        sr.name,
        sur.user_code as userCode
        from sys_role sr
        left join sys_user_role sur on sr.code = sur.role_code
        where sur.user_code in
        <foreach collection="userCodes" item="userCode" separator="," close=")" open="(">
            #{userCode}
        </foreach>
    </select>

    <select id="selectPageRoleUserVo" resultType="com.zjhh.user.dao.entity.User">
        SELECT su.id,
        su.code,
        su.username,
        su.login_name AS loginName,
        su.mobile,
        su.locked,
        su.description,
        su.user_type AS userType,
        su.new_user AS newUser,
        su.create_user AS createUser,
        su.gmt_create AS gmtCreate,
        su.avatar
        FROM sys_user su
        left join sys_organize_user sou on sou.user_code = su.code
        WHERE su.user_type != '0'
        and su.code not in (select user_code from sys_user_role where role_code = #{req.roleCode})
        <if test="req.searchKey != null and req.searchKey != ''">
            and ( su.login_name like CONCAT('%', #{req.searchKey}, '%') or su.username like CONCAT('%',
            #{req.searchKey}, '%'))
        </if>
        <if test="req.organizeCodes != null and req.organizeCodes.size > 0">
            and sou.organize_code in
            <foreach collection="req.organizeCodes" item="organizeCode" open="(" close=")" separator=",">
                #{organizeCode}
            </foreach>
        </if>
    </select>

    <select id="countOrganizeSystemManager" resultType="java.lang.Integer">
        select count(*)
        from sys_organize_user sou
                 left join sys_user su on sou.user_code = su.code
        where su.user_type = '1'
          and sou.organize_code = #{organizeCode}
    </select>

    <select id="listAllPermission" resultType="java.lang.String">
        select distinct t1.router as permission
        from sys_menu t1
        left join sys_role_menu t2 on t1.code = t2.menu_code
        left join sys_user_role t3 on t2.role_code = t3.role_code
        where t1.router is not null
        and t1.router != 'menu'
        and t1.router != 'navMenu'
        <if test="userCode != null and userCode != ''">
            and t3.user_code = #{userCode}
        </if>
        union all
        select distinct t1.perm as permission
        from sys_menu t1
        left join sys_role_menu t2 on t1.code = t2.menu_code
        left join sys_user_role t3 on t2.role_code = t3.role_code
        where t1.menu_type = '2'
        <if test="userCode != null and userCode != ''">
            and t3.user_code = #{userCode}
        </if>
        order by permission
    </select>
</mapper>

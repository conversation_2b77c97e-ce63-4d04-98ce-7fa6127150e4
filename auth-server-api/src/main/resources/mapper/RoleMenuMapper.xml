<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.RoleMenuMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        version,
        gmt_create,
        gmt_update,
        role_code, menu_code
    </sql>

    <select id="listCheckedMenu" resultType="com.zjhh.user.dao.entity.Menu">
        select sm.code, sm.menu_type as menuType
        from sys_role_menu srm
                 left join sys_menu sm on srm.menu_code = sm.code
        where srm.role_code = #{roleCode}
          and sm.code is not null
    </select>

    <select id="countUserMenu" resultType="java.lang.Integer">
        select count(*)
        from sys_user_role sur
                 left join sys_role_menu srm on sur.role_code = srm.role_code
        where srm.menu_code = #{menuCode}
          and sur.user_code = #{userCode}
    </select>

</mapper>

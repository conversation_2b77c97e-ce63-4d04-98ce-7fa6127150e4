<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.SetSearchMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , uuid, search_uuid, search_name, default_value, display_set, menu_code, sort, version, gmt_create,
        gmt_update
    </sql>

    <select id="listSysSearch" resultType="com.zjhh.user.vo.systemshow.SysSearchVo">
        select t1.uuid                                      as searchUuid,
               t1.search_id,
               coalesce(t2.search_name, t1.search_name)     as search_name,
               coalesce(t2.default_value, t1.default_value) as default_value,
               coalesce(t2.display_set, t1.display_set)     as display_set,
               t1.display_show,
               t1.desc,
               t1.editable,
               coalesce(t2.sort, t1.sort)                   as sort
        from sys_component_search t1
                 left join sys_set_search t2 on t1.uuid = t2.search_uuid and t2.menu_code = #{menuCode}
        where t1.component = #{component}
        order by coalesce(t2.sort, t1.sort)
    </select>

</mapper>

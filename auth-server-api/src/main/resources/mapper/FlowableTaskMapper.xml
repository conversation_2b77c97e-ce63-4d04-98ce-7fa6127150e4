<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.workflow.dao.mapper.FlowableTaskMapper">

    <select id="getApplyingTasks" parameterType="com.zjhh.workflow.request.TaskReq"
            resultType="com.zjhh.workflow.vo.TaskVo">
        SELECT DISTINCT t1.ID_           AS taskId,
                        t1.NAME_         AS taskName,
                        t2.NAME_         AS formName,
                        t2.TENANT_ID_    AS systemSn,
                        t2.BUSINESS_KEY_ AS businessKey,
                        t2.PROC_INST_ID_ AS processInstanceId,
                        t1.CREATE_TIME_  AS startTime
        FROM act_ru_task t1
                 INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
                 LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        WHERE t2.BUSINESS_KEY_ IS NOT NULL
          AND (
                    t1.ASSIGNEE_ = #{params.userCode}
                OR (
                            t1.ASSIGNEE_ IN (SELECT G.group_id_
                                             FROM act_id_membership G
                                             WHERE G.user_id_ = #{params.userCode})
                        )
                OR (
                            (
                                    t1.ASSIGNEE_ IS NULL
                                    OR t1.ASSIGNEE_ = ''
                                )
                            AND (
                                        t3.USER_ID_ = #{params.userCode}
                                    OR t3.GROUP_ID_ IN (SELECT g.group_id_
                                                        FROM act_id_membership g
                                                        WHERE g.user_id_ = #{params.userCode})
                                )
                        )
            )
    </select>
    <select id="getApplyedTasks" resultType="com.zjhh.workflow.vo.TaskVo">
        SELECT DISTINCT t1.ID_           AS taskId,
                        t1.NAME_         AS taskName,
                        t2.FIRST_        AS approver,
                        t2.ID_           AS approverId,
                        t3.NAME_         AS formName,
                        t3.BUSINESS_KEY_ AS businessKey,
                        t3.PROC_INST_ID_ AS processInstanceId,
                        t3.TENANT_ID_    as systemSn,
                        t1.START_TIME_   AS startTime,
                        t1.END_TIME_     as endTime
        FROM act_hi_taskinst t1
                 LEFT JOIN act_id_user t2 ON t1.ASSIGNEE_ = t2.ID_
                 LEFT JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        WHERE t1.END_TIME_ is not null
          AND t1.ASSIGNEE_ = #{params.userCode}
    </select>
    <select id="getAdviceGuid" resultType="java.lang.String">
        select TEXT_ as guid
        from ACT_HI_VARINST
        where PROC_INST_ID_ in (select DISTINCT T.processInstanceId
                                from (SELECT DISTINCT t3.PROC_INST_ID_ AS processInstanceId

                                      FROM ACT_HI_TASKINST t1
                                               LEFT JOIN ACT_ID_USER t2 ON t1.ASSIGNEE_ = t2.ID_
                                               LEFT JOIN ACT_HI_PROCINST t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
                                      WHERE t1.END_TIME_ is not null
                                        AND t3.BUSINESS_KEY_ = 'advice'
                                        AND (t1.ASSIGNEE_ = #{userCode} or t1.ASSIGNEE_ = #{unitCode})
                                        AND t1.EXECUTION_ID_ is not null
                                      union all
                                      SELECT DISTINCT t2.PROC_INST_ID_ AS processInstanceId
                                      FROM ACT_RU_TASK t1
                                               INNER JOIN ACT_RU_EXECUTION t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
                                               LEFT JOIN ACT_RU_IDENTITYLINK t3 ON t3.PROC_INST_ID_ = t1.PROC_INST_ID_
                                      WHERE t2.BUSINESS_KEY_ IS not NULL
                                        and t2.BUSINESS_KEY_ = 'advice'
                                        AND (
                                              (t1.ASSIGNEE_ = #{userCode} or t1.ASSIGNEE_ = #{unitCode})
                                              OR (
                                                      t1.ASSIGNEE_ IN (SELECT G.group_id_
                                                                       FROM ACT_ID_MEMBERSHIP G
                                                                       WHERE G.user_id_ = #{userCode})
                                                  )
                                              OR (
                                                      (
                                                              t1.ASSIGNEE_ IS NULL
                                                              OR t1.ASSIGNEE_ = ''
                                                          )
                                                      AND (
                                                              (t3.USER_ID_ = #{userCode} or t3.USER_ID_ = #{unitCode})
                                                              OR t3.GROUP_ID_ IN (SELECT g.group_id_
                                                                                  FROM ACT_ID_MEMBERSHIP g
                                                                                  WHERE g.user_id_ = #{userCode})
                                                          )
                                                  )
                                          )) T)
          and NAME_ = 'advice_guid'
        order by TEXT_::decimal DESC
    </select>
    <select id="processDefIdByKey" resultType="java.lang.String">
        select ID_
        from ACT_RE_PROCDEF
        where KEY_ = #{key}
        order by VERSION_ DESC limit 1
    </select>

    <select id="taskUsers" resultType="java.lang.String">
        select USER_ID_
        from ACT_HI_IDENTITYLINK
        where (PROC_INST_ID_ = #{taskId} or TASK_ID_ = #{taskId})
          and (TYPE_ = 'candidate' or TYPE_ = 'participant')
    </select>
    <select id="checkMultiTaskCompleted" resultType="java.lang.Boolean">
        select case
                   when coalesce(sum(t1.completeCount::decimal), 0) = coalesce(sum(t1.total::decimal), 0) then true
                   else false end
        from (select text_ as total, '0' as completeCount
              from act_ru_variable
              where proc_inst_id_ = #{processId}
                and (name_ = 'nrOfInstances')
              union all
              select '0' as total, text_ as completeCount
              from act_ru_variable
              where proc_inst_id_ = #{processId}
                and (name_ = 'nrOfCompletedInstances')) t1
    </select>
    <select id="instanceUsers" resultType="java.lang.String">
        select USER_ID_
        from ACT_HI_IDENTITYLINK
        where (PROC_INST_ID_ = #{instanceId} or TASK_ID_ = #{instanceId})
          and TYPE_ = 'candidate'
    </select>
    <select id="currentTaskUsers" resultType="java.lang.String">
        select USER_ID_
        from ACT_RU_IDENTITYLINK
        where  TASK_ID_ = #{taskId}
          and TYPE_ = 'candidate'
    </select>


</mapper>
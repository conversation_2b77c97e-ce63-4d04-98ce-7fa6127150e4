<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.SetColumnMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , uuid, column_uuid, column_name, menu_code, showed, sort, gmt_create, gmt_update
    </sql>

    <select id="listSysColumn" resultType="com.zjhh.user.vo.SysColumnVo">
        select
        #{menuCode} as menuCode,
        t1.uuid as columnUuid,
        t1.tab_uuid as tabUuid,
        t3.tab_name as tabName,
        t1.column_id as columnId,
        coalesce(t2.column_name, t1.column_name) as columnName,
        coalesce(t2.showed, t1.showed) as showed,
        coalesce(t2.sort, t1.sort) as sort,
        t2.link_menu_code as linkMenuCode,
        t1.parent_uuid,
        t1.desc,
        t1.is_leaf
        from sys_component_column t1
        left join sys_set_column t2 on t1.uuid = t2.column_uuid and t2.menu_code = #{menuCode}
        left join sys_component_tab t3 on t1.tab_uuid = t3.uuid
        where t1.component = #{component}
        and t1.tab_uuid in
        <foreach collection="tabUuids" item="tabUuid" open="(" close=")" separator=",">
            #{tabUuid}
        </foreach>
        order by coalesce(t2.sort, t1.sort)
    </select>

    <select id="listSysShowColumn" resultType="com.zjhh.user.dto.SysColumnDto">
        select t1.uuid,
               t1.tab_uuid,
               t1.column_id,
               coalesce(t2.column_name, t1.column_name) as column_name,
               t1.parent_uuid,
               t1.is_leaf,
               t2.link_menu_code,
               coalesce(t2.sort, t1.sort)               as sort
        from sys_component_column t1
                 left join sys_set_column t2 on t1.uuid = t2.column_uuid and t2.menu_code = #{menuCode}
        where t1.component = #{component}
          and (coalesce(t2.showed, t1.showed) or t1.is_leaf = false)
        order by coalesce(t2.sort, t1.sort), t1.column_id
    </select>

    <select id="listSelectedLeftColumn" resultType="com.zjhh.user.dto.SelectedColumnDto">
        select
        t1.tab_uuid,
        t1.uuid as column_uuid
        from sys_component_column t1
        left join sys_set_column t2 on t1.uuid = t2.column_uuid and t2.menu_code = #{menuCode}
        where t1.component = #{component}
        and t1.is_leaf
        and coalesce(t2.showed, t1.showed)
        and t1.tab_uuid in
        <foreach collection="tabUuids" item="tabUuid" open="(" close=")" separator=",">
            #{tabUuid}
        </foreach>
    </select>

    <select id="findLinkMenuCode" resultType="java.lang.String">
        select t1.link_menu_code
        from sys_set_column t1
                 left join sys_component_column t2 on t1.column_uuid = t2.uuid
        where t1.menu_code = #{req.menuCode}
          and t2.column_id = #{req.columnId} limit 1
    </select>

    <select id="getSystemColumn" resultType="com.zjhh.user.vo.systemshow.GetSystemColumnVo">
        select t1.column_id,
               coalesce(t2.column_name, t1.column_name) as column_name
        from sys_mobile_component_column t1
                 left join sys_mobile_set_column t2 on t1.uuid = t2.column_uuid and t2.menu_code = #{menuCode}
        where t1.component = #{component}
          and t1.tab_uuid = (select uuid
                             from sys_mobile_component_tab t3
                             where t3.tab_id = #{tabId}
                               and t3.component =
                                   #{component})
          and coalesce(t2.showed, t1.showed)
        order by coalesce(t2.sort, t1.sort)
    </select>


</mapper>

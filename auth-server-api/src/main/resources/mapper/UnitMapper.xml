<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.UnitMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        version,
        gmt_create,
        gmt_update,
        code, name, parent_code, area_code, create_user
    </sql>

    <update id="updateParentName">
        update sys_unit
        set parent_name = #{parentName}
        where parent_code = #{parentCode}
    </update>

    <update id="updateAreaName">
        update sys_unit
        set area_name = #{areaName}
        where area_code = #{areaCode}
    </update>
    <select id="listUnit" resultType="com.zjhh.user.vo.MenuTreeVo">
        select code as key, name as title, code as value
        from sys_unit
        where area_code = #{admDivCode}
    </select>

    <select id="listUnitSelect" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select code as key, name as title, code as value, 'root' as parent_key
        from sys_unit
        where parent_code = area_code
        union all
        select code as key, name as title, code as value, parent_code as parent_key
        from sys_unit
        where parent_code != area_code
    </select>
</mapper>

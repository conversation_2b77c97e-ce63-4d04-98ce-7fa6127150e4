<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.workflow.dao.mapper.IFlowableProcessDefinitionDao">
    <update id="updateResource">
        update ACT_GE_BYTEARRAY
        set BYTES_ = (select BYTES_ from ACT_GE_BYTEARRAY where ID_ = #{id})
        where ID_ = #{oldId}
    </update>
    <select id="getById" parameterType="java.lang.String"
            resultType="com.zjhh.workflow.vo.ProcessDefinitionVo">
        SELECT t.CATEGORY_           AS category,
               t.ID_                 AS id,
               t.NAME_               AS NAME,
               t.KEY_                AS modelKey,
               t.RESOURCE_NAME_      AS resourceName,
               t.DGRM_RESOURCE_NAME_ as dgrmResourceName,
               t.SUSPENSION_STATE_   as suspensionState,
               t.VERSION_            AS version,
               t.DEPLOYMENT_ID_      AS deploymentId,
               t.TENANT_ID_          AS tenantId
        FROM ACT_RE_PROCDEF t
        where t.ID_ = #{processDefinitionId}
    </select>
    <select id="getPagerModel"
            resultType="com.zjhh.workflow.vo.ProcessDefinitionVo">
        SELECT
        t.CATEGORY_ AS category,
        t.ID_ AS id,
        t.NAME_ AS NAME,
        t.KEY_ AS modelKey,
        t.RESOURCE_NAME_ AS resourceName,
        t.DGRM_RESOURCE_NAME_ as dgrmResourceName,
        t.SUSPENSION_STATE_ as suspensionState,
        t.VERSION_ AS version,
        t.DEPLOYMENT_ID_ AS deploymentId,
        t.TENANT_ID_ AS tenantId
        FROM
        ACT_RE_PROCDEF t
        <where>
            <if test="name!=null and name!=''">
                and t.NAME_ LIKE CONCAT('%',#{params.name},'%')
            </if>
            <if test="modelKey!=null and modelKey!=''">
                or t.KEY_ LIKE CONCAT('%',#{params.modelKey},'%')
            </if>
        </where>
        ORDER BY
        t.VERSION_ DESC
    </select>
    <select id="getOldId" resultType="java.lang.String">
        select DEPLOYMENT_ID_
        from ACT_RE_PROCDEF
        where VERSION_ =
              (select VERSION_ - 1 from ACT_RE_PROCDEF where DEPLOYMENT_ID_ = #{id})
          and KEY_ = #{key}
    </select>
</mapper>

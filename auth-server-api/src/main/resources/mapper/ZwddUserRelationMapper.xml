<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.ZwddUserRelationMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , code, username, mobile, gmt_create, gmt_update, tenant_user_id
    </sql>
    <select id="getZwddAccountId" resultType="java.lang.String">
        select account_id
        from sys_user_relation
        where user_code = #{userCode}
    </select>

</mapper>

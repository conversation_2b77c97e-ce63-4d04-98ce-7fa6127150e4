<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.SetButtonMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , uuid, button_uuid, button_name, menu_code, sort, version, gmt_create, gmt_update
    </sql>

    <select id="listSysButton" resultType="com.zjhh.user.vo.systemshow.SysButtonVo">
        select t1.uuid                                as button_uuid,
               t1.button_id,
               coalesce(t2.button_name, t1.button_name) as button_name,
               coalesce(t2.display_set, t1.display_set) as display_set,
               t1.display_show,
               t1.desc,
               coalesce(t2.sort, t1.sort)               as sort
        from sys_component_button t1
                 left join sys_set_button t2 on t1.uuid = t2.button_uuid and t2.menu_code = #{menuCode}
        where t1.component = #{component}
        order by coalesce(t2.sort, t1.sort)
    </select>

</mapper>

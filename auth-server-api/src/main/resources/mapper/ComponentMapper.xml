<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.user.dao.mapper.ComponentMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        version,
        gmt_create,
        gmt_update,
        code, title, component, parent_code, perm
    </sql>

    <select id="listComponentButton" resultType="com.zjhh.user.dao.entity.Component">
        select code, id, code, title, component, parent_code as parentCode, perm
        from sys_component
        where parent_code = (select code
                             from sys_component
                             where component = #{parentComponent})
          and component = 'button'
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.aialys.dao.mapper.ChatAlysModuleMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, module, name, parent_id, sort
    </sql>

    <resultMap id="AlysConfigVo" type="com.zjhh.aialys.vo.AlysConfigVo">
        <result column="id" property="id"/>
        <result column="page" property="page"/>
        <result column="module_name" property="moduleName"/>
        <result column="opened" property="opened"/>
        <result column="prompt_template_id" property="promptTemplateId"/>
        <result column="prompt" property="prompt"/>
        <result column="module" property="module"/>
        <collection property="tabs" ofType="com.zjhh.aialys.vo.AlysConfigTabVo">
            <result column="tab_id" property="tabId"/>
            <result column="tab" property="tab"/>
            <result column="tab_name" property="tabName"/>
            <result column="input_llm" property="inputLlm"/>
            <result column="tab_type" property="tabType"/>
            <result column="tab_desc" property="tabDesc"/>
            <collection property="columns" ofType="com.zjhh.aialys.vo.AlysConfigColumnVo">
                <result column="column_id" property="columnId"/>
                <result column="field" property="field"/>
                <result column="column_name" property="columnName"/>
                <result column="column_type" property="columnType"/>
                <result column="unit" property="unit"/>
                <result column="column_desc" property="columnDesc"/>
            </collection>
        </collection>
    </resultMap>

    <select id="listAlysConfigLeftTree" resultType="com.zjhh.aialys.vo.AlysConfigLeftTreeVo">
        select t1.id,
               t1.name,
               t1.parent_id,
               t2.id is not null as       configured,
               coalesce(t2.opened, false) opened,
       t1.parent_id != 'root' as selectable
        from chat_alys_module t1
            left join chat_alys_module_set t2 on t1.id = t2.module_id
        order by t1.sort
    </select>

    <select id="getAlysConfig" resultMap="AlysConfigVo">
        select t1.id,
               t1.module,
               t3.name                                  as page,
               t1.name                                  as module_name,
               t2.opened,
               t2.prompt_template_id,
               t2.prompt,
               t4.id                                    as tab_id,
               t4.tab,
               t4.name                                  as tab_name,
               t5.input_llm,
               t4.type                                  as tab_type,
               coalesce(t5.description, t4.description) as tab_desc,
               t6.id                                    as column_id,
               t6.field,
               coalesce(t7.column_name, t6.column_name) as column_name,
               t6.type                                  as column_type,
               coalesce(t7.unit, t6.unit)               as unit,
               coalesce(t7.description, t6.description) as column_desc
        from chat_alys_module t1
                 left join chat_alys_module_set t2 on t1.id = t2.module_id
                 left join chat_alys_module t3 on t1.parent_id = t3.id
                 left join chat_alys_tab t4 on t4.module_id = t1.id
                 left join chat_alys_tab_set t5 on t4.id = t5.tab_id
                 left join chat_alys_column t6 on t6.tab_id = t4.id
                 left join chat_alys_column_set t7 on t7.column_id = t6.id
        where t1.id = #{id}
    </select>

</mapper>

package com.zjhh.comm.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2021/5/12 15:45
 */
public class StringUnicodeSerializer extends JsonSerializer<String> {

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value.contains("&emsp;") || value.contains("&nbsp;") || value.contains(" ")) {
            gen.writeString(value.replaceAll("&emsp;", "\u3000").replaceAll("&nbsp;", "\u3000").replaceAll(" ", "\u3000"));
        } else {
            gen.writeString(value);
        }

    }
}

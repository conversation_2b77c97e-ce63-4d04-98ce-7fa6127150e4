package com.zjhh.comm.serializer;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * BigDecimal 整数输出
 *
 * <AUTHOR>
 * @since 2021/11/23 14:02
 */
public class BigDecimalLongSerialize extends JsonSerializer<BigDecimal> {

    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (ObjectUtil.isNotNull(value)) {
            gen.writeNumber(value.longValue());
        }
    }
}

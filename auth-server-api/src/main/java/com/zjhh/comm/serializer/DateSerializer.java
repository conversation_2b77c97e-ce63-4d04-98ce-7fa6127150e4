package com.zjhh.comm.serializer;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2021/5/13 10:50
 */
public class DateSerializer extends JsonSerializer<String> {

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        DateTime dateTime = DateUtil.parse(value, "yyyyMMdd", "yyyyMM", "yyyy");
        if (value.length() == 4) {
            gen.writeString(DateUtil.format(dateTime, "yyyy年"));
        } else if (value.length() == 6) {
            gen.writeString(DateUtil.format(dateTime, "yyyy年MM月"));
        } else if (value.length() == 8) {
            gen.writeString(DateUtil.format(dateTime, "yyyy年MM月dd日"));
        } else {
            gen.writeString(value);
        }
    }
}

package com.zjhh.comm.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/5/10 17:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class SortComparable implements Comparable<SortComparable> {

    private String value;

    @Override
    public int compareTo(SortComparable o) {
        if (this.value.length() != o.value.length()) {
            return this.value.length() - o.value.length();
        }
        return value.compareTo(o.value);
    }

}

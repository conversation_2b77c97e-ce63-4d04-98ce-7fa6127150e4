package com.zjhh.comm.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/3/10 18:34
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IdReq extends BaseReq {

    private static final long serialVersionUID = 3021259745201383167L;

    @Schema(description = "id")
    @NotNull(message = "ID不能为空！")
    private String id;
}

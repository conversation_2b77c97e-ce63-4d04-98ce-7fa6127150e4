package com.zjhh.comm.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/10 14:27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TreeSelectedReq extends BaseReq {

    private static final long serialVersionUID = -3069621859442474243L;

    @Schema(description = "选择的keys：如果列表里面有all，当做全选")
    private List<String> selectedKeys;
}

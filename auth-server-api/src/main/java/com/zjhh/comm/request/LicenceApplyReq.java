package com.zjhh.comm.request;

import com.zjhh.comm.dto.LicenceProduct;
import com.zjhh.comm.dto.LicenceServerInfo;
import com.zjhh.comm.dto.LicenceServiceInfo;
import com.zjhh.comm.dto.LicenceUser;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/24 11:21
 */
@Data
public class LicenceApplyReq implements Serializable {

    private static final long serialVersionUID = 4273667897576833028L;


    /**
     * 用户信息
     */
    private LicenceUser licenceUser;

    /**
     * 服务商信息
     */
    private LicenceServiceInfo licenceService;

    /**
     * 产品注册信息
     */
    private LicenceProduct licenceProduct;

    /**
     * 服务器信息
     */
    private List<LicenceServerInfo> licenceServers;

}

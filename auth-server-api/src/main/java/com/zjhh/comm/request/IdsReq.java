package com.zjhh.comm.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/18 14:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IdsReq extends BaseReq {

    private static final long serialVersionUID = 7735503667654540210L;

    @Schema(description = "id数组")
    private List<String> ids;
}

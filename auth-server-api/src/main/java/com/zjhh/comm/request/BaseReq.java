package com.zjhh.comm.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/6/8 16:06
 */
@Data
public class BaseReq implements Serializable {

    private static final long serialVersionUID = -3542849068356535331L;

    /**
     * 重大投资项目权限
     */
    @Schema(hidden = true)
    private List<String> majorInvestProAuthCodes;

    /**
     * 主管部门权限
     */
    @Schema(hidden = true)
    private List<String> supdepAuthCodes;

    /**
     * 预算单位权限
     */
    @Schema(hidden = true)
    private List<String> agencyAuthCodes;

}

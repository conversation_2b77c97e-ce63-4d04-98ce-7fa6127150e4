package com.zjhh.comm.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/11/9 16:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TreeSelectVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 8209595733932740442L;

    private String key;

    private String parentKey;

    private String title;

    private String value;

    private Object extra;

    private Boolean selectable;

    private Map<String, String> slots;

    private Map<String, String> scopedSlots;

    private List<TreeSelectVo> children;

    public List<TreeSelectVo> getChildren() {
        return CollUtil.isEmpty(children) ? null : children;
    }

    public Map<String, String> getScopedSlots() {
        Map<String, String> map = new HashMap<>(1);
        map.put("title", "title");
        return map;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        return ObjectUtil.equal(key, ((TreeSelectVo) o).key);
    }

    @Override
    public int hashCode() {
        return key.hashCode();
    }

    public Map<String, String> getSlots() {
        if (ObjectUtil.isNotNull(extra)) {
            if (extra instanceof Number) {
                Map<String, String> map = new HashMap<>();
                int val = ((Number) extra).intValue();
                if (val == 1) {
                    if (StrUtil.equals("0", parentKey)) {
                        map.put("icon", "organization");
                    } else {
                        map.put("icon", "area");
                    }
                    return map;
                } else if (val == 2) {
                    map.put("icon", "unit");
                    return map;
                } else if (val == 3) {
                    map.put("icon", "user");
                    return map;
                }
            }
        }
        return null;
    }
}

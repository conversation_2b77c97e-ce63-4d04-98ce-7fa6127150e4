package com.zjhh.comm.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 包含表头的VO
 *
 * <AUTHOR>
 * @date 2021-09-22 10:15 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ColumnResultVO extends BaseVo {
    private static final long serialVersionUID = 3954808545659842520L;

    private List<ColumnVO> columns;

    private List<Map<String, Object>> tables;

}

package com.zjhh.comm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @since 2021/11/19 11:48
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingleSelectVo extends BaseVo {

    private static final long serialVersionUID = -8481360889437997860L;

    @Schema(description = "编码")
    private Object code;

    @Schema(description = "名称")
    private String title;

    @Schema(description = "值")
    private Object value;
}

package com.zjhh.comm.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/24 14:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LicenceDataVo extends BaseVo {

    private static final long serialVersionUID = -7453457804446211002L;

    private String companyName;

    private String startDate;

    private String expireDate;

    private Integer cipherNumber;

    private List<LicenceNetworkVo> networks;
}

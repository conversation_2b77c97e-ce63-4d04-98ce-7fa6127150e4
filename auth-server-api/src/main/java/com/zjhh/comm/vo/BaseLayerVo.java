package com.zjhh.comm.vo;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/6
 */
@Data
public class BaseLayerVo<T extends BaseLayerVo<T>> implements Serializable{
    private static final long serialVersionUID = -7398825020882341348L;

    @ExcelIgnore
    private String uuid;

    @ExcelIgnore
    private Integer isLeaf;

    @ExcelIgnore
    private Boolean hasChild;

    @ExcelIgnore
    private List<T> children;

    public String getUuid() {
        return IdUtil.fastSimpleUUID();
    }
}

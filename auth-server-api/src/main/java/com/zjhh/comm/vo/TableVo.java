package com.zjhh.comm.vo;

import com.zjhh.comm.dto.ColumnDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/22 15:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TableVo<T> extends BaseVo {

    private static final long serialVersionUID = -3476431874298266757L;

    private List<ColumnDto> column;

    private List<T> table;
}

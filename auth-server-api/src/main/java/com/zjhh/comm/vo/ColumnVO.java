package com.zjhh.comm.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 表头VO
 *
 * <AUTHOR>
 * @date 2021-07-15 6:31 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ColumnVO extends BaseVo {

    public ColumnVO(){

    }
    public ColumnVO(String title, String dataIndex) {
        this.title = title;
        this.dataIndex = dataIndex;
    }

    private static final long serialVersionUID = 4557929542951240121L;

    private String dataIndex;

    private String title;

    private Integer px;

    //行转列最底层节点加宽度
    private Integer width;

    private List<ColumnVO> children;

}

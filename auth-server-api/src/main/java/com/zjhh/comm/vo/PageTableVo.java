package com.zjhh.comm.vo;

import com.zjhh.comm.dto.ColumnDto;
import com.zjhh.db.comm.Page;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/25 20:37
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PageTableVo<T> extends BaseVo {

    private static final long serialVersionUID = 5736064793621068137L;


    private List<ColumnDto> column;

    private Page<T> table;
}

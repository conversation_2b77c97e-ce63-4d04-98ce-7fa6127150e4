package com.zjhh.comm.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/5/26 14:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LicenceUser implements Serializable {

    private static final long serialVersionUID = -5746930406549744407L;

    /**
     * 行政区划
     */
    private String area;

    /**
     * 邮政编码
     */
    private String zipCode;

    /**
     * 用户名称
     */
    private String companyName;

    /**
     * 联系人
     */
    private String linkName;

    /**
     * 电话
     */
    private String mobile;

    /**
     * 地址
     */
    private String address;

    /**
     * email
     */
    private String email;
}

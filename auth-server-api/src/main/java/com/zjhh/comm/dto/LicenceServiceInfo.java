package com.zjhh.comm.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/5/26 14:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LicenceServiceInfo implements Serializable {

    private static final long serialVersionUID = 2361508808546454538L;

    /**
     * 名称
     */
    private String serviceName;

    /**
     * 联系人
     */
    private String linkName;

    /**
     * 电话
     */
    private String mobile;

    /**
     * 地址
     */
    private String address;

    /**
     * email
     */
    private String email;
}

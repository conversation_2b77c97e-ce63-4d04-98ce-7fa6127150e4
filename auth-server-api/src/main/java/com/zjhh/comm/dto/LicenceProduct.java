package com.zjhh.comm.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/5/26 14:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LicenceProduct implements Serializable {

    private static final long serialVersionUID = 1872281409059940964L;

    /**
     * 注册时间
     */
    private String issueDate;

    /**
     * 开始时间
     */
    private String beginDate;

    /**
     * 有效期
     */
    private String expireDate;
}

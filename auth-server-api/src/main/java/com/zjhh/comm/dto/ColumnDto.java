package com.zjhh.comm.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/22 15:01
 */
@Data
@Builder
public class ColumnDto implements Serializable {

    private static final long serialVersionUID = 2795363768053365781L;

    private String field;

    private String title;

    private List<ColumnDto> children;

    public ColumnDto() {
    }

    public ColumnDto(String field, String title) {
        this.field = field;
        this.title = title;
    }

    public ColumnDto(String field, String title, List<ColumnDto> children) {
        this.field = field;
        this.title = title;
        this.children = children;
    }
}

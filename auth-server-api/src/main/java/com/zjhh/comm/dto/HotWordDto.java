package com.zjhh.comm.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/7/22 09:25
 */
@Data
public class HotWordDto implements Serializable {

    private static final long serialVersionUID = 730519998250382847L;
    @Schema(description = "关键词")
    private String word;

    @Schema(description = "维度")
    private Integer weight;

    public HotWordDto(String word, Integer weight) {
        this.word = word;
        this.weight = weight;
    }
}

package com.zjhh.comm.constant;

import cn.hutool.core.io.FileUtil;

import java.io.File;

/**
 * <AUTHOR>
 * @since 2020/6/12 11:37
 */
public class CommConstants {

    /**
     * 成功的返回码
     */
    public static final Integer CODE_SUCCESS = 200;

    /**
     * 通用失败的返回码（友好提示）
     */
    public static final Integer CODE_SERVER_ERROR = 400;

    /**
     * 登录验证
     */
    public static final Integer CODE_NEED_LOGIN = 401;

    /**
     * 系统错误
     */
    public static final Integer CODE_SYSTEM_ERROR = 500;

    /**
     * 参数校验失败
     */
    public static final Integer CODE_ARGUMENT_NOT_VALID = 501;


    public static final String ADD_ERROR = "新增失败！";

    public static final String UPDATE_ERROR = "修改失败！";

    public static final String DELETE_ERROR = "删除失败！";

    public static final String OPERATE_ERROR = "操作失败！";

    public static final String USER_SESSION = "userSession";

    public static final String USER_PERMISSION = "userPermission";

    public static final String LICENCE_PATH = FileUtil.getUserHomePath() + File.separator;

    public static final String LICENCE_XML = "zjhh_licence.xml";

    public static final String TEMP_XML = "temp.xml";

    public static final String LICENCE_APPLY = "licence.id";

    /**
     * 文件存储请求url
     */
    public static final String FILE_URL = "file";
}

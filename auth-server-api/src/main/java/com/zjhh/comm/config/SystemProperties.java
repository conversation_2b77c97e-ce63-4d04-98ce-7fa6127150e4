package com.zjhh.comm.config;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/4/10 14:18
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "system")
public class SystemProperties {

    private String system;

    private ZwddAuth zwddAuth;

    private StThirdLogin stThirdLogin;

    /**
     * 不需要验证证书
     */
    private Boolean noCheckLicence;

    public Boolean isPc() {
        return StrUtil.equals("pc", system);
    }

    public Boolean isMobile() {
        return StrUtil.equals("mobile", system);
    }

    @Data
    public static class ZwddAuth implements Serializable {

        private static final long serialVersionUID = 8517214435767747437L;

        private String appId;

        private String appKey;

        private String appSecret;

        private String scanAppKey;

        private String scanAppSecret;

        private String protocal;

        private String tenantId;

        private String domainName;

        private String zzdUrl;
    }

    @Data
    public static class StThirdLogin implements Serializable {

        private static final long serialVersionUID = 2053268044370220323L;

        private String appKey;

        private String appSecret;
    }
}

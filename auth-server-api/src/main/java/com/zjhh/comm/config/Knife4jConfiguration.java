package com.zjhh.comm.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2020/10/31 09:33
 */
@Configuration
public class Knife4jConfiguration {

    private static final String API_INFO_TITLE = "通用接口文档";
    private static final String API_INFO_VERSION = "V1.0";
    private static final String API_INFO_DESCRIPTION = "Api接口列表";

    // 2024集同接口
    @Bean
    public GroupedOpenApi api4() {
        return GroupedOpenApi.builder()
                .group("base-platform")
                .displayName("V1.0")
                .packagesToScan("com.zjhh")
                .build();
    }


    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title(API_INFO_TITLE)
                        .description(API_INFO_DESCRIPTION)
                        .version(API_INFO_VERSION)
                        .contact(new Contact().name("yejun").email("<EMAIL>"))
                );
    }
}

package com.zjhh.comm.exception;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020/6/8 16:04
 */
@Data
public class BaseException extends RuntimeException {

    private static final long serialVersionUID = 789450839249301777L;

    private Integer code;

    public BaseException(Integer code) {
        this.code = code;
    }

    public BaseException(String message, Integer code) {
        super(message);
        this.code = code;
    }

    public BaseException(String message, Throwable cause, Integer code) {
        super(message, cause);
        this.code = code;
    }

    public BaseException(Throwable cause, Integer code) {
        super(cause);
        this.code = code;
    }

    public BaseException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace, Integer code) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.code = code;
    }
}

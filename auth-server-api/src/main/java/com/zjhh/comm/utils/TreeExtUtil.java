package com.zjhh.comm.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.func.Func1;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 树形工具类的扩展
 *
 * <AUTHOR>
 * @date 2024/7/6
 */
@Slf4j
public class TreeExtUtil {

    /**
     * 过滤不合理的数据并返回，不合理的数据有
     * 1.null
     * 2.code字段为null
     *
     * @param sourceList    未树化的源数据列表
     * @param codeMethodRef code字段对应的get方法引用
     * @param <T>           业务实体类类型
     * @param <R>           code和parentCode对应的类型
     * @return 过滤后的数据
     * @throws Exception
     */
    public static <T, R> List<T> getTargetList(List<T> sourceList, Func1<T, R> codeMethodRef) throws Exception {
        if (CollUtil.isEmpty(sourceList)) {
            return new ArrayList<>();
        }
        List<T> targetList = new ArrayList<>();
        for (T t : sourceList) {
            if (t != null && codeMethodRef.call(t) != null) {
                targetList.add(t);
            }
        }
        return targetList;
    }

    /**
     * 过滤不合理的数据并返回，不合理的数据有
     * 1.null
     * 2.code对应的值为null
     *
     * @param dataList 源数据列表
     * @param codeKey  code对应的key
     * @return 过滤后的数据
     * @throws Exception
     */
    public static List<Map<String, Object>> getTargetList(List<? extends Map<String, Object>> dataList, String codeKey) throws Exception {
        if (CollUtil.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> targetList = new ArrayList<>();
        for (Map<String, Object> rowMap : dataList) {
            if (rowMap != null && rowMap.get(codeKey) != null) {
                targetList.add(rowMap);
            }
        }
        return targetList;
    }


    /**
     * 获取根节点的parentCode
     *
     * @param targetList      完成过滤后的数据
     * @param codeMethodRef   code字段对应的get方法引用
     * @param parentMethodRef parentCode字段对应的get方法引用
     * @param <T>             业务实体类类型
     * @param <R>             code和parentCode对应的类型
     * @return 跟节点的parentCode值
     * @throws Exception
     */
    public static <T, R> R getRootValue(List<T> targetList, Func1<T, R> codeMethodRef, Func1<T, R> parentMethodRef) throws Exception {
        if (CollUtil.isEmpty(targetList)) {
            log.warn("数据为空");
            throw new Exception("Can't find root");
        }
        Set<R> codeSet = new HashSet<>();
        Set<R> parentCodeSet = new HashSet<>();
        for (T t : targetList) {
            codeSet.add(codeMethodRef.call(t));
            parentCodeSet.add(parentMethodRef.call(t));
        }
        int count = 0;
        R result = null;
        for (R parentCode : parentCodeSet) {
            if (!codeSet.contains(parentCode)) {
                count++;
                result = parentCode;
            }
        }
        if (count == 1) {
            return result;
        } else {
            throw new Exception("Can't find root");
        }
    }

    /**
     * 获取根节点的parentCode
     *
     * @param targetList    过滤后的数据
     * @param codeKey       code对应的key
     * @param parentCodeKey parentCode对应的key
     * @return 跟节点的parentCode值
     * @throws Exception
     */
    public static Object getRootValue(List<? extends Map<String, Object>> targetList, String codeKey, String parentCodeKey) throws Exception {
        if (CollUtil.isEmpty(targetList)) {
            log.warn("数据为空");
            throw new Exception("Can't find root");
        }
        Set<Object> codeSet = new HashSet<>();
        Set<Object> parentCodeSet = new HashSet<>();
        for (Map<String, Object> rowMap : targetList) {
            codeSet.add(rowMap.get(codeKey));
            parentCodeSet.add(rowMap.get(parentCodeKey));
        }
        int count = 0;
        Object result = null;
        for (Object parentCode : parentCodeSet) {
            if (!codeSet.contains(parentCode)) {
                count++;
                result = parentCode;
            }
        }
        if (count == 1) {
            return result;
        } else {
            throw new Exception("Can't find root");
        }
    }
}

package com.zjhh.comm.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.user.vo.TreeVo;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/9 17:10
 */
public class TreeUtils {

    /**
     * 列表转树结构
     *
     * @param list
     * @param parentKey
     * @return
     */
    public static List<TreeSelectVo> listToTree(List<TreeSelectVo> list, String parentKey) {
        List<TreeSelectVo> res = list.stream().filter(item -> StrUtil.equals(parentKey, item.getParentKey())).collect(Collectors.toList());
        if (CollUtil.isEmpty(res)) {
            return res;
        }
        res.forEach(item -> item.setChildren(listToTree(list, item.getKey())));
        return res;
    }

    public static List<TreeVo> menuTree(List<TreeVo> list, String parentKey) {
        List<TreeVo> res = list.stream().filter(item -> StrUtil.equals(parentKey, item.getParentKey())).collect(Collectors.toList());
        if (CollUtil.isEmpty(res)) {
            return res;
        }
        res.forEach(item -> item.setChildren(menuTree(list, item.getKey())));
        return res;
    }

    public static void getAllTreeNode(List<TreeSelectVo> list, List<String> selectedKeys, List<String> selectedTitles) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        list.forEach(item -> {
            if (CollUtil.isNotEmpty(item.getChildren())) {
                getAllTreeNode(item.getChildren(), selectedKeys, selectedTitles);
            } else {
                selectedKeys.add(item.getKey());
                selectedTitles.add(item.getTitle());
            }
        });
    }

    public static void getCheckTreeNode(List<TreeSelectVo> list, List<String> selectedKeys, List<String> selectedTitles, List<String> checkKeys) {
        if (!CollUtil.isEmpty(list)) {
            list.forEach((item) -> {
                if (checkKeys.contains(item.getKey())) {
                    getAllTreeNode(item.getChildren(), selectedKeys, selectedTitles);
                } else if (CollUtil.isNotEmpty(item.getChildren())) {
                    getCheckTreeNode(item.getChildren(), selectedKeys, selectedTitles, checkKeys);
                }

            });
        }
    }
}

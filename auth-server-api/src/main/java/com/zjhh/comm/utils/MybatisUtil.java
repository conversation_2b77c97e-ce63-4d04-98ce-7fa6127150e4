package com.zjhh.comm.utils;

import cn.hutool.core.collection.CollUtil;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/15
 */
@Component
public class MybatisUtil {

    /**
     * 默认单次批量插入数据条数
     */
    private static final int BATCH_COUNT = 100;

    /**
     * 批量插入分批次，默认每次100条
     *
     * @param list      需要插入的源数据
     * @param mapper    需要用到的mapper
     * @param <T>       实体类型
     */
    public <T> void insertBatch(List<T> list, CustomerBaseMapper<T> mapper) {
        insertBatch(list, mapper, BATCH_COUNT);
    }

    /**
     * 批量插入分批次
     *
     * @param list          需要插入的源数据
     * @param mapper        需要用到的mapper
     * @param batchCount    每批次插入条数
     * @param <T>           实体类型
     */
    public <T> void insertBatch(List<T> list, CustomerBaseMapper<T> mapper, int batchCount) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<T> batchList = new ArrayList<>();
        for (T t : list) {
            batchList.add(t);
            if (batchList.size() == batchCount) {
                mapper.insertBatchSomeColumn(batchList);
                batchList.clear();
            }
        }
        if (CollUtil.isNotEmpty(batchList)) {
            mapper.insertBatchSomeColumn(batchList);
        }
    }
}

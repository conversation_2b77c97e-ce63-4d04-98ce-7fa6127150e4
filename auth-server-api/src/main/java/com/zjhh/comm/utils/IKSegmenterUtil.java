package com.zjhh.comm.utils;

import com.alibaba.fastjson2.JSONObject;
import com.zjhh.comm.dto.HotWordDto;
import org.wltea.analyzer.cfg.DefaultConfig;
import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/7/21 09:33
 */
public class IKSegmenterUtil {

    private final static DefaultConfig config = new DefaultConfig();

    static {
        config.setUseSmart(true);
    }

    public static List<HotWordDto> listHotWord(List<String> stringList, List<String> filterWords, Integer length) throws IOException {
        StringBuilder sb = new StringBuilder();
        stringList.forEach(str -> sb.append(str).append(";"));
        return listHotWord(sb.toString(), filterWords, length);
    }

    /**
     * 获取热词
     *
     * @param str         元数据
     * @param filterWords 过滤的热词列表
     * @param length      取前多少个
     * @return
     */
    public static List<HotWordDto> listHotWord(String str, List<String> filterWords, Integer length) throws IOException {
        str = removeStr(str);
        Map<String, Integer> map = new HashMap<>();
        IKSegmenter ikSegmenter = new IKSegmenter(new StringReader(str), config);
        Lexeme lexeme;
        while ((lexeme = ikSegmenter.next()) != null) {
            if (lexeme.getLexemeText().length() > 1) {
                final String term = lexeme.getLexemeText();
                map.compute(term, (k, v) -> {
                    if (v == null) {
                        v = 1;
                    } else {
                        v += 1;
                    }
                    return v;
                });
            }
        }
        return map.entrySet().stream().sorted((o1, o2) -> o2.getValue().compareTo(o1.getValue()))
                .filter(e -> !filterWords.contains(e.getKey()))
                .limit(length)
                .map(e -> new HotWordDto(e.getKey(), e.getValue()))
                .collect(Collectors.toList());
    }

    private static String removeStr(String str) {
        return str.replaceAll("[a-zA-Z0-9]", "");
    }

    public static void main(String[] args) throws IOException {
        String str = "在平平淡淡的日常中，大家都经常接触到作文吧，作文是人们把记忆中所存储的有关知识、经验和思想用书面形式表达出来的记叙方式。相信写作文是一个让许多人都头痛的问题，下面是小编为大家整理的初三作文随机，仅供参考，大家一起来看看吧。初三作文随机　　喜欢这样的感觉。轻敲键盘，夜深人静。只是，我也是一个严重睡眠不足的人。班主任说，回家的话一定要好好地补觉，然而，我还是不听话的一个小孩。　　在学校，感觉学校做的调整已经好多了吧。从最初我们十一点睡觉改到十点半就睡觉，从最初的六点起床晨训到现在的六点半起床，感觉，我们已经是很幸福了。有些时候，幸福对于初三生来说，很简单。　　昨天，我们跑了两千多米。昨天下午要下去训练的时候，课间操的`时候，大家都提心吊胆的说：“腿都软了，还要再跑四圈啊！”但是后来下去后，体育老师说跑两圈，大家都说幸福来得太突然了。大家都害怕说等会会有其他的练习。是的，上次我们也以为是体育老师突然变慈善了，让我们跑两圈而不是四圈。结果后来要我们围着球场矮人走两圈。　　竟然是笑着打出来这些经历的。其实那些经历于我来说都是酸的。累的。但是，却也不知为何，此刻的我敲击着键盘，不会觉得很累，觉得，说出来，然而为自己的那些坚持感到自豪。　　今天同桌哭了。同桌经常看到我哭，感觉。他已经看过我两次眼泪哐啷郎地掉了。而他，我是第一次看到他掉眼泪。我以为他是因为成绩太差了，因为这几天他总是跟我说他好迷茫，我并没有安慰他，我只是说我也是有种没有目的地的迷茫。我不知道我该何去何从，只知道，我该把眼下的做好了，否则我会生不如死。　　后来同桌跟我说了他哭的原因，终究还是因为太过于重感情。　　刚才看文章的时候，看到这么一篇文章，大概意思说的是聪明的女人就不该因为情感掉眼泪。　　或许吧，或许我们都不是聪明的人。我们终究的，只是别人眼中的无能的女人，好掉眼泪，没有理智的女人。我们总是误以为自己的情商很高，后来才知道，掉眼泪不过是情商低的表现！";
        List<String> filterWords = new ArrayList<>();
        filterWords.add("女人");
        filterWords.add("作文");
        System.out.println(JSONObject.toJSONString(listHotWord(str, filterWords, 10)));
    }
}

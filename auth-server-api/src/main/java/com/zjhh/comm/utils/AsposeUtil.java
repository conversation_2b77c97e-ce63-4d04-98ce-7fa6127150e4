package com.zjhh.comm.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aspose.cells.Cells;
import com.aspose.cells.PdfSaveOptions;
import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;
import com.aspose.slides.Presentation;
import com.aspose.words.*;
import com.zjhh.comm.exception.BizException;
import org.ofdrw.converter.ConvertHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/7/1 15:45
 */
public class AsposeUtil {

    private static final Logger log = LoggerFactory.getLogger(AsposeUtil.class);

    static {
        ClassLoader contextClassLoader = AsposeUtil.class.getClassLoader();
        try (InputStream wordIs = contextClassLoader.getResourceAsStream("license.xml");
             InputStream excelIs = contextClassLoader.getResourceAsStream("license.xml");
             InputStream ppdIs = contextClassLoader.getResourceAsStream("license.xml");
             InputStream fonts = contextClassLoader.getResourceAsStream("fonts/simsun.ttc");
        ) {
            com.aspose.cells.License excelAposeLic = new com.aspose.cells.License();
            excelAposeLic.setLicense(excelIs);

            License wordAposeLic = new License();
            wordAposeLic.setLicense(wordIs);

            byte[] fontsByte = IoUtil.readBytes(fonts);
            FontSourceBase[] fontSourceBases = new FontSourceBase[1];
            fontSourceBases[0] = new MemoryFontSource(fontsByte);
            com.aspose.words.FontSettings.getDefaultInstance().setFontsSources(fontSourceBases);
            com.aspose.slides.License pptLicense = new com.aspose.slides.License();
            pptLicense.setLicense(ppdIs);
        } catch (Exception e) {
            log.error("license加载出错", e);
        }
    }

    /**
     * 防止别的地方创建文档时候未初始化该类，导致License未加载
     */
    public static void init() {

    }

    /**
     * word文本替换
     *
     * @param is
     * @param savePath 保存的路径
     * @param map
     */
    public static void replaceWord(InputStream is, String savePath, Map<String, String> map) {
        try {
            Document doc = new Document(is);
            Range range = doc.getRange();
            for (String key : map.keySet()) {
                String replaceKey = "{{" + key + "}}";
                range.replace(replaceKey, map.get(key), true, false);
            }
            doc.save(savePath, SaveFormat.DOC);
        } catch (Exception e) {
            log.error("word替换出错", e);
            throw new BizException("word操作出错！");
        } finally {
            IoUtil.close(is);
        }
    }

    public static void convertToPdf(String suffix, String inPath, File pdfFile) {
        switch (suffix) {
            case "doc":
            case "docx":
                doc2Pdf(inPath, pdfFile);
                return;
            case "xls":
            case "xlsx":
                excel2Pdf(inPath, pdfFile);
                return;
            case "ppt":
            case "pptx":
                ppt2Pdf(inPath, pdfFile);
                return;
            case "txt":
                txtToPdf(inPath, pdfFile);
                return;
            case "ofd":
                ConvertHelper.ofd2pdf(inPath, pdfFile);
        }
    }

    public static void txtToPdf(String inPath, File pdfFile) {
        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
            String txt = FileUtil.readUtf8String(inPath);
            Document doc = new Document();
            DocumentBuilder builder = new DocumentBuilder(doc);
            builder.getPageSetup().setPaperSize(PaperSize.A4);
            builder.getPageSetup().setOrientation(Orientation.PORTRAIT);
            builder.getPageSetup().setVerticalAlignment(PageVerticalAlignment.TOP);
            builder.getPageSetup().setLeftMargin(100);
            builder.getPageSetup().setRightMargin(100);
            builder.write(txt);
            doc.save(fos, SaveFormat.PDF);
        } catch (Exception e) {
            log.error("doc2Pdf", e);
        }

    }

    /**
     * word转pdf
     *
     * @param inPath
     * @param pdfFile
     */
    private static void doc2Pdf(String inPath, File pdfFile) {
        log.info("文件开始转换");
        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
            Document doc = new Document(inPath);
            TableCollection tables = doc.getFirstSection().getBody().getTables();
            if (ObjectUtil.isNotNull(tables)) {
                for (Table table : tables) {
                    RowCollection rows = table.getRows();
                    table.setAllowAutoFit(false);
                    for (Row row : rows) {
                        CellCollection cells = row.getCells();
                        for (Cell cell : cells) {
                            CellFormat cellFormat = cell.getCellFormat();
                            cellFormat.setFitText(false);
                            cellFormat.setWrapText(true);
                        }
                    }
                }
            }
            doc.save(fos, SaveFormat.PDF);
        } catch (Exception e) {
            log.error("doc2Pdf", e);
        }
    }

    /**
     * excel转pdf
     *
     * @param inPath
     * @param pdfFile
     */
    private static void excel2Pdf(String inPath, File pdfFile) {
        log.info("文件开始转换");
        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
            Workbook workbook = new Workbook(inPath);
            PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
            pdfSaveOptions.setOnePagePerSheet(true);
            int[] autoDrawSheets = {3};
            autoDraw(workbook, autoDrawSheets);
            workbook.save(fos, pdfSaveOptions);
        } catch (Exception e) {
            log.error("excel2Pdf", e);
        }
    }

    /**
     * ppt转pdf
     *
     * @param inPath
     * @param pdfFile
     */
    private static void ppt2Pdf(String inPath, File pdfFile) {
        log.info("文件开始转换");
        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
            Presentation present = new Presentation(inPath);
            present.save(fos, com.aspose.slides.SaveFormat.Pdf);
        } catch (Exception e) {
            log.error("doc2Pdf", e);
        }
    }

    /**
     * @param wb   设置打印的sheet自动拉伸比例
     * @param page 自动拉伸的页的sheet数组
     */
    private static void autoDraw(Workbook wb, int[] page) {
        if (null != page && page.length > 0) {
            for (int i = 0; i < page.length; i++) {
                wb.getWorksheets().get(i).getHorizontalPageBreaks().clear();

                wb.getWorksheets().get(i).getVerticalPageBreaks().clear();
            }
        }
    }

    public static void main(String[] args) throws Exception {
        Workbook workbook = new Workbook("/Users/<USER>/Downloads/111.xlsx");
        Worksheet worksheet = workbook.getWorksheets().get(0); // 获取第一个工作表

        // 获取原始数据区域
        Cells cells = worksheet.getCells();
        int maxRow = cells.getMaxDataRow(); // 数据的最大行索引
        int maxCol = cells.getMaxDataColumn(); // 数据的最大列索引

        // 创建一个新的工作表来存储转置后的数据
        Worksheet transposeSheet = workbook.getWorksheets().add("Transposed");
        Cells transposeCells = transposeSheet.getCells();

        // 行转列，列转行
        for (int row = 0; row <= maxRow; row++) {
            for (int col = 0; col <= maxCol; col++) {
                // 获取原数据并写入转置位置
                com.aspose.cells.Cell sourceCell = cells.get(row, col);
                transposeCells.get(col, row).setValue(sourceCell.getValue());
            }
        }

        workbook.getWorksheets().removeAt(0);
        // 保存文件
        workbook.save("/Users/<USER>/Downloads/222.xlsx");
        System.out.println("数据已成功转置并保存！");
    }
}

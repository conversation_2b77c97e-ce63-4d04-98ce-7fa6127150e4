package com.zjhh.comm.utils;

import cn.hutool.core.collection.CollUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/29 11:22
 */
public class DataPercentageUtil {

    private static void calculateInitialPercentage(List<DataDto> dataDtos) {
        List<BigDecimal> data = DataDto.listCalData(dataDtos);
        BigDecimal sum = data.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        dataDtos.forEach(dto -> {
            BigDecimal zb = dto.getCalData().divide(sum, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            dto.setZb(zb);
            dto.setResData(zb);
        });
    }

    /**
     * 当前元素数据dataDtos没有进行0值过滤
     *
     * @param dataDtos  元素数据
     * @param minValue  饼图占比的最小值
     * @param absFlag   取用策略。true表示负数取绝对值；false表示最小负数取0，其它数加上最小负数的绝对值
     */
    public static void calculateAdjustedPercentage(List<DataDto> dataDtos, BigDecimal minValue, boolean absFlag) {
        if (CollUtil.isEmpty(dataDtos)) {
            return;
        }
        //处理可能出现的负数
        if (absFlag) {
            //负数取绝对值
            dataDtos.forEach(dataDto -> dataDto.setCalData(dataDto.getCalData().abs()));
        } else {
            //负数变为0，其它数加上负数的绝对值
            BigDecimal actualMinValue = BigDecimal.ZERO;
            for (DataDto dataDto : dataDtos) {
                if (dataDto.getCalData().compareTo(actualMinValue) < 0) {
                    actualMinValue = dataDto.getCalData();
                }
            }
            if (actualMinValue.compareTo(BigDecimal.ZERO) < 0) {
                BigDecimal absoluteValue = actualMinValue.abs();
                for (DataDto dataDto : dataDtos) {
                    dataDto.setCalData(dataDto.getCalData().add(absoluteValue));
                }
            }
        }
        calculateInitialPercentage(dataDtos);
        adjustPercentages(dataDtos, minValue);
    }

    private static void adjustPercentages(List<DataDto> dataDtos, BigDecimal minValue) {
        boolean adjustmentNeeded = true;
        while (adjustmentNeeded) {
            adjustmentNeeded = false;
            List<Integer> belowFive = new ArrayList<>();
            List<Integer> aboveFive = new ArrayList<>();
            for (int i = 0; i < dataDtos.size(); i++) {
                if (dataDtos.get(i).getResData().compareTo(minValue) < 0) {
                    belowFive.add(i);
                } else if (dataDtos.get(i).getResData().compareTo(minValue) > 0) {
                    aboveFive.add(i);
                }
            }
            if (!belowFive.isEmpty()) {
                for (int index : belowFive) {
                    BigDecimal difference = minValue.subtract(dataDtos.get(index).getResData());
                    dataDtos.get(index).setResData(minValue);

                    BigDecimal amountToSubtract = difference.divide(new BigDecimal(aboveFive.size()), 4, RoundingMode.HALF_UP);
                    for (int indexAbove : aboveFive) {
                        dataDtos.get(indexAbove).setResData(dataDtos.get(indexAbove).getResData().subtract(amountToSubtract).setScale(2, RoundingMode.HALF_UP));
                        if (dataDtos.get(indexAbove).getResData().compareTo(minValue) < 0) {
                            adjustmentNeeded = true;
                        }
                    }
                }
            }
        }

    }

    @Data
    public static class DataDto implements Serializable {

        private static final long serialVersionUID = -1785549704807239670L;

        @Schema(description = "唯一标识")
        private String key;

        @Schema(description = "计算数据")
        private BigDecimal calData;

        @Schema(description = "结果数据")
        private BigDecimal resData;

        @Schema(description = "实际占比")
        private BigDecimal zb;

        public static List<BigDecimal> listCalData(List<DataDto> dataDtos) {
            List<BigDecimal> list = new ArrayList<>();
            dataDtos.forEach(dataDto -> list.add(dataDto.getCalData()));
            return list;
        }
    }
}

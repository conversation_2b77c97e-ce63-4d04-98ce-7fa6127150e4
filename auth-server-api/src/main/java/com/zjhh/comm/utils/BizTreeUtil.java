package com.zjhh.comm.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.zjhh.comm.vo.BaseLayerVo;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 用户钻取的实体类的树化
 * 要求isLeaf属性为0或1，且必须是Integer类型
 *
 * <AUTHOR>
 * @date 2024/7/5
 */
@Slf4j
public class BizTreeUtil {

    private static final String IS_LEAF = "isLeaf";

    private static final String HAS_CHILD = "hasChild";

    private static final String CHILDREN = "children";

    private static final String BASE_SPACE = "         ";

    /**
     * 树化业务数据，指定跟节点
     *
     * @param sourceList      未树化的源数据列表
     * @param codeMethodRef   code字段对应的get方法引用
     * @param parentMethodRef parentCode字段对应的get方法引用
     * @param rootValue       根节点的值
     * @param <T>             业务实体类类型
     * @param <R>             code和parentCode对应的类型
     * @return 树化后的数据
     */
    public static <T extends BaseLayerVo<T>, R> List<T> treeify(List<T> sourceList, Func1<T, R> codeMethodRef, Func1<T, R> parentMethodRef, R rootValue) {
        try {
            // 过滤不合理数据
            List<T> targetList = TreeExtUtil.getTargetList(sourceList, codeMethodRef);
            if (CollUtil.isEmpty(targetList)) {
                return new ArrayList<>();
            }
            Map<R, List<T>> map = new HashMap<>();
            for (T vo : targetList) {
                map.putIfAbsent(parentMethodRef.call(vo), new ArrayList<>());
                map.get(parentMethodRef.call(vo)).add(vo);
            }
            for (T t : targetList) {
                List<T> children = map.get(codeMethodRef.call(t));
                t.setHasChild(Objects.equals(0, t.getIsLeaf()));
                t.setChildren(children);
                // 非叶子节点但children为null时，表示当前查询的结果集没有查询出子节点，需要将children设置为空数据（使之可以钻取）
                if (Objects.equals(0, t.getIsLeaf()) && children == null) {
                    t.setChildren(new ArrayList<>());
                }
            }
            return map.get(rootValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 树化业务数据，自动寻找跟节点
     *
     * @param sourceList      未树化的源数据列表
     * @param codeMethodRef   code字段对应的get方法引用
     * @param parentMethodRef parentCode字段对应的get方法引用
     * @param <T>             业务实体类类型
     * @param <R>             code和parentCode对应的类型
     * @return 树化后的数据
     */
    public static <T extends BaseLayerVo<T>, R> List<T> treeify(List<T> sourceList, Func1<T, R> codeMethodRef, Func1<T, R> parentMethodRef) {
        try {
            List<T> targetList = TreeExtUtil.getTargetList(sourceList, codeMethodRef);
            if (CollUtil.isEmpty(targetList)) {
                return new ArrayList<>();
            }
            R rootValue = TreeExtUtil.getRootValue(targetList, codeMethodRef, parentMethodRef);
            return treeify(targetList, codeMethodRef, parentMethodRef, rootValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 树化业务数据，指定跟节点
     *
     * @param dataList      未树化的源数据列表
     * @param codeKey       code对应的key名称
     * @param parentCodeKey parentCode对应的key名称
     * @param rootValue     根节点的parentCode
     * @return 树化后的数据列表
     */
    public static List<Map<String, Object>> treeify(List<? extends Map<String, Object>> dataList, String codeKey, String parentCodeKey, Object rootValue) {
        try {
            List<Map<String, Object>> targetList = TreeExtUtil.getTargetList(dataList, codeKey);
            if (CollUtil.isEmpty(targetList)) {
                return new ArrayList<>();
            }
            Map<Object, List<Map<String, Object>>> map = new HashMap<>();
            for (Map<String, Object> rowMap : targetList) {
                map.putIfAbsent(rowMap.get(parentCodeKey), new ArrayList<>());
                map.get(rowMap.get(parentCodeKey)).add(rowMap);
            }
            for (Map<String, Object> rowMap : targetList) {
                List<Map<String, Object>> children = map.get(rowMap.get(codeKey));
                rowMap.put(CHILDREN, children);
                rowMap.put(HAS_CHILD, Objects.equals(0, rowMap.get(IS_LEAF)));
                // 非叶子节点但children为null时，表示当前查询的结果集没有查询出子节点，需要将children设置为空数据（使之可以钻取）
                if (Objects.equals(0, rowMap.get(IS_LEAF)) && children == null) {
                    rowMap.put(CHILDREN, new ArrayList<>());
                }
            }
            return map.get(rootValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 树化业务数据，自动寻找跟节点
     *
     * @param dataList      未树化的源数据列表
     * @param codeKey       code对应的key名称
     * @param parentCodeKey parentCode对应的key名称
     * @return 树化后的数据列表
     */
    public static List<Map<String, Object>> treeify(List<? extends Map<String, Object>> dataList, String codeKey, String parentCodeKey) {
        try {
            List<Map<String, Object>> targetList = TreeExtUtil.getTargetList(dataList, codeKey);
            if (CollUtil.isEmpty(targetList)) {
                return new ArrayList<>();
            }
            Object rootValue = TreeExtUtil.getRootValue(targetList, codeKey, parentCodeKey);
            return treeify(targetList, codeKey, parentCodeKey, rootValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 已树化的数据，转为excel加空格前缀的数据
     *
     * @param treeifiedList 以树化的数据列表
     * @param nameMethodRef 加前缀的字段get方法引用
     * @param <T>           业务实体类型
     * @return              excel加空格前缀的数据
     */
    public static <T extends BaseLayerVo<T>> List<T> toExcelData(List<T> treeifiedList, Func1<T, String> nameMethodRef) {
        return toExcelData(treeifiedList, nameMethodRef, "");
    }

    /**
     * 已树化的数据，转为excel加空格前缀的数据
     * 默认第一层不加空格，每层多BASE_SPACE空格
     *
     * @param treeifiedList 以树化的数据列表
     * @param nameMethodRef 加前缀的字段get方法引用
     * @param curSpace      当前层级需要加的前缀空格
     * @param <T>           业务实体类型
     * @return              excel加空格前缀的数据
     */
    private static <T extends BaseLayerVo<T>> List<T> toExcelData(List<T> treeifiedList, Func1<T, String> nameMethodRef, String curSpace) {
        try {
            if (CollUtil.isEmpty(treeifiedList)) {
                return new ArrayList<>();
            }
            List<T> resultList = new ArrayList<>();
            String nameFieldName = LambdaUtil.getFieldName(nameMethodRef);
            for (T t : treeifiedList) {
                List<T> children = t.getChildren();
                // 这里children属性设置为null，避免非叶子节点children还是有子节点数据
                t.setChildren(null);
                String originName = nameMethodRef.call(t);
                ReflectUtil.setFieldValue(t, nameFieldName, curSpace + (originName == null ? "" : originName));
                resultList.add(t);
                if (BooleanUtil.isTrue(t.getHasChild())) {
                    // 考虑特殊问题，isLeaf是1，但查询出来下级还是有数据（较为少见）。该情况下不再考虑子节点
                    resultList.addAll(toExcelData(children, nameMethodRef, curSpace + BASE_SPACE));
                }
            }
            return resultList;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 已树化的数据，转为excel加空格前缀的数据
     * 默认第一层不加空格，每层多BASE_SPACE空格
     *
     * @param treeifiedList  已树化的数据列表
     * @param nameKey   加前缀的字段key
     * @return          excel加空格前缀的数据
     */
    public static List<Map<String, Object>> toExcelData(List<? extends Map<String, Object>> treeifiedList, String nameKey) {
        return toExcelData(treeifiedList, nameKey, "");
    }

    /**
     * 已树化的数据，转为excel加空格前缀的数据
     * 默认第一层不加空格，每层多BASE_SPACE空格
     *
     * @param treeifiedList  已树化的数据列表
     * @param nameKey   加前缀的字段key
     * @param curSpace  当前层级需要加的前缀空格
     * @return          excel加空格前缀的数据
     */
    private static List<Map<String, Object>> toExcelData(List<? extends Map<String, Object>> treeifiedList, String nameKey, String curSpace) {
        try {
            if (CollUtil.isEmpty(treeifiedList)) {
                return new ArrayList<>();
            }
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (Map<String, Object> rowMap : treeifiedList) {
                Object children = rowMap.get(CHILDREN);
                Object hasChild = rowMap.get(HAS_CHILD);
                rowMap.put(CHILDREN, null);
                Object originName = rowMap.get(nameKey);
                rowMap.put(nameKey, curSpace + (originName == null ? "" : originName));
                resultList.add(rowMap);
                if (hasChild instanceof Boolean && BooleanUtil.isTrue((Boolean) hasChild)) {
                    if (children instanceof List) {
                        // 确保树化后的children是存的Map<String, Object>类型数据。调用该类的树化方法可保证。
                        resultList.addAll(toExcelData((List<? extends Map<String, Object>>) children, nameKey, curSpace + BASE_SPACE));
                    }
                }
            }
            return resultList;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

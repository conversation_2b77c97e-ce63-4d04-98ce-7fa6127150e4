package com.zjhh.comm.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.core.util.ReflectUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 注意：该类的方法，除去了code为null的数据。
 * 该类提供了非树化的源数据name字段加前缀空格的方法，对于已树化的数据，则可以直接遍历加前缀更加方便
 *
 * <AUTHOR>
 * @date 2024/7/5
 */
@Slf4j
public class ExcelExtUtil {

    /**
     * 带层级的树
     */
    @Data
    private static class LevelTree<T> {
        public LevelTree(T code, T parentCode) {
            this.code = code;
            this.parentCode = parentCode;
        }

        private T code;

        private T parentCode;

        private List<LevelTree<T>> children;
    }

    /**
     * @param sourceList      源数据列表
     * @param codeMethodRef   code字段对应的get方法引用
     * @param parentMethodRef parentCode字段对应的get方法引用
     * @param nameMethodRef   name字段对应的get方法引用（即需要加前缀空格的字段）
     * @param rootValue       跟节点对应的parentCode值
     * @param <T>             业务实体类类型
     * @param <R>             code和parentCode的类型
     * @return name字段加前缀后的列表
     */
    public static <T, R> List<T> addPreSpaceForName(List<T> sourceList, Func1<T, R> codeMethodRef, Func1<T, R> parentMethodRef, Func1<T, String> nameMethodRef, R rootValue) {
        try {
            if (CollUtil.isEmpty(sourceList)) {
                return new ArrayList<>();
            }
            List<T> targetList = new ArrayList<>();
            List<LevelTree<R>> treeList = new ArrayList<>();
            for (T t : sourceList) {
                if (t != null && codeMethodRef.call(t) != null) {
                    targetList.add(t);
                    LevelTree<R> levelTree = new LevelTree<>(codeMethodRef.call(t), parentMethodRef.call(t));
                    treeList.add(levelTree);
                }
            }
            List<LevelTree<R>> treefiedList = treeify(treeList, rootValue);
            Map<R, String> spaceMap = new HashMap<>();
            dfs(treefiedList, spaceMap);
            String fieldName = LambdaUtil.getFieldName(nameMethodRef);
            for (T t : targetList) {
                String originName = nameMethodRef.call(t);
                ReflectUtil.setFieldValue(t, fieldName, spaceMap.get(codeMethodRef.call(t)) + (originName == null ? "" : originName));
            }
            return targetList;
        } catch (Exception e) {
            log.error("层级转换错误：", e);
            throw new RuntimeException("导出错误，请联系管理员");
        }
    }

    public static <T, R> List<T> addPreSpaceForName(List<T> sourceList, Func1<T, R> codeMethodRef, Func1<T, R> parentMethodRef, Func1<T, String> nameMethodRef) {
        try {
            if (CollUtil.isEmpty(sourceList)) {
                return new ArrayList<>();
            }
            List<T> targetList = TreeExtUtil.getTargetList(sourceList, codeMethodRef);
            if (CollUtil.isEmpty(targetList)) {
                return new ArrayList<>();
            }
            R rootValue = TreeExtUtil.getRootValue(targetList, codeMethodRef, parentMethodRef);
            return addPreSpaceForName(targetList, codeMethodRef, parentMethodRef, nameMethodRef, rootValue);
        } catch (Exception e) {
            log.error("层级转换错误：", e);
            throw new RuntimeException("导出错误，请联系管理员");
        }
    }


    /**
     * 可用于行转列形式的层级数据转换.
     *
     * @param dataList      行数据列表
     * @param codeKey       code对应的key
     * @param parentCodeKey parentCode对应的key
     * @param nameKey       name对应的key
     * @param rootValue     跟节点的parentCode对应的值
     * @return 加了前缀空格的行数据列表
     */
    public static List<Map<String, Object>> addPreSpaceForName(List<? extends Map<String, Object>> dataList, String codeKey, String parentCodeKey, String nameKey, Object rootValue) {
        try {
            if (CollUtil.isEmpty(dataList)) {
                return new ArrayList<>();
            }
            List<Map<String, Object>> targetList = new ArrayList<>();
            List<LevelTree<Object>> treeList = new ArrayList<>();
            for (Map<String, Object> rowMap : dataList) {
                if (rowMap != null && rowMap.get(codeKey) != null) {
                    LevelTree<Object> levelTree = new LevelTree<>(rowMap.get(codeKey), rowMap.get(parentCodeKey));
                    treeList.add(levelTree);
                    targetList.add(rowMap);
                }
            }
            if (CollUtil.isEmpty(targetList)) {
                return new ArrayList<>();
            }
            List<LevelTree<Object>> treefiedList = treeify(treeList, rootValue);
            Map<Object, String> spaceMap = new HashMap<>();
            dfs(treefiedList, spaceMap);
            for (Map<String, Object> rowMap : targetList) {
                Object originName = rowMap.get(nameKey);
                rowMap.put(nameKey, spaceMap.get(rowMap.get(codeKey)) + (originName == null ? "" : originName));
            }
            return targetList;
        } catch (Exception e) {
            log.error("层级转换错误：", e);
            throw new RuntimeException("导出错误，请联系管理员");
        }
    }

    /**
     * 可用于行转列形式的层级数据转换.
     *
     * @param dataList      行数据列表
     * @param codeKey       code对应的key
     * @param parentCodeKey parentCode对应的key
     * @param nameKey       name对应的key
     * @return 加了前缀空格的行数据列表
     */
    public static List<Map<String, Object>> addPreSpaceForName(List<? extends Map<String, Object>> dataList, String codeKey, String parentCodeKey, String nameKey) {
        try {
            if (CollUtil.isEmpty(dataList)) {
                return new ArrayList<>();
            }
            List<Map<String, Object>> targetList = TreeExtUtil.getTargetList(dataList, codeKey);
            if (CollUtil.isEmpty(targetList)) {
                return new ArrayList<>();
            }
            Object rootValue = TreeExtUtil.getRootValue(targetList, codeKey, parentCodeKey);
            return addPreSpaceForName(targetList, codeKey, parentCodeKey, nameKey, rootValue);
        } catch (Exception e) {
            log.error("层级转换错误：", e);
            throw new RuntimeException("导出错误，请联系管理员");
        }
    }


    /**
     * 树化
     *
     * @param treeList  中间层级实体列表
     * @param rootValue 跟节点对应的parentCode值
     * @param <R>       code和parentCode的类型
     * @return 树化后的中间层级实体列表
     */
    private static <R> List<LevelTree<R>> treeify(List<LevelTree<R>> treeList, R rootValue) {
        if (CollUtil.isEmpty(treeList)) {
            return new ArrayList<>();
        }
        Map<R, List<LevelTree<R>>> map = new HashMap<>();
        for (LevelTree<R> levelTree : treeList) {
            map.putIfAbsent(levelTree.getParentCode(), new ArrayList<>());
            map.get(levelTree.getParentCode()).add(levelTree);
        }
        for (LevelTree<R> levelTree : treeList) {
            levelTree.setChildren(map.get(levelTree.getCode()));
        }
        return map.get(rootValue);
    }

    /**
     * 获取前缀空格映射spaceMap
     *
     * @param treefiedList 树化后的中间实体列表
     * @param currentSpace 当前层级需要加的前缀字符串
     * @param baseSpace    每层需要多加的前缀字符串
     * @param spaceMap     key为code，value为需要加的前缀空格的map
     * @param <R>          code和parentCode的类型
     */
    private static <R> void dfs(List<LevelTree<R>> treefiedList, String currentSpace, String baseSpace, Map<R, String> spaceMap) {
        if (CollUtil.isEmpty(treefiedList)) {
            return;
        }
        for (LevelTree<R> levelTree : treefiedList) {
            spaceMap.put(levelTree.getCode(), currentSpace);
            dfs(levelTree.getChildren(), currentSpace + baseSpace, baseSpace, spaceMap);
        }
    }

    /**
     * 获取前缀空格映射spaceMap
     * 默认第一级没有前置空格，每层9个英文空格
     *
     * @param treefiedList 树化后的中间实体列表
     * @param spaceMap     key为code，value为需要加的前缀空格的map
     * @param <R>          code和parentCode的类型
     */
    private static <R> void dfs(List<LevelTree<R>> treefiedList, Map<R, String> spaceMap) {
        dfs(treefiedList, "", "         ", spaceMap);
    }
}

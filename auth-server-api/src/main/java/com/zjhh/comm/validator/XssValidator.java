package com.zjhh.comm.validator;

import cn.hutool.core.util.StrUtil;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Safelist;

/**
 * <AUTHOR>
 * @since 2024/9/14 上午9:08
 */
public class XssValidator implements ConstraintValidator<Xss, String> {

    private static final Safelist WHITE_LIST = Safelist.relaxed();

    /**
     * 定义输出设置，关闭prettyPrint（prettyPrint=false），目的是避免在清理过程中对代码进行格式化
     * 从而保持输入和输出内容的一致性。
     */
    private static final Document.OutputSettings OUTPUT_SETTINGS = new Document.OutputSettings().prettyPrint(false);

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {

        if (StrUtil.isBlank(value)) {
            return true;
        }

        // 使用Jsoup库对输入值进行清理，以移除潜在的XSS攻击脚本。
        // 使用预定义的白名单和输出设置来确保只保留安全的HTML元素和属性。
        String cleanedValue = Jsoup.clean(value, "", WHITE_LIST, OUTPUT_SETTINGS);

        // 比较清理后的值与原始值是否相同，用于判断输入值是否有效。
        return cleanedValue.equals(value);
    }

}

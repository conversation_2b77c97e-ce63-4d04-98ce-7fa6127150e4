package com.zjhh.comm.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;


@Target({ElementType.FIELD})
@Retention(RUNTIME)
@Constraint(validatedBy = PhoneOrNullValidator.class)
public @interface PhoneOrNull {

    String message() default "手机号不符合规则！";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}

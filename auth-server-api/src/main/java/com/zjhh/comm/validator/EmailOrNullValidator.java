package com.zjhh.comm.validator;

import cn.hutool.core.util.StrUtil;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.regex.Pattern;

public class EmailOrNullValidator implements ConstraintValidator<EmailOrNull, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (StrUtil.isNotBlank(value)) {
            return Pattern.matches("[A-Z0-9a-z_]+@[A-Z0-9a-z_]+(\\.[A-Z0-9a-z]+)+", value);
        }
        return true;
    }
}

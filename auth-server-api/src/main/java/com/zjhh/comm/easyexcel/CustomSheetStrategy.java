package com.zjhh.comm.easyexcel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.AbstractVerticalCellStyleStrategy;
import lombok.Getter;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/5/2
 */
@Getter
public class CustomSheetStrategy {

    /**
     * 核心对象，用于上、中、下三部分
     */
    private final ThreeTableVo threeTableVo;

    /**
     * 大标题所占行，
     * 若左下或右下角有数据时，第一个table占titleRows + 1,否则占titleRows行。
     */
    private Integer titleRows;

    /**
     * 与titleRows很强的关联性。
     */
    private Integer topRows;

    /**
     * 底部的说明占几行
     */
    private Integer bottomRows;

    /**
     * 中间核心部分的表头占几行
     */
    private Integer midHeadRows;

    /**
     * 中间核心部门的数据占几行
     */
    private Integer midDataRows;

    /**
     * 中间数据占几列。那么几个table就都占相同的列。
     */
    private Integer columns;

    /**
     * title的左右下角是否有元素
     *
     * 这里不使用包装类，避免出现null的情况
     */
    private Boolean hasCorner;

    /**
     * 通常使用这个构造方法就够了，头部和底部都占两行
     * 注意：由于使用了包装类，未设置时为null，需要直接初始化为0
     * @param threeTableVo
     */
    public CustomSheetStrategy(ThreeTableVo threeTableVo) {
        this.threeTableVo = threeTableVo;
        ThreeTableVo.MidContentTwoVo midContentTwoVo = threeTableVo.getMidContentTwoVo();
        List<?> dataList = midContentTwoVo.getDataList();
        midDataRows = dataList.size();
        //两个表头中有一个一定存在，获取列数和表头所占行数
        if (!Objects.isNull(midContentTwoVo.getClazzHead())) {
            int[] rowsAndColumns = EasyExcelUtil.getRowsAndColumns(midContentTwoVo.getClazzHead());
            midHeadRows = rowsAndColumns[0];
            columns = rowsAndColumns[1];
        } else if (CollUtil.isNotEmpty(midContentTwoVo.getHeadList())) {
            int[] rowsAndColumns = EasyExcelUtil.getRowsAndColumns(midContentTwoVo.getHeadList());
            midHeadRows = rowsAndColumns[0];
            columns = rowsAndColumns[1];
        }
        //目前设置了threeTableVo,midDataRows,midHeadRows,columns四个变量，其他变量初始化
        titleRows = 0;
        topRows = 0;
        hasCorner = false;
        bottomRows = 0;

        //如果下面有bottom，独占两行，没有时默认为0，
        if (StrUtil.isNotBlank(threeTableVo.getBottomDescThreeVo())) {
            bottomRows = 2;
        }
        //如果有title，默认也独占两行，没有时默认为0
        if (!Objects.isNull(threeTableVo.getTopTitleOneVo())) {
            titleRows = 2;
            //只有在这种情况下才为true
            if (StrUtil.isNotBlank(threeTableVo.getTopTitleOneVo().getLeftDesc()) ||
                    StrUtil.isNotBlank(threeTableVo.getTopTitleOneVo().getRightDesc())) {
                hasCorner = true;
            }
        }
        //具体头部topRows占titleRows行还是titleRows+1行，就需要更加title是否有左右下角来定。
        topRows = calTopRows(threeTableVo.getTopTitleOneVo(), titleRows);
    }

    private int calTopRows(ThreeTableVo.TopTitleOneVo topTitleOneVo, int titleRows) {
        if (topTitleOneVo == null) {
            return 0;
        }
        //左右下角都为空时
        if (StrUtil.isBlank(topTitleOneVo.getLeftDesc()) && StrUtil.isBlank(topTitleOneVo.getRightDesc())) {
            return titleRows;
        }
        //但凡左右有一个
        return titleRows + 1;
    }

    /**
     * 其他几个构造函数，titleRows可设置为指定值，除非TopTitleOneVo不为空。
     * @param titleRows
     * @param threeTableVo
     */
    public CustomSheetStrategy(Integer titleRows, ThreeTableVo threeTableVo) {
        this(threeTableVo);
        if (!Objects.isNull(threeTableVo.getTopTitleOneVo())) {
            this.titleRows = titleRows;
        }
        topRows = calTopRows(threeTableVo.getTopTitleOneVo(), titleRows);
    }

    public CustomSheetStrategy(ThreeTableVo threeTableVo, Integer bottomRows) {
        this(threeTableVo);
        if (StrUtil.isNotBlank(threeTableVo.getBottomDescThreeVo())) {
            this.bottomRows = bottomRows;
        }
    }

    public CustomSheetStrategy(ThreeTableVo threeTableVo, Integer titleRows, Integer bottomRows) {
        this(titleRows, threeTableVo);
        if (StrUtil.isNotBlank(threeTableVo.getBottomDescThreeVo())) {
            this.bottomRows = bottomRows;
        }
    }

    /**
     * 因多个table需要合并，统一设置到sheet的WriterHandler中。
     * @return
     */
    public List<WriteHandler> getWriteHandler() {
        List<WriteHandler> writeHandlers = new ArrayList<>();
        //列宽设置可以单独在数据所在的table进行。
        writeHandlers.add(new CustomCellWidthByRange(topRows, topRows + midHeadRows + midDataRows - 1));
        if (threeTableVo.getTopTitleOneVo() != null) {
            writeHandlers.add(new TopMergeStrategy());
            writeHandlers.add(new TopStyleStrategy());
        }
        //有bottom部分时，合并与样式设置
        if (StrUtil.isNotBlank(threeTableVo.getBottomDescThreeVo())) {
            writeHandlers.add(new BottomMergeStrategy());
            writeHandlers.add(new BottomStyleStrategy());
        }
        //中间部分可能需要设置列的千分位，合并等等其他的，需要自己加
        return writeHandlers;
    }

    /**
     * 标题部分的合并策略
     */
    public class TopMergeStrategy implements SheetWriteHandler {

        @Override
        public void afterSheetCreate(SheetWriteHandlerContext context) {
            Sheet sheet = context.getWriteSheetHolder().getSheet();
            //合并大标题行
            sheet.addMergedRegionUnsafe(new CellRangeAddress(0, titleRows - 1, 0, columns - 1));
            //左下角或又下脚有元素时
            if (hasCorner) {
                if (columns / 2 - 1 > 0) {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(titleRows, titleRows, 0, columns / 2 - 1));
                }
                if (columns / 2 < columns - 1) {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(titleRows, titleRows, columns / 2, columns - 1));
                }
            }
        }
    }

    public class TopStyleStrategy extends AbstractVerticalCellStyleStrategy {
        @Override
        protected WriteCellStyle contentCellStyle(CellWriteHandlerContext context) {
            Integer columnIndex = context.getColumnIndex();
            Integer rowIndex = context.getRowIndex();
            WriteCellStyle writeCellStyle = new WriteCellStyle();
            if (rowIndex == 0) {
                WriteFont titleWriteFont = new WriteFont();
                titleWriteFont.setBold(true);
                titleWriteFont.setFontHeightInPoints((short) 20);
                writeCellStyle.setWriteFont(titleWriteFont);
                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
                writeCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                return writeCellStyle;
            }
            //没有左右下脚时，单独设置。
            if (!hasCorner) {
                return writeCellStyle;
            }

            //设置左右下脚
            if (rowIndex.equals(titleRows)) {
                WriteFont writeFont = new WriteFont();
                writeFont.setFontHeightInPoints((short)10);
                writeCellStyle.setWriteFont(writeFont);
                writeCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                if (columnIndex == 0) {
                    //列索引为0时，左下角
                    writeCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
                } else {
                    //否则列索引为1，为右下角
                    writeCellStyle.setHorizontalAlignment(HorizontalAlignment.RIGHT);
                }
            }
            //其他行无样式
            return writeCellStyle;
        }
    }

    public class BottomMergeStrategy implements SheetWriteHandler {
        @Override
        public void afterSheetCreate(SheetWriteHandlerContext context) {
            Sheet sheet = context.getWriteSheetHolder().getSheet();
            sheet.addMergedRegion(new CellRangeAddress(topRows + midHeadRows + midDataRows,
                    topRows + midHeadRows + midDataRows + bottomRows - 1, 0, columns - 1));
        }
    }

    public class BottomStyleStrategy extends AbstractVerticalCellStyleStrategy {
        @Override
        protected WriteCellStyle contentCellStyle(CellWriteHandlerContext context) {
            Integer rowIndex = context.getRowIndex();
            Integer columnIndex = context.getColumnIndex();
            WriteCellStyle writeCellStyle = new WriteCellStyle();
            if (rowIndex == topRows + midHeadRows + midDataRows && columnIndex == 0) {
                //字体默认字体，设置水平居左，垂直居中即可
                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
                writeCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            }
            return writeCellStyle;
        }
    }
}

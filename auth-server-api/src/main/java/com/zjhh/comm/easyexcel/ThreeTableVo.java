package com.zjhh.comm.easyexcel;

import lombok.Getter;

import java.util.List;

/**
 * 注意：使用构造器初始化问题。
 * 1.topTitleOneVo可以为null，bottomDescThreeVo可以为null
 * 2.midContentTwoVo不能为null
 *
 *
 *
 * <AUTHOR>
 * @date 2023/5/2
 */
@Getter
public class ThreeTableVo {

    private TopTitleOneVo topTitleOneVo;

    private final MidContentTwoVo midContentTwoVo;

    private String bottomDescThreeVo;

    /**
     * 只有中间部分
     * @param midContentTwoVo
     */
    public ThreeTableVo(MidContentTwoVo midContentTwoVo) {
        this.midContentTwoVo = midContentTwoVo;
    }

    /**
     * 有中间部分和大标题
     * @param topTitleOneVo
     * @param midContentTwoVo
     */
    public ThreeTableVo(TopTitleOneVo topTitleOneVo, MidContentTwoVo midContentTwoVo) {
        this.topTitleOneVo = topTitleOneVo;
        this.midContentTwoVo = midContentTwoVo;
    }

    /**
     * 有中间部分和底部描述
     * @param midContentTwoVo
     * @param bottomDescThreeVo
     */
    public ThreeTableVo(MidContentTwoVo midContentTwoVo, String bottomDescThreeVo) {
        this.midContentTwoVo = midContentTwoVo;
        this.bottomDescThreeVo = bottomDescThreeVo;
    }

    /**
     * 大标题，中间内容，底部描述都有
     * @param topTitleOneVo
     * @param midContentTwoVo
     * @param bottomDescThreeVo
     */
    public ThreeTableVo(TopTitleOneVo topTitleOneVo, MidContentTwoVo midContentTwoVo, String bottomDescThreeVo) {
        this.topTitleOneVo = topTitleOneVo;
        this.midContentTwoVo = midContentTwoVo;
        this.bottomDescThreeVo = bottomDescThreeVo;
    }

    /**
     * 初始化时title不能为空，其它可以为空
     */
    @Getter
    public static class TopTitleOneVo {
        /**
         * 中间大标题
         * 必须初始化，且不会改变，故用final
         */
        private final String title;

        /**
         * 左下脚
         */
        private String leftDesc;

        /**
         * 又下脚描述
         */
        private String rightDesc;

        public TopTitleOneVo(String title) {
            this.title = title;
        }

        public TopTitleOneVo(String title, String rightDesc) {
            this(title);
            this.rightDesc = rightDesc;
        }

        public TopTitleOneVo(String title, String leftDesc, String rightDesc) {
            this(title, rightDesc);
            this.leftDesc = leftDesc;
        }
    }

    /**
     * headList与clazzHead二选一。clazzHead不能为null，headList不能为空
     * dataList允许是空集合，但不能为null
     */
    @Getter
    public static class MidContentTwoVo {

        private List<List<String>> headList;

        private Class<?> clazzHead;

        /**
         * 有可能是List<T>类型，也有可能是List<List<Object>>，easyexcel会自动解析。
         * 这里设置样式时，主要用到其size属性
         */
        private final List<?> dataList;

        public MidContentTwoVo(List<List<String>> headList, List<?> dataList) {
            this.headList = headList;
            this.dataList = dataList;
        }

        public MidContentTwoVo(Class<?> clazzHead, List<?> dataList) {
            this.clazzHead = clazzHead;
            this.dataList = dataList;
        }
    }
}

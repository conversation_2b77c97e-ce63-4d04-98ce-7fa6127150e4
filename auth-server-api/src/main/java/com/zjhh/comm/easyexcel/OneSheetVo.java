package com.zjhh.comm.easyexcel;

import com.alibaba.excel.write.handler.WriteHandler;
import com.zjhh.comm.vo.BaseVo;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/10
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class OneSheetVo extends BaseVo {
    private static final long serialVersionUID = -2084243365476738466L;

    /**
     * sheet页的合并策略、样式、数据等
     */
    private final CustomSheetStrategy sheetStrategy;

    /**
     * sheet页名称
     */
    private String sheetName;

    /**
     * sheet页核心table部分策略。如行合并，单元格格式等
     */
    private List<WriteHandler> writeHandlers;

    public OneSheetVo(CustomSheetStrategy sheetStrategy) {
        this.sheetStrategy = sheetStrategy;
    }

    public OneSheetVo(CustomSheetStrategy sheetStrategy, String sheetName) {
        this.sheetStrategy = sheetStrategy;
        this.sheetName = sheetName;
    }

    public OneSheetVo(CustomSheetStrategy sheetStrategy, String sheetName, List<WriteHandler> writeHandlers) {
        this.sheetStrategy = sheetStrategy;
        this.sheetName = sheetName;
        this.writeHandlers = writeHandlers;
    }
}

package com.zjhh.comm.easyexcel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.data.DataFormatData;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.builder.ExcelWriterTableBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.zjhh.comm.vo.ColumnResultVO;
import com.zjhh.comm.vo.ColumnVO;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/12/10
 */
@Slf4j
public class EasyExcelUtil {

    public static HorizontalCellStyleStrategy getExcelProperty() {
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为红色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 10);
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
        contentWriteCellStyle.setWrapped(true);
        contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        DataFormatData dataFormatData = new DataFormatData();
        //#,###的形式会导致0在excel上不显示。#,##0.##小数点后0~2位都可以，整数时excel显示多个小数点
        dataFormatData.setFormat("#,##0.00");
        contentWriteCellStyle.setDataFormatData(dataFormatData);
        // 背景绿色
        contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setBold(true);
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 10);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle,
                contentWriteCellStyle);

        return horizontalCellStyleStrategy;
    }

    public static HorizontalCellStyleStrategy getExcelPropertyNoDataFormat() {
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为红色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        //字号大小，如果字号变大，那么列宽设置
        headWriteFont.setFontHeightInPoints((short) 10);
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
        contentWriteCellStyle.setWrapped(true);
        contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        // 背景绿色
        contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setBold(true);
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 10);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle,
                contentWriteCellStyle);

        return horizontalCellStyleStrategy;
    }

    /**
     * 表头部分为白色，且内容未设置数据格式
     *
     * @return
     */
    public static HorizontalCellStyleStrategy getExcelPropertyNoDataFormatAndAllWhite() {
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为红色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        //字号大小，如果字号变大，那么列宽设置
        headWriteFont.setFontHeightInPoints((short) 10);
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
        contentWriteCellStyle.setWrapped(true);
        contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        // 背景绿色
        contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont contentWriteFont = new WriteFont();
        //contentWriteFont.setBold(true);
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 10);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle,
                contentWriteCellStyle);

        return horizontalCellStyleStrategy;
    }

    /**
     * 获取表头的行数和列数
     *
     * @param headList
     * @return
     */
    public static int[] getRowsAndColumns(List<List<String>> headList) {
        if (CollUtil.isEmpty(headList)) {
            return new int[]{0, 0};
        }
        int rows = 0;
        int cols = headList.size();
        for (List<String> head : headList) {
            if (CollUtil.isNotEmpty(head)) {
                rows = Math.max(rows, head.size());
            }
        }
        return new int[]{rows, cols};
    }

    /**
     * 获取表头的行数和列数。
     * 注意：这里只考虑有ExcelProperty注解的情形。
     *
     * @param clazz
     * @return
     */
    public static int[] getRowsAndColumns(Class<?> clazz) {
        if (clazz == null) {
            return new int[]{0, 0};
        }
        int rows = 0;
        int cols = 0;
        Field[] declaredFields =  ReflectUtil.getFields(clazz);
        for (Field field : declaredFields) {
            if (field.isAnnotationPresent(ExcelProperty.class)) {
                ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
                //获取注解上的列title
                String[] titles = annotation.value();
                rows = Math.max(rows, titles.length);
                cols++;
            }
        }
        return new int[]{rows, cols};
    }

    /**
     * write方法没有执行excelWriter.finish方法
     *
     * @param sheetStrategy 上中下三部分设置
     * @param excelWriter   excel相关对象
     * @param writeSheet    sheet相关对象
     * @param writeHandlers sheet中核心的table补充样式，如列格式，行合并等。中间部分的自适应列宽以集成到sheetStrategy
     */
    public static void writeOneSheet(CustomSheetStrategy sheetStrategy, ExcelWriter excelWriter, WriteSheet writeSheet,
                                     List<WriteHandler> writeHandlers) {
        int columns = sheetStrategy.getColumns();
        ThreeTableVo threeTableVo = sheetStrategy.getThreeTableVo();
        ThreeTableVo.TopTitleOneVo topTitleOneVo = threeTableVo.getTopTitleOneVo();
        String bottomDesc = threeTableVo.getBottomDescThreeVo();
        ThreeTableVo.MidContentTwoVo midContentTwoVo = threeTableVo.getMidContentTwoVo();
        if (topTitleOneVo != null) {
            int titleRows = sheetStrategy.getTitleRows();
            WriteTable writeTable0 = EasyExcel.writerTable(0).build();
            List<List<Object>> topList = new ArrayList<>();
            topList.add(CollUtil.newArrayList(topTitleOneVo.getTitle()));
            for (int i = 0; i < titleRows - 1; i++) {
                //标题title部分要合并，故加几个空集合。
                topList.add(new ArrayList<>());
            }
            String leftDesc = topTitleOneVo.getLeftDesc();
            String rightDesc = topTitleOneVo.getRightDesc();
            if (StrUtil.isNotBlank(leftDesc) || StrUtil.isNotBlank(rightDesc)) {
                //左右下脚只要有一个不为空，就需要设置数据。
                List<Object> topDescList = new ArrayList<>();
                for (int i = 0; i < columns; i++) {
                    if (i == 0 && StrUtil.isNotBlank(leftDesc)) {
                        topDescList.add(leftDesc);
                    } else if (i == columns / 2 && StrUtil.isNotBlank(rightDesc)) {
                        topDescList.add(rightDesc);
                    } else {
                        topDescList.add(null);
                    }
                }
                topList.add(topDescList);
            }
            excelWriter.write(topList, writeSheet, writeTable0);
        }
        ExcelWriterTableBuilder excelWriterTableBuilder = EasyExcel.writerTable(1).needHead(true)
                .registerWriteHandler(getExcelPropertyNoDataFormatAndAllWhite());
        if (CollUtil.isNotEmpty(writeHandlers)) {
            for (WriteHandler writeHandler : writeHandlers) {
                excelWriterTableBuilder.registerWriteHandler(writeHandler);
            }
        }
        if (midContentTwoVo.getClazzHead() == null) {
            excelWriterTableBuilder.head(midContentTwoVo.getHeadList());
        } else {
            excelWriterTableBuilder.head(midContentTwoVo.getClazzHead());
        }
        WriteTable writeTable1 = excelWriterTableBuilder.build();
        excelWriter.write(midContentTwoVo.getDataList(), writeSheet, writeTable1);
        if (StrUtil.isNotBlank(bottomDesc)) {
            WriteTable writeTable2 = EasyExcel.writerTable(2).build();
            List<List<Object>> bottomList = new ArrayList<>();
            bottomList.add(CollUtil.newArrayList(bottomDesc));
            excelWriter.write(bottomList, writeSheet, writeTable2);
        }
    }

    public static void exportOneSheetExcel(OutputStream outputStream, CustomSheetStrategy sheetStrategy, String excelName, String sheetName, WriteHandler... writeHandlers) throws IOException {
        ExcelWriter excelWriter = EasyExcel.write(outputStream).build();

        ExcelWriterSheetBuilder excelWriterSheetBuilder = EasyExcel.writerSheet(0, sheetName);
        for (WriteHandler writeHandler : sheetStrategy.getWriteHandler()) {
            excelWriterSheetBuilder.registerWriteHandler(writeHandler);
        }
        //网格线设置
        WriteSheet writeSheet = excelWriterSheetBuilder
                .registerWriteHandler(new GridLineDisplayStrategy(false)).build();

        int columns = sheetStrategy.getColumns();
        ThreeTableVo threeTableVo = sheetStrategy.getThreeTableVo();
        ThreeTableVo.TopTitleOneVo topTitleOneVo = threeTableVo.getTopTitleOneVo();
        String bottomDesc = threeTableVo.getBottomDescThreeVo();
        ThreeTableVo.MidContentTwoVo midContentTwoVo = threeTableVo.getMidContentTwoVo();
        if (topTitleOneVo != null) {
            int titleRows = sheetStrategy.getTitleRows();
            WriteTable writeTable0 = EasyExcel.writerTable(0).build();
            List<List<Object>> topList = new ArrayList<>();
            topList.add(CollUtil.newArrayList(topTitleOneVo.getTitle()));
            for (int i = 0; i < titleRows - 1; i++) {
                //标题title部分要合并，故加几个空集合。
                topList.add(new ArrayList<>());
            }
            String leftDesc = topTitleOneVo.getLeftDesc();
            String rightDesc = topTitleOneVo.getRightDesc();
            if (StrUtil.isNotBlank(leftDesc) || StrUtil.isNotBlank(rightDesc)) {
                //左右下脚只要有一个不为空，就需要设置数据。
                List<Object> topDescList = new ArrayList<>();
                for (int i = 0; i < columns; i++) {
                    if (i == 0 && StrUtil.isNotBlank(leftDesc)) {
                        topDescList.add(leftDesc);
                    } else if (i == columns / 2 && StrUtil.isNotBlank(rightDesc)) {
                        topDescList.add(rightDesc);
                    } else {
                        topDescList.add(null);
                    }
                }
                topList.add(topDescList);
            }
            excelWriter.write(topList, writeSheet, writeTable0);
        }
        ExcelWriterTableBuilder excelWriterTableBuilder = EasyExcel.writerTable(1).needHead(true)
                .registerWriteHandler(getExcelPropertyNoDataFormatAndAllWhite());

        if (ArrayUtil.isNotEmpty(writeHandlers)) {
            for (WriteHandler writeHandler : writeHandlers) {
                excelWriterTableBuilder.registerWriteHandler(writeHandler);
            }
        }
        if (midContentTwoVo.getClazzHead() == null) {
            excelWriterTableBuilder.head(midContentTwoVo.getHeadList());
        } else {
            excelWriterTableBuilder.head(midContentTwoVo.getClazzHead());
        }
        WriteTable writeTable1 = excelWriterTableBuilder.build();
        excelWriter.write(midContentTwoVo.getDataList(), writeSheet, writeTable1);
        if (StrUtil.isNotBlank(bottomDesc)) {
            WriteTable writeTable2 = EasyExcel.writerTable(2).build();
            List<List<Object>> bottomList = new ArrayList<>();
            bottomList.add(CollUtil.newArrayList(bottomDesc));
            excelWriter.write(bottomList, writeSheet, writeTable2);
        }
        excelWriter.finish();
    }

    /**
     * 单个个sheet时导出。无网格线，表头无背景色
     *
     * @param response
     * @param sheetStrategy
     * @param excelName
     * @param sheetName
     * @param writeHandlers
     * @throws IOException
     */
    public static void exportOneSheetExcel(HttpServletResponse response, CustomSheetStrategy sheetStrategy, String excelName, String sheetName, WriteHandler... writeHandlers) throws IOException {
        String fileName = URLEncoder.encode(excelName, "UTF-8").replaceAll("\\+", "%20") + ".xlsx";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=" + fileName);
        exportOneSheetExcel(response.getOutputStream(), sheetStrategy, excelName, sheetName, writeHandlers);
    }

    /**
     * 多个sheet页导出，
     * export方法执行了excelWriter.finish();
     *
     * @param response
     * @param sheetVos
     * @param excelName
     * @throws IOException
     */
    public static void exportMultiSheetExcel(HttpServletResponse response, List<OneSheetVo> sheetVos, String excelName) throws IOException {
        if (CollUtil.isEmpty(sheetVos)) {
            throw new RuntimeException("没有sheet需要导出");
        }
        String fileName = URLEncoder.encode(excelName, "UTF-8").replaceAll("\\+", "%20") + ".xlsx";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=" + fileName);
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .build();

        for (int i = 0; i < sheetVos.size(); i++) {
            OneSheetVo sheetVo = sheetVos.get(i);
            CustomSheetStrategy sheetStrategy = sheetVo.getSheetStrategy();
            List<WriteHandler> writeHandlers = sheetVo.getWriteHandlers();
            ExcelWriterSheetBuilder excelWriterSheetBuilder = EasyExcel.writerSheet(i, sheetVo.getSheetName());
            for (WriteHandler writeHandler : sheetStrategy.getWriteHandler()) {
                excelWriterSheetBuilder.registerWriteHandler(writeHandler);
            }
            //网格线设置
            WriteSheet writeSheet = excelWriterSheetBuilder
                    .registerWriteHandler(new GridLineDisplayStrategy(false)).build();
            writeOneSheet(sheetStrategy, excelWriter, writeSheet, writeHandlers);
        }
        excelWriter.finish();
    }

    /**
     * 使用场景：用于表格导出时，移除当前行数据的不需要导出列，如uuid，code等。
     *
     * @param map        原始map数据。只对LinkedHashMap有效
     * @param removedPre 需要移除前面几个键值对的数据
     * @param <K>        map的key类型
     * @param <T>        map的value类型
     * @return value组成的list集合
     */
    public static <K, T> List<T> mapValueToList(Map<K, T> map, int removedPre) {
        List<T> list = new ArrayList<>();
        if (!(map instanceof LinkedHashMap)) {
            return list;
        }
        if (CollUtil.isEmpty(map) || map.size() <= removedPre) {
            return list;
        }
        int point = 0;
        for (T value : map.values()) {
            if (point < removedPre) {
                point++;
            } else {
                list.add(value);
            }
        }
        return list;
    }

    /**
     * ColumnResultVo中的tables转为导出excel需要的数据格式。
     * 已废弃，推荐使用tableDataToList(ColumnResultVO columnResultVO)
     *
     * @param tables     元素tables表格数据
     * @param removedPre 每行数据需要移除前面几列
     * @param <K>        map的key类型
     * @param <T>        value组成的list集合
     * @return
     */
    @Deprecated
    public static <K, T> List<List<T>> tableDataToList(List<Map<K, T>> tables, int removedPre) {
        List<List<T>> excelData = new ArrayList<>();
        for (Map<K, T> map : tables) {
            excelData.add(mapValueToList(map, removedPre));
        }
        return excelData;
    }

    /**
     * ColumnResultVo中的tables转为导出excel需要的数据格式
     *
     * @param columnResultVO
     * @return
     */
    public static List<List<Object>> tableDataToList(ColumnResultVO columnResultVO) {
        return tableDataToList(columnResultVO.getColumns(), columnResultVO.getTables());
    }

    public static List<List<Object>> tableDataToList(List<? extends ColumnVO> columns, List<Map<String, Object>> tables) {
        List<List<Object>> dataList = new ArrayList<>();
        List<String> keyList = getKeyList(columns);
        for (Map<String, Object> map : tables) {
            List<Object> curData = new ArrayList<>();
            for (String key : keyList) {
                curData.add(map.get(key));
            }
            dataList.add(curData);
        }
        return dataList;
    }

    /**
     * 获取需要的key
     *
     * @return
     */
    private static List<String> getKeyList(List<? extends ColumnVO> columns) {
        List<String> list = new ArrayList<>();
        for (ColumnVO columnVO : columns) {
            if (CollUtil.isEmpty(columnVO.getChildren())) {
                list.add(columnVO.getDataIndex());
            } else {
                list.addAll(getKeyList(columnVO.getChildren()));
            }
        }
        return list;
    }

    /**
     * 行转列的表头，转为excel表头格式。
     *
     * @param columnVOS
     * @return
     */
    public static List<List<String>> columnsToExcelHead(List<? extends ColumnVO> columnVOS) {
        List<List<String>> resultList = new ArrayList<>();
        columnVOS.forEach(columnVO -> {
            resultList.addAll(handle(columnVO));
        });
        return resultList;
    }

    private static List<List<String>> handle(ColumnVO columnVO) {
        List<List<String>> resultList = new ArrayList<>();
        List<String> preList = CollUtil.newArrayList(columnVO.getTitle());
        if (CollUtil.isEmpty(columnVO.getChildren())) {
            resultList.add(preList);
        } else {
            for (ColumnVO columnVO1 : columnVO.getChildren()) {
                List<List<String>> handle = handle(columnVO1);
                for (List<String> strings : handle) {
                    List<String> tempList = new ArrayList<>();
                    tempList.addAll(preList);
                    tempList.addAll(strings);
                    resultList.add(tempList);
                }
            }
        }
        return resultList;
    }

    /**
     * 无泛型的List<Map>转为有泛型的List<Map<K,T>>
     *
     * @param list
     * @param <K>
     * @param <T>
     * @return
     */
    public static <K, T> List<Map<K, T>> mapConvert(List<Map> list) {
        List<Map<K, T>> answer = new ArrayList<>();
        if (CollUtil.isEmpty(list)) {
            return answer;
        }
        for (Map map : list) {
            answer.add(map);
        }
        return answer;
    }

    public static <T> List<List<Object>> beanListToExcelList(List<T> list) {
        List<List<Object>> result = new ArrayList<>();
        for (T entity : list) {
            result.add(beanToListByOrder(entity));
        }
        return result;
    }

    public static <T> List<Object> beanToListByOrder(T entity) {
        Class<?> aClass = entity.getClass();
        Field[] declaredFields = aClass.getDeclaredFields();
        TreeMap<Integer, Object> treeMap = new TreeMap<>();
        for (Field field : declaredFields) {
            if (field.isAnnotationPresent(ExcelProperty.class)) {
                int value = field.getAnnotation(ExcelProperty.class).index();
                try {
                    field.setAccessible(true);
                    treeMap.put(value, field.get(entity));
                } catch (IllegalAccessException e) {
                    log.info("反射错误-{}", entity);
                }
            }
        }
        //treeMap的迭代器根据红黑树前序遍历，key越小时value越靠前
        return new ArrayList<>(treeMap.values());
    }
}

package com.zjhh.comm.easyexcel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import org.apache.poi.ss.usermodel.Sheet;

/**
 * 网格线是否显示
 * <AUTHOR>
 * @date 2023/5/1
 */
public class GridLineDisplayStrategy implements SheetWriteHandler {

    private final Boolean show;

    public GridLineDisplayStrategy(Boolean show) {
        this.show = show;
    }

    @Override
    public void afterSheetCreate(SheetWriteHandlerContext context) {
        Sheet sheet = context.getWriteSheetHolder().getSheet();
        sheet.setDisplayGridlines(show);
    }
}

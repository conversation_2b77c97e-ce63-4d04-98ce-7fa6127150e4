package com.zjhh.comm.easyexcel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 表格内容部分（不算表头）的合并策略。
 * 通常表格不包括下面一行描述。
 *
 * 注意：此合并入参时dataList不包括表格下方一行的list。
 * <AUTHOR>
 * @date 2023/4/26
 */
public class ContentRowMergeStrategy implements SheetWriteHandler {

    /**
     * 开始合并的行
     */
    private Integer startRow;

    /**
     * 开始合并的列
     */
    private Integer startColumn;

    /**
     * 结束合并的列
     */
    private Integer endColumn;

    /**
     * 需要合并的表格数据。
     * 注意：不包括表头以及表格下特意增加的描述行
     */
    private List<List<Object>> dataList;

    public ContentRowMergeStrategy(Integer startRow, Integer startColumn, Integer endColumn, List<List<Object>> dataList) {
        this.startRow = startRow;
        this.startColumn = startColumn;
        this.endColumn = endColumn;
        this.dataList = dataList;
    }

    @Override
    public void afterSheetCreate(SheetWriteHandlerContext context) {
        if (Objects.isNull(dataList) || dataList.isEmpty()) {
            return;
        }
        //这里需要对原有入参进行修正。暂忽略
        Sheet sheet = context.getWriteSheetHolder().getSheet();
        Set<String> alreadyMergeSet = new HashSet<>();
        int m = dataList.size();
        for (int i = 0; i < m; i++) {
            for (int j = startColumn; j <= endColumn; j++) {
                //当前合并的起始行和结束行
                int bottomRow = i;
                String key = i + "-" + j;
                if (alreadyMergeSet.contains(key)) {
                    continue;
                }
                alreadyMergeSet.add(key);
                //需要确保集合中没有null元素。还需要确保list的最短长度，否则可能报错
                Object baseValue = dataList.get(i).get(j);
                for (int k = i + 1; k < m; k++) {
                    //需要判断k行与i行前面的所有列数据是不是完全相同。
                    boolean preFlag = true;
                    for (int h = startColumn; h < j; h++) {
                        if (!Objects.equals(dataList.get(i).get(h), dataList.get(k).get(h))) {
                            preFlag = false;
                            break;
                        }
                    }
                    //当前行数据没有访问，那么下一行一定也没有访问。
                    if (preFlag && Objects.equals(baseValue, dataList.get(k).get(j))) {
                        bottomRow = k;
                        alreadyMergeSet.add(k + "-" + j);
                    } else {
                        break;
                    }
                }
                if (bottomRow != i) {
                    //注意合并时有个起始的基准行startRow
                    sheet.addMergedRegion(new CellRangeAddress(i + startRow, bottomRow + startRow, j, j));
                }
            }
        }
    }
}

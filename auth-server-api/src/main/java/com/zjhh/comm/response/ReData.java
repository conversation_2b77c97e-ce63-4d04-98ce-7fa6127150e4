package com.zjhh.comm.response;

import com.zjhh.comm.constant.CommConstants;
import com.zjhh.comm.exception.BaseException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2020/6/8 16:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "接口返回对象")
public class ReData<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 6114037547157056666L;

    @Schema(description = "成功标志")
    private Boolean isSuccess;

    @Schema(description = "返回代码")
    private Integer code;

    @Schema(description = "处理结果描述")
    private String message;

    @Schema(description = "返回数据对象")
    private T data;

    @Schema(description = "时间戳")
    private Long timestamp;

    private ReData(Boolean isSuccess, Integer code, String message, T data) {
        this.isSuccess = isSuccess;
        this.code = code;
        this.message = message;
        this.data = data;
        timestamp = System.currentTimeMillis();
    }

    public static <T> ReData<T> success(T data) {
        return new ReData<>(true, CommConstants.CODE_SUCCESS, "成功", data);
    }

    public static <T> ReData<T> success() {
        return success(null);
    }

    public static <T> ReData<T> success(String message) {
        return new ReData<>(true, CommConstants.CODE_SUCCESS, message, null);
    }

    public static <T> ReData<T> successStr(T data) {
        return new ReData<>(true, CommConstants.CODE_SUCCESS, null, data);
    }

    public static <T> ReData<T> error(String description) {
        return new ReData<>(false, CommConstants.CODE_SERVER_ERROR, description, null);
    }

    public static <T> ReData<T> error(Integer code, String description) {
        return new ReData<>(false, code, description, null);
    }

    public static <T> ReData<T> error(BaseException e) {
        return new ReData<>(false, e.getCode(), e.getMessage(), null);
    }

}

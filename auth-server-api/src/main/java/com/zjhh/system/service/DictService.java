package com.zjhh.system.service;

import com.zjhh.comm.dto.LicenceProduct;
import com.zjhh.comm.dto.LicenceServiceInfo;
import com.zjhh.comm.dto.LicenceUser;
import com.zjhh.db.comm.Page;
import com.zjhh.system.dao.entity.Dict;
import com.zjhh.system.dto.WatermarkDto;
import com.zjhh.system.request.AddDictReq;
import com.zjhh.system.request.PageSearchReq;
import com.zjhh.system.request.UpdateDictReq;

/**
 * <AUTHOR>
 * @since 2021/3/9 11:12
 */
public interface DictService {

    /**
     * 获取字典
     *
     * @param code
     * @return
     */
    String getValue(String code);

    /**
     * 是否需要验证码
     *
     * @return
     */
    Boolean needCaptcha();

    /**
     * 获取字典列表
     *
     * @param req
     * @return
     */
    Page<Dict> pageDict(PageSearchReq req);

    /**
     * 修改字典
     *
     * @param req
     */
    void update(UpdateDictReq req);

    /**
     * 修改字典
     *
     * @param code
     * @param value
     */
    void update(String code, String value);

    /**
     * 新增字典
     *
     * @param req
     */
    void add(AddDictReq req);

    /**
     * 删除字典
     *
     * @param id
     */
    void delete(String id);

    /**
     * 刷新缓存
     */
    void flushCache();

    /**
     * 新增服务商信息
     *
     * @param info
     */
    void addLicenceService(LicenceServiceInfo info);

    /**
     * 用户信息
     *
     * @param user
     */
    void addLicenceUser(LicenceUser user);

    /**
     * 产品注册信息
     *
     * @param product
     */
    void addLicenceProduct(LicenceProduct product);

    /**
     * 获取服务商信
     *
     * @return
     */
    LicenceServiceInfo getLicenceService();

    /**
     * 获取服务商信
     *
     * @return
     */
    LicenceUser getLicenceUser();

    /**
     * 获取服务商信
     *
     * @return
     */
    LicenceProduct getLicenceProduct();

    /**
     * 获取文件上传路径
     *
     * @return
     */
    String getFileUploadPath();

    /*
     * 是否开启复杂密码认证
     *
     * @return
     */
    Boolean complexPassword();

    /**
     * 获取水印
     *
     * @return
     */
    WatermarkDto getWatermark();

    /**
     * 是否开启代表监督录入
     *
     * @return
     */
    Boolean repSupervision();

    /**
     * 获取系统行政区划
     *
     * @return
     */
    String getSystemArea();

    /**
     * 获取是否开启我要建议
     *
     * @return
     */
    Boolean getSuggest();

    /**
     * @return
     */
    String getLoginQrcodeName();

    /**
     * 获取系统名称
     *
     * @return
     */
    String getSystemName();

    /**
     * 获取后台管理logo名称
     *
     * @return
     */
    String getAdminName();
}

package com.zjhh.system.service;

import com.zjhh.db.comm.Page;
import com.zjhh.system.dao.entity.QuartzJob;
import com.zjhh.system.request.AddQuartzJobReq;
import com.zjhh.system.request.PageQuartzJobReq;
import com.zjhh.system.request.UpdateQuartzJobReq;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/10 17:09
 */
public interface QuartzService {

    /**
     * 新增任务
     *
     * @param req
     */
    void add(AddQuartzJobReq req);

    /**
     * 修改任务
     *
     * @param req
     */
    void update(UpdateQuartzJobReq req);

    /**
     * 暂停任务
     *
     * @param id
     */
    void pause(String id);

    /**
     * 运行一次
     *
     * @param id
     */
    void runOnce(String id);

    /**
     * 恢复任务
     *
     * @param id
     */
    void resume(String id);

    /**
     * 删除任务
     *
     * @param id
     */
    void delete(String id);

    /**
     * 初始化任务
     */
    void initJob();

    /**
     * 获取定时任务分页
     *
     * @param req
     * @return
     */
    Page<QuartzJob> pageQuartzJob(PageQuartzJobReq req);

    /**
     * 批量删除
     *
     * @param ids
     */
    void deleteBatch(List<String> ids);
}

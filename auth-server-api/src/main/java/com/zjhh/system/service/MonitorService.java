package com.zjhh.system.service;

import com.zjhh.system.vo.JvmInfoVo;
import com.zjhh.system.vo.RedisInfoVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/4 16:57
 */
public interface MonitorService {

    /**
     * 获取所有redis信息
     *
     * @return
     */
    List<RedisInfoVo> listAllRedisInfo();

    /**
     * 获取redis内存信息
     *
     * @return
     */
    RedisInfoVo getRedisMemory();

    /**
     * 获取redis keys数量
     *
     * @return
     */
    RedisInfoVo countRedisKey();

    /**
     * 获取jvm信息
     *
     * @return
     */
    JvmInfoVo getJvmInfo();
}

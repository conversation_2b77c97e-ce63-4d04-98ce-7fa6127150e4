package com.zjhh.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.exception.BizException;
import com.zjhh.db.comm.Page;
import com.zjhh.system.dao.entity.QuartzJob;
import com.zjhh.system.dao.mapper.QuartzJobMapper;
import com.zjhh.system.request.AddQuartzJobReq;
import com.zjhh.system.request.PageQuartzJobReq;
import com.zjhh.system.request.UpdateQuartzJobReq;
import com.zjhh.system.service.QuartzService;
import com.zjhh.system.utils.QuartzUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/3/10 17:09
 */
@Slf4j
@Service
public class QuartzServiceImpl implements QuartzService {

    @Resource
    private QuartzJobMapper quartzJobMapper;

    @Resource
    private Scheduler scheduler;

    @Override
    public void add(AddQuartzJobReq req) {
        QuartzJob job = BeanUtil.copyProperties(req, QuartzJob.class);
        job.setJobKey(IdUtil.simpleUUID());
        quartzJobMapper.insert(job);
        if (job.getStart()) {
            QuartzUtil.createScheduleJob(scheduler, job);
        }
    }

    @Override
    public void update(UpdateQuartzJobReq req) {
        QuartzJob job = checkJob(req.getId());
        job.setJobClass(req.getJobClass());
        job.setDescription(req.getDescription());
        job.setCronExpression(req.getCronExpression());
        quartzJobMapper.updateById(job);
        if (QuartzUtil.checkScheduleJob(scheduler, job.getJobKey())) {
            QuartzUtil.updateScheduleJob(scheduler, job);
        }
    }

    @Override
    public void pause(String id) {
        QuartzJob job = checkJob(id);
        job.setStart(false);
        quartzJobMapper.updateById(job);
        QuartzUtil.pauseScheduleJob(scheduler, job.getJobKey());
    }

    @Override
    public void runOnce(String id) {
        QuartzJob job = checkJob(id);
        QuartzUtil.runOnce(scheduler, job.getJobKey());
    }

    @Override
    public void resume(String id) {
        QuartzJob job = checkJob(id);
        job.setStart(true);
        quartzJobMapper.updateById(job);
        if (QuartzUtil.checkScheduleJob(scheduler, job.getJobKey())) {
            QuartzUtil.resumeScheduleJob(scheduler, job.getJobKey());
        } else {
            QuartzUtil.createScheduleJob(scheduler, job);
        }
    }

    @Override
    public void delete(String id) {
        QuartzJob job = checkJob(id);
        quartzJobMapper.deleteById(id);
        if (QuartzUtil.checkScheduleJob(scheduler, job.getJobKey())) {
            QuartzUtil.deleteScheduleJob(scheduler, job.getJobKey());
        }
    }

    @Override
    public void initJob() {
        log.info("初始化定时任务！");
        QueryWrapper<QuartzJob> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(QuartzJob::getStart, true);
        List<QuartzJob> quartzJobs = quartzJobMapper.selectList(wrapper);
        quartzJobs.forEach(quartzJob -> QuartzUtil.createScheduleJob(scheduler, quartzJob));
    }

    @Override
    public Page<QuartzJob> pageQuartzJob(PageQuartzJobReq req) {
        Page<QuartzJob> page = new Page<>();
        page.setCurrent(req.getCurrent());
        page.setSize(req.getSize());
        QueryWrapper<QuartzJob> wrapper = new QueryWrapper<>();
        if (Objects.nonNull(req.getStart())) {
            wrapper.lambda().eq(QuartzJob::getStart, req.getStart());
        }
        if (StrUtil.isNotBlank(req.getSearchKey())) {
            wrapper.lambda().and(wp -> wp.like(QuartzJob::getJobClass, req.getSearchKey())
                    .or()
                    .like(QuartzJob::getDescription, req.getSearchKey()));
        }
        return quartzJobMapper.selectPage(page, wrapper);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            throw new BizException("删除列表不能为空！");
        }
        quartzJobMapper.deleteBatchIds(ids);
    }

    private QuartzJob checkJob(String id) {
        QuartzJob job = quartzJobMapper.selectById(id);
        if (Objects.isNull(job)) {
            throw new BizException("该定时任务不存在！");
        }
        return job;
    }
}

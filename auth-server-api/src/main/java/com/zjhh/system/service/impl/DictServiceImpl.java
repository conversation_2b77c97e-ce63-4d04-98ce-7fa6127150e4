package com.zjhh.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.dto.LicenceProduct;
import com.zjhh.comm.dto.LicenceServiceInfo;
import com.zjhh.comm.dto.LicenceUser;
import com.zjhh.comm.exception.BizException;
import com.zjhh.db.comm.Page;
import com.zjhh.system.constants.DictConstants;
import com.zjhh.system.dao.entity.Dict;
import com.zjhh.system.dao.mapper.DictMapper;
import com.zjhh.system.dto.WatermarkDto;
import com.zjhh.system.request.AddDictReq;
import com.zjhh.system.request.PageSearchReq;
import com.zjhh.system.request.UpdateDictReq;
import com.zjhh.system.service.DictService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/3/9 11:12
 */
@Service
@DS("master")
public class DictServiceImpl implements DictService {

    @Resource
    private CacheManager cacheManager;

    private Cache<String, String> dictCache;

    @Resource
    private DictMapper dictMapper;

    @PostConstruct
    public void init() {
        QuickConfig qc = QuickConfig.newBuilder("dict:")
                .cacheType(CacheType.REMOTE)
                .syncLocal(true)
                .build();
        dictCache = cacheManager.getOrCreateCache(qc);
    }

    @Override
    public String getValue(String code) {
        String value = dictCache.get(code);
        if (StrUtil.isBlank(value)) {
            QueryWrapper<Dict> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(Dict::getCode, code);
            Dict dict = dictMapper.selectOne(wrapper);
            if (Objects.isNull(dict)) {
                return null;
            }
            value = dict.getValue();
            dictCache.put(code, value);
        }
        return value;
    }

    @Override
    public Boolean needCaptcha() {
        return Boolean.parseBoolean(getValue(DictConstants.IS_NEED_CAPTCHA));
    }

    @Override
    public Page<Dict> pageDict(PageSearchReq req) {
        QueryWrapper<Dict> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Dict::getPreShow, true);
        if (StrUtil.isNotBlank(req.getSearchKey())) {
            wrapper.lambda()
                    .like(Dict::getCode, req.getSearchKey())
                    .or()
                    .like(Dict::getDescription, req.getSearchKey());
        }
        wrapper.lambda().orderByAsc(Dict::getId);
        Page<Dict> page = req.getPage(Dict.class);
        return dictMapper.selectPage(page, wrapper);
    }

    @Override
    public void update(UpdateDictReq req) {
        Dict dict = checkDict(req.getId());
        dict.setValue(req.getValue());
        dict.setDescription(req.getDescription());
        dict.setGmtUpdate(LocalDateTime.now());
        dictCache.put(dict.getCode(), dict.getValue());
        dictMapper.updateById(dict);
    }

    @Override
    public void update(String code, String value) {
        QueryWrapper<Dict> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Dict::getCode, code);
        Dict dict = dictMapper.selectOne(wrapper);
        if (Objects.isNull(dict)) {
            return;
        }
        dict.setValue(value);
        dict.setGmtUpdate(LocalDateTime.now());
        dictCache.put(code, value);
        dictMapper.updateById(dict);
    }

    @Override
    public void add(AddDictReq req) {
        Dict dict = new Dict();
        dict.setCode(req.getCode());
        dict.setValue(req.getValue());
        dict.setDescription(req.getDescription());
        dict.setPreShow(true);
        dictMapper.insert(dict);
    }

    @Override
    public void delete(String id) {
        Dict dict = checkDict(id);
        dictCache.remove(dict.getCode());
        dictMapper.deleteById(dict);
    }

    @Override
    public void flushCache() {
        QueryWrapper<Dict> wrapper = new QueryWrapper<>();
        List<Dict> dictList = dictMapper.selectList(wrapper);
        dictList.forEach(dict -> dictCache.put(dict.getCode(), dict.getValue()));
    }

    @Override
    public void addLicenceService(LicenceServiceInfo info) {
        QueryWrapper<Dict> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Dict::getCode, DictConstants.LICENCE_SERVICE);
        Dict dict = dictMapper.selectOne(wrapper);
        if (Objects.nonNull(dict)) {
            dict.setValue(JSONObject.toJSONString(info));
            dict.setGmtUpdate(LocalDateTime.now());
            dictMapper.updateById(dict);
        } else {
            dict = new Dict();
            dict.setCode(DictConstants.LICENCE_SERVICE);
            dict.setValue(JSONObject.toJSONString(info));
            dict.setDescription("服务商信息");
            dict.setPreShow(false);
            dictMapper.insert(dict);
        }
        dictCache.remove(DictConstants.LICENCE_SERVICE);
    }

    @Override
    public void addLicenceUser(LicenceUser user) {
        QueryWrapper<Dict> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Dict::getCode, DictConstants.LICENCE_USER);
        Dict dict = dictMapper.selectOne(wrapper);
        if (Objects.nonNull(dict)) {
            dict.setValue(JSONObject.toJSONString(user));
            dict.setGmtUpdate(LocalDateTime.now());
            dictMapper.updateById(dict);
        } else {
            dict = new Dict();
            dict.setCode(DictConstants.LICENCE_USER);
            dict.setValue(JSONObject.toJSONString(user));
            dict.setDescription("用户信息");
            dict.setPreShow(false);
            dictMapper.insert(dict);
        }
        dictCache.remove(DictConstants.LICENCE_USER);
    }

    @Override
    public void addLicenceProduct(LicenceProduct product) {
        QueryWrapper<Dict> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Dict::getCode, DictConstants.LICENCE_PRODUCT);
        Dict dict = dictMapper.selectOne(wrapper);
        if (Objects.nonNull(dict)) {
            dict.setValue(JSONObject.toJSONString(product));
            dict.setGmtUpdate(LocalDateTime.now());
            dictMapper.updateById(dict);
        } else {
            dict = new Dict();
            dict.setCode(DictConstants.LICENCE_PRODUCT);
            dict.setValue(JSONObject.toJSONString(product));
            dict.setDescription("产品信息");
            dict.setPreShow(false);
            dictMapper.insert(dict);
        }
        dictCache.remove(DictConstants.LICENCE_PRODUCT);
    }

    @Override
    public LicenceServiceInfo getLicenceService() {
        String value = dictCache.get(DictConstants.LICENCE_SERVICE);
        if (StrUtil.isBlank(value)) {
            QueryWrapper<Dict> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(Dict::getCode, DictConstants.LICENCE_SERVICE);
            Dict dict = dictMapper.selectOne(wrapper);
            if (Objects.isNull(dict)) {
                return null;
            }
            value = dict.getValue();
            dictCache.put(DictConstants.LICENCE_SERVICE, value);
        }
        return ObjectUtil.isNull(value) ? null : JSONObject.parseObject(value, LicenceServiceInfo.class);
    }

    @Override
    public LicenceUser getLicenceUser() {
        String value = dictCache.get(DictConstants.LICENCE_USER);
        if (StrUtil.isBlank(value)) {
            QueryWrapper<Dict> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(Dict::getCode, DictConstants.LICENCE_USER);
            Dict dict = dictMapper.selectOne(wrapper);
            if (Objects.isNull(dict)) {
                return null;
            }
            value = dict.getValue();
            dictCache.put(DictConstants.LICENCE_USER, value);
        }
        return ObjectUtil.isNull(value) ? null : JSONObject.parseObject(value, LicenceUser.class);
    }

    @Override
    public LicenceProduct getLicenceProduct() {
        String value = dictCache.get(DictConstants.LICENCE_PRODUCT);
        if (StrUtil.isBlank(value)) {
            QueryWrapper<Dict> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(Dict::getCode, DictConstants.LICENCE_PRODUCT);
            Dict dict = dictMapper.selectOne(wrapper);
            if (Objects.isNull(dict)) {
                return null;
            }
            value = dict.getValue();
            dictCache.put(DictConstants.LICENCE_PRODUCT, value);
        }
        return ObjectUtil.isNull(value) ? null : JSONObject.parseObject(value, LicenceProduct.class);
    }

    @Override
    public String getFileUploadPath() {
        return getValue(DictConstants.UPLOAD_FILE_PATH);
    }

    @Override
    public Boolean complexPassword() {
        return Boolean.parseBoolean(getValue(DictConstants.COMPLEX_PASSWORD));
    }

    @Override
    public WatermarkDto getWatermark() {
        String value = getValue(DictConstants.WATERMARK);
        return JSONObject.parseObject(value, WatermarkDto.class);
    }

    @Override
    public Boolean repSupervision() {
        return Boolean.parseBoolean(getValue(DictConstants.REP_SUPERVISION));
    }

    @Override
    public String getSystemArea() {
        return getValue(DictConstants.SYSTEM_AREA);
    }

    @Override
    public Boolean getSuggest() {
        return Boolean.parseBoolean(getValue(DictConstants.SUGGEST));
    }

    @Override
    public String getLoginQrcodeName() {
        return getValue(DictConstants.LOGIN_QRCODE_NAME);
    }

    @Override
    public String getSystemName() {
        String value = getValue(DictConstants.SYSTEM_NAME);
        if (StrUtil.isBlank(value)) {
            value = "淏瀚财经联网";
        }
        return value;
    }

    @Override
    public String getAdminName() {
        String value = getValue(DictConstants.ADMIN_NAME);
        if (StrUtil.isBlank(value)) {
            value = "淏瀚财经联网";
        }
        return value;
    }

    private Dict checkDict(String id) {
        Dict dict = dictMapper.selectById(id);
        if (Objects.isNull(dict)) {
            throw new BizException("该字典数据不存在！");
        }
        return dict;
    }
}

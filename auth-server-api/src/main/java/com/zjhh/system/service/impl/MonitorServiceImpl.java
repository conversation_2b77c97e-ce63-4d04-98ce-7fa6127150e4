package com.zjhh.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.zjhh.system.enume.RedisInfoEnum;
import com.zjhh.system.service.MonitorService;
import com.zjhh.system.vo.JvmInfoVo;
import com.zjhh.system.vo.RedisInfoVo;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.lang.management.*;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/3/4 16:59
 */
@Service
public class MonitorServiceImpl implements MonitorService {

    @Resource
    private RedisTemplate<String, Serializable> redisTemplate;

    @Override
    public List<RedisInfoVo> listAllRedisInfo() {
        Properties info = redisTemplate.getRequiredConnectionFactory().getConnection().info("all");
        List<RedisInfoVo> list = new ArrayList<>();
        if (CollUtil.isEmpty(info)) {
            return list;
        }
        RedisInfoEnum[] redisInfoEnums = RedisInfoEnum.values();
        info.forEach((k, v) -> {
            for (RedisInfoEnum redisInfoEnum : redisInfoEnums) {
                if (redisInfoEnum.key().equals(k.toString())) {
                    RedisInfoVo vo = new RedisInfoVo();
                    vo.setKey(k.toString());
                    vo.setDescription(redisInfoEnum.description());
                    vo.setValue(v.toString());
                    list.add(vo);
                }
            }
        });
        return list;
    }

    @Override
    public RedisInfoVo getRedisMemory() {
        Properties info = redisTemplate.getRequiredConnectionFactory().getConnection().info("memory");
        if (Objects.isNull(info)) {
            return null;
        }
        RedisInfoVo vo = new RedisInfoVo();
        vo.setKey(RedisInfoEnum.USED_MEMORY.key());
        vo.setValue(info.getProperty(RedisInfoEnum.USED_MEMORY.key()));
        return vo;
    }

    @Override
    public RedisInfoVo countRedisKey() {
        Properties info = redisTemplate.getRequiredConnectionFactory().getConnection().info("keyspace");
        if (Objects.isNull(info)) {
            return null;
        }
        int sum = 0;
        Enumeration<Object> enu = info.elements();
        while (enu.hasMoreElements()) {
            Object value = enu.nextElement();
            int count = Integer.parseInt(value.toString().split(",")[0].split("=")[1]);
            sum += count;
        }
        RedisInfoVo vo = new RedisInfoVo();
        vo.setKey("keys");
        vo.setValue(String.valueOf(sum));
        return vo;
    }

    @Override
    public JvmInfoVo getJvmInfo() {
        JvmInfoVo vo = new JvmInfoVo();
        MemoryUsage heapMemoryUsage = ManagementFactory.getMemoryMXBean().getHeapMemoryUsage();
        MemoryUsage nonHeapMemoryUsage = ManagementFactory.getMemoryMXBean().getNonHeapMemoryUsage();
        ClassLoadingMXBean cl = ManagementFactory.getClassLoadingMXBean();
        ThreadMXBean threads = ManagementFactory.getThreadMXBean();
        List<GarbageCollectorMXBean> gcs = ManagementFactory.getGarbageCollectorMXBeans();

        vo.setHeapInit(getMb(heapMemoryUsage.getInit()));
        vo.setHeapCommitted(getMb(heapMemoryUsage.getCommitted()));
        vo.setHeaoUsed(getMb(heapMemoryUsage.getUsed()));
        vo.setHeapMax(getMb(heapMemoryUsage.getMax()));
        vo.setNoHeapInit(getMb(nonHeapMemoryUsage.getInit()));
        vo.setNoHeapCommitted(getMb(nonHeapMemoryUsage.getCommitted()));
        vo.setNoHeapUsed(getMb(nonHeapMemoryUsage.getUsed()));
        vo.setNoHeapMax(getMb(nonHeapMemoryUsage.getMax()));
        vo.setThreadTotal(threads.getThreadCount());
        vo.setThreadDaemon(threads.getDaemonThreadCount());
        vo.setThreadPeak(threads.getPeakThreadCount());
        vo.setThreadTotalStart(threads.getTotalStartedThreadCount());
        vo.setClassTotalLoad(cl.getTotalLoadedClassCount());
        vo.setClassCurrentLoad(cl.getLoadedClassCount());
        vo.setClassUnload(cl.getUnloadedClassCount());
        long fullCount = 0, fullTime = 0, youngCount = 0, youngTime = 0;
        for (GarbageCollectorMXBean gc : gcs) {
            switch (gc.getName()) {
                case "PS Scavenge":
                    youngCount += gc.getCollectionCount();
                    youngTime += gc.getCollectionTime();
                    break;
                case "PS MarkSweep":
                    fullCount += gc.getCollectionCount();
                    fullTime += gc.getCollectionTime();
                    break;
                default:
                    break;
            }
        }
        vo.setFullCount(fullCount);
        vo.setFullTime(fullTime);
        vo.setYoungCount(youngCount);
        vo.setYoungTime(youngTime);
        return vo;
    }

    private BigDecimal getMb(Long bytes) {
        return new BigDecimal(bytes).divide(new BigDecimal(1024 * 1024), 2, BigDecimal.ROUND_HALF_DOWN);
    }
}

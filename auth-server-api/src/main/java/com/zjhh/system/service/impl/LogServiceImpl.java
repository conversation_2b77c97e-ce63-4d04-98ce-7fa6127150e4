package com.zjhh.system.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.db.comm.Page;
import com.zjhh.system.dao.entity.LogApi;
import com.zjhh.system.dao.mapper.LogApiMapper;
import com.zjhh.system.request.PageLogReq;
import com.zjhh.system.service.LogService;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2020/6/19 10:41
 */
@Service
public class LogServiceImpl implements LogService {

    @Resource
    private LogApiMapper logApiMapper;

    @Async
    @Override
    public void log(LogApi logApi) {
        logApiMapper.insert(logApi);
    }

    @Override
    public Page<LogApi> pageLog(PageLogReq req) {
        Page<LogApi> page = new Page<>();
        page.setCurrent(req.getCurrent());
        page.setSize(req.getSize());
        QueryWrapper<LogApi> wrapper = new QueryWrapper<>();
        if (Objects.nonNull(req.getType())) {
            wrapper.lambda().eq(LogApi::getLogType, req.getType());
        }
        if (StrUtil.isNotBlank(req.getKey())) {
            wrapper.lambda()
                    .and(wp -> wp.like(LogApi::getLoginName, req.getKey()).or().like(LogApi::getUserName, req.getKey()));
        }
        if (StrUtil.isNotBlank(req.getBeginTime())) {
            wrapper.lambda().ge(LogApi::getGmtCreate, DateUtil.parseLocalDateTime(req.getBeginTime() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN));
        }
        if (StrUtil.isNotBlank(req.getEndTime())) {
            wrapper.lambda().le(LogApi::getGmtCreate, DateUtil.parseLocalDateTime(req.getEndTime() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN));
        }
        wrapper.lambda().orderByDesc(LogApi::getGmtCreate);
        return logApiMapper.selectPage(page, wrapper);
    }
}

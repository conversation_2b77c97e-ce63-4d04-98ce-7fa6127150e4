package com.zjhh.system.utils;

import cn.hutool.core.util.ObjectUtil;
import com.zjhh.system.dao.entity.QuartzJob;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;

/**
 * 定时任务工具类
 *
 * <AUTHOR>
 * @since 2021/3/10 17:42
 */
@Slf4j
public class QuartzUtil {

    /**
     * 创建定时任务 定时任务创建之后默认启动状态
     *
     * @param scheduler
     * @param job
     */
    public static void createScheduleJob(Scheduler scheduler, QuartzJob job) {
        try {
            Class<? extends Job> jobClass = (Class<? extends Job>) Class.forName(job.getJobClass());
            JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(job.getJobKey()).build();
            if (ObjectUtil.isNotNull(job.getDataMap())) {
                jobDetail.getJobDataMap().putAll(job.getDataMap());
            }
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(job.getCronExpression());
            CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity(job.getJobKey()).withSchedule(scheduleBuilder).build();
            scheduler.scheduleJob(jobDetail, trigger);
        } catch (ClassNotFoundException e) {
            log.error("定时任务类路径出错：请输入类的绝对路径");
        } catch (SchedulerException e) {
            log.error("创建定时任务出错", e);
        }
    }

    /**
     * 根据任务名称暂停定时任务
     *
     * @param scheduler 调度器
     * @param key       定时任务名称
     * @throws SchedulerException
     */
    public static void pauseScheduleJob(Scheduler scheduler, String key) {
        JobKey jobKey = JobKey.jobKey(key);
        try {
            scheduler.pauseJob(jobKey);
        } catch (SchedulerException e) {
            log.error("暂停定时任务出错", e);
        }
    }

    /**
     * 根据任务名称恢复定时任务
     *
     * @param scheduler 调度器
     * @param key       定时任务名称
     * @throws SchedulerException
     */
    public static void resumeScheduleJob(Scheduler scheduler, String key) {
        JobKey jobKey = JobKey.jobKey(key);
        try {
            scheduler.resumeJob(jobKey);
        } catch (SchedulerException e) {
            log.error("启动定时任务出错", e);
        }
    }

    /**
     * 根据任务名称立即运行一次定时任务
     *
     * @param scheduler 调度器
     * @param key       定时任务名称
     * @throws SchedulerException
     */
    public static void runOnce(Scheduler scheduler, String key) {
        JobKey jobKey = JobKey.jobKey(key);
        try {
            scheduler.triggerJob(jobKey);
        } catch (SchedulerException e) {
            log.error("运行定时任务出错", e);
        }
    }

    /**
     * 更新定时任务
     *
     * @param scheduler 调度器
     * @param job       定时任务信息类
     * @throws SchedulerException
     */
    public static void updateScheduleJob(Scheduler scheduler, QuartzJob job) {
        try {
            //获取到对应任务的触发器
            TriggerKey triggerKey = TriggerKey.triggerKey(job.getJobKey());
            //设置定时任务执行方式
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(job.getCronExpression());
            //重新构建任务的触发器trigger
            CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
            trigger = trigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(scheduleBuilder).build();
            //重置对应的job
            scheduler.rescheduleJob(triggerKey, trigger);
        } catch (SchedulerException e) {
            log.error("更新定时任务出错", e);
        }
    }

    /**
     * 判断定时任务是否存在
     *
     * @param scheduler
     * @param key
     * @return
     */
    public static Boolean checkScheduleJob(Scheduler scheduler, String key) {
        try {
            return scheduler.checkExists(JobKey.jobKey(key));
        } catch (SchedulerException e) {
            return false;
        }
    }

    /**
     * 根据定时任务名称从调度器当中删除定时任务
     *
     * @param scheduler 调度器
     * @param key       定时任务名称
     * @throws SchedulerException
     */
    public static void deleteScheduleJob(Scheduler scheduler, String key) {
        JobKey jobKey = JobKey.jobKey(key);
        try {
            scheduler.deleteJob(jobKey);
        } catch (SchedulerException e) {
            log.error("删除定时任务出错", e);
        }
    }
}

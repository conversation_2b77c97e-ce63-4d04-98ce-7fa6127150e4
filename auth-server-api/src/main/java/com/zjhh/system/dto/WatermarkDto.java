package com.zjhh.system.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/3 11:44
 */
@Data
public class WatermarkDto implements Serializable {

    private static final long serialVersionUID = 8930034583814732387L;

    private String text;

    private Boolean username;

    private Boolean date;

    public List<String> getWatermarkStr(String userName) {
        List<String> list = new ArrayList<>();
        if (StrUtil.isNotBlank(text)) {
            list.add(text);
        }
        if (username != null && username) {
            list.add(userName);
        }
        if (date != null && date) {
            list.add(DateUtil.format(LocalDateTime.now(), DatePattern.CHINESE_DATE_PATTERN));
        }
        return list;
    }
}

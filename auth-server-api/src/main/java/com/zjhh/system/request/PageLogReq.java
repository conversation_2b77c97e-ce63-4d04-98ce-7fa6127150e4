package com.zjhh.system.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/3/4 14:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageLogReq extends PageReq {

    private static final long serialVersionUID = -7698982238972677711L;

    @Schema(description = "操作类型")
    private Integer type;

    @Schema(description = "关键字")
    private String key;

    @Schema(description = "开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private String beginTime;

    @Schema(description = "结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private String endTime;
}

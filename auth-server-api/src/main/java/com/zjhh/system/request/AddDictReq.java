package com.zjhh.system.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/3/17 9:23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddDictReq extends BaseReq {

    private static final long serialVersionUID = 3510596659199244076L;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "值")
    private String value;

    @Schema(description = "描述")
    private String description;
}

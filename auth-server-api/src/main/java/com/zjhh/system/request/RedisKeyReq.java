package com.zjhh.system.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/3/4 17:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RedisKeyReq extends BaseReq {

    private static final long serialVersionUID = 4107327265194494056L;

    @Schema(description = "redis关键字")
    @NotBlank(message = "key不能为空！")
    private String key;
}

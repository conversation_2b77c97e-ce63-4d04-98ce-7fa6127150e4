package com.zjhh.system.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/3/16 16:24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageSearchReq extends PageReq {

    private static final long serialVersionUID = 1636663339224545870L;

    @Schema(description = "搜索关键字")
    private String searchKey;
}

package com.zjhh.system.request;

import com.zjhh.comm.request.IdReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/3/10 17:59
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateQuartzJobReq extends IdReq {

    private static final long serialVersionUID = 2877434027098999218L;

    @Schema(description = "定时任务类的绝对路径")
    @NotBlank(message = "定时任务类的绝对路径不能为空")
    private String jobClass;

    @Schema(description = "任务运行时间表达式")
    @NotBlank(message = "任务运行时间表达式不能为空")
    private String cronExpression;

    @Schema(description = "描述")
    private String description;
}

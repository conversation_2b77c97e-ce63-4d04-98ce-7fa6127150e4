package com.zjhh.system.request;

import com.zjhh.comm.request.IdReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/3/16 17:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateDictReq extends IdReq {

    private static final long serialVersionUID = -2016513809801326180L;

    @Schema(description = "值")
    private String value;

    @Schema(description = "描述")
    private String description;
}

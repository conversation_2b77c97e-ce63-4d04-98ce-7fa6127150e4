package com.zjhh.system.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/10 17:55
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddQuartzJobReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 264125651419365139L;

    @Schema(description = "定时任务类的绝对路径")
    @NotBlank(message = "定时任务类的绝对路径不能为空")
    private String jobClass;

    @Schema(description = "定时任务是否启动")
    @NotNull
    private Boolean start;

    @Schema(description = "任务运行时间表达式")
    @NotBlank(message = "任务运行时间表达式不能为空")
    private String cronExpression;

    @Schema(description = "描述")
    @Size(max = 200, message = "描述不能超过200个字")
    private String description;
}

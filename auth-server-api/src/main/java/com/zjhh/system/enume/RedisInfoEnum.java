package com.zjhh.system.enume;

/**
 * <AUTHOR>
 * @since 2021/3/4 17:41
 */
public enum RedisInfoEnum {

    CLIENT_LONGEST_OUTPUT_LIST("client_longest_output_list", "当前连接的客户端当中，最长的输出列表"),

    RDB_BGSAVE_IN_PROGRESS("rdb_bgsave_in_progress", "服务器是否正在创建rdb文件"),

    GCC_VERSION("gcc_version", "编译 Redis 时所使用的 GCC 版本"),

    USED_CPU_USER_CHILDREN("used_cpu_user_children", "将后台进程在用户态所占用的CPU时求和累计起来"),

    USED_MEMORY_LUA("used_memory_lua", "Lua 引擎所使用的内存大小（以字节为单位）"),

    USED_MEMORY("used_memory", "由 Redis 分配器分配的内存总量，以字节（byte）为单位"),

    REDIS_VERSION("redis_version", "Redis 服务器版本"),

    UPTIME_IN_SECONDS("uptime_in_seconds", "自 Redis 服务器启动以来，经过的秒数"),

    EXPIRED_KEYS("expired_keys", "运行以来过期的key的数量"),

    REPL_BACKLOG_SIZE("repl_backlog_size", "复制积压缓冲大小"),

    REJECTED_CONNECTIONS("rejected_connections", "拒绝的连接个数，redis连接个数达到maxclients限制，拒绝新连接的个数"),

    ARCH_BITS("arch_bits", "架构（32 或 64 位）"),

    USED_MEMORY_HUMAN("used_memory_human", "以人类可读的格式返回 Redis 分配的内存总量"),

    INSTANTANEOUS_OPS_PER_SEC("instantaneous_ops_per_sec", "redis当前的qps，redis内部较实时的每秒执行的命令数"),

    AOF_CURRENT_REWRITE_TIME_SEC("aof_current_rewrite_time_sec", "如果rewrite操作正在进行，则记录所使用的时间，单位秒"),

    LRU_CLOCK("lru_clock", "以分钟为单位进行自增的时钟，用于 LRU 管理"),

    REDIS_GIT_SHA1("redis_git_sha1", "Git SHA1"),

    LATEST_FORK_USEC("latest_fork_usec", "最近一次fork操作阻塞redis进程的耗时数，单位微秒"),

    USED_CPU_SYS("used_cpu_sys", "将所有redis主进程在核心态所占用的CPU时求和累计起来"),

    USED_CPU_USER("used_cpu_user", "将所有redis主进程在用户态所占用的CPU时求和累计起来"),

    KEYSPACE_HITS("keyspace_hits", "命中次数"),

    PROCESS_ID("process_id", "服务器进程的 PID"),

    REPL_BACKLOG_HISTLEN("repl_backlog_histlen", "此值等于 master_repl_offset - repl_backlog_first_byte_offset,该值不会超过repl_backlog_size的大小"),

    MASTER_REPL_OFFSET("master_repl_offset", "主从同步偏移量,此值如果和上面的offset相同说明主从一致没延迟"),

    REDIS_MODE("redis_mode", "运行模式，单机（standalone）或者集群（cluster）"),

    MEM_ALLOCATOR("mem_allocator", "在编译时指定的， Redis 所使用的内存分配器。可以是 libc 、 jemalloc 或者 tcmalloc"),

    REDIS_BUILD_ID("redis_build_id", "redis_build_id"),

    AOF_REWRITE_IN_PROGRESS("aof_rewrite_in_progress", "标识aof的rewrite操作是否在进行中"),

    PUBSUB_CHANNELS("pubsub_channels", "当前使用中的频道数量"),

    EVICTED_KEYS("evicted_keys", "运行以来剔除(超过了maxmemory后)的key的数量"),

    RDB_LAST_BGSAVE_STATUS("rdb_last_bgsave_status", "最近一次rdb持久化是否成功"),

    RDB_CURRENT_BGSAVE_TIME_SEC("rdb_current_bgsave_time_sec", "如果服务器正在创建rdb文件，那么这个域记录的就是当前的创建操作已经耗费的秒数"),

    USED_MEMORY_PEAK_HUMAN("used_memory_peak_human", "以人类可读的格式返回 Redis 的内存消耗峰值"),

    AOF_LAST_BGREWRITE_STATUS("aof_last_bgrewrite_status", "上次bgrewrite aof操作的状态"),

    UPTIME_IN_DAYS("uptime_in_days", "自 Redis 服务器启动以来，经过的天数"),

    KEYSPACE_MISSES("keyspace_misses", "没命中次数"),

    AOF_ENABLED("aof_enabled", "是否开启了aof"),

    CLIENT_BIGGEST_INPUT_BUF("client_biggest_input_buf", "当前连接的客户端当中，最大输入缓存，用client list命令观察qbuf和qbuf-free两个字段最大值"),

    RDB_LAST_BGSAVE_TIME_SEC("rdb_last_bgsave_time_sec", "最近一次成功生成rdb文件耗时秒数"),

    BLOCKED_CLIENTS("blocked_clients", "正在等待阻塞命令（BLPOP、BRPOP、BRPOPLPUSH）的客户端的数量"),

    OS("os", "Redis 服务器的宿主操作系统"),

    AOF_REWRITE_SCHEDULED("aof_rewrite_scheduled", "rewrite任务计划，当客户端发送bgrewriteaof指令，如果当前rewrite子进程正在执行，那么将客户端请求的bgrewriteaof变为计划任务，待aof子进程结束后执行rewrite"),

    REPL_BACKLOG_ACTIVE("repl_backlog_active", "复制积压缓冲区是否开启"),

    ROLE("role", "实例的角色，是master or slave"),

    USED_MEMORY_PEAK("used_memory_peak", "Redis 的内存消耗峰值(以字节为单位)"),

    RUN_ID("run_id", "Redis 服务器的随机标识符（用于 Sentinel 和集群）"),

    USED_CPU_SYS_CHILDREN("used_cpu_sys_children", "将后台进程在核心态所占用的CPU时求和累计起来"),

    MULTIPLEXING_API("multiplexing_api", "Redis 所使用的事件处理机制"),

    PUBSUB_PATTERNS("pubsub_patterns", "当前使用的模式的数量"),

    SYNC_PARTIAL_ERR("sync_partial_err", "主从部分同步失败次数"),

    TOTAL_COMMANDS_PROCESSED("total_commands_processed", "redis处理的命令数"),

    HZ("hz", "redis内部调度（进行关闭timeout的客户端，删除过期key等等）频率，程序规定serverCron每秒运行10次。"),

    SYNC_FULL("sync_full", "主从完全同步成功次数"),

    TOTAL_CONNECTIONS_RECEIVED("total_connections_received", "新创建连接个数,如果新创建连接过多，过度地创建和销毁连接对性能有影响，说明短连接严重或连接池使用有问题，需调研代码的连接设置"),

    AOF_LAST_REWRITE_TIME_SEC("aof_last_rewrite_time_sec", "最近一次aof rewrite耗费的时长"),

    TCP_PORT("tcp_port", "TCP/IP 监听端口"),

    REDIS_GIT_DIRTY("redis_git_dirty", "Git dirty flag"),

    USED_MEMORY_RSS("used_memory_rss", "从操作系统的角度，返回 Redis 已分配的内存总量（俗称常驻集大小）。这个值和 top 、 ps 等命令的输出一致"),

    MEM_FRAGMENTATION_RATIO("mem_fragmentation_ratio", "sed_memory_rss 和 used_memory 之间的比率"),

    RDB_CHANGES_SINCE_LAST_SAVE("rdb_changes_since_last_save", "离最近一次成功生成rdb文件，写入命令的个数，即有多少个写入命令没有持久化"),

    LOADING("loading", "服务器是否正在载入持久化文件"),

    CONNECTED_CLIENTS("connected_clients", "已连接客户端的数量（不包括通过从属服务器连接的客户端）"),

    SYNC_PARTIAL_OK("sync_partial_ok", "主从部分同步成功次数"),

    RDB_LAST_SAVE_TIME("rdb_last_save_time", "离最近一次成功创建rdb文件的时间戳。当前时间戳 - rdb_last_save_time=多少秒未成功生成rdb文件");

    private final String key;

    private final String description;

    RedisInfoEnum(String key, String description) {
        this.key = key;
        this.description = description;
    }

    public String key() {
        return this.key;
    }

    public String description() {
        return this.description;
    }

}

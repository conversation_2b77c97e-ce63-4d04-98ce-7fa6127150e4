package com.zjhh.system.dao.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.localdatetime.LocalDateTimeDateConverter;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.db.dao.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 系统参数配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_dict")
public class Dict extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "编码", index = 0)
    private String code;

    @ExcelProperty(value = "值", index = 1)
    private String value;

    /**
     * 描述
     */
    @Schema(description = "描述")
    @ExcelProperty(value = "描述", index = 2)
    private String description;

    /**
     * 1-文本输入框 2-是否选择框 3-水印
     */
    @Schema(description = "1-文本输入框 2-是否选择框 3-水印")
    @ExcelIgnore
    private Integer type;

    @ExcelIgnore
    private Boolean preShow;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间", index = 3, converter = LocalDateTimeDateConverter.class)
    private LocalDateTime gmtCreate;
}

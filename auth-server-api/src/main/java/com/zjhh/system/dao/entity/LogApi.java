package com.zjhh.system.dao.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.db.dao.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_log_api")
public class LogApi extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Schema(description = "编码")
    @ExcelIgnore
    private String code;

    /**
     * 用户编码
     */
    @Schema(hidden = true)
    @ExcelIgnore
    private String userCode;

    /**
     * 登录类型 1：登录日志 2：登出日志 3：操作日志
     */
    @Schema(description = "日志类型：1：登录日志 2：登出日志 3：操作日志")
    @ExcelIgnore
    private Integer logType;

    /**
     * 执行方法
     */
    @Schema(description = "执行方法")
    @ExcelProperty(value = "执行方法", index = 2)
    private String method;

    @Schema(description = "url")
    @ExcelIgnore
    private String url;

    @Schema(description = "ip")
    @ExcelProperty(value = "ip", index = 4)
    private String ip;

    /**
     * 总执行时间
     */
    @Schema(description = "执行时间")
    @ExcelProperty(value = "执行时间(ms)", index = 3)
    private Long totalTime;

    /**
     * 用户姓名
     */
    @Schema(description = "用户姓名")
    @ExcelProperty(value = "用户姓名", index = 0)
    private String userName;

    /**
     * 用户登录账号
     */
    @Schema(description = "用户账号")
    @ExcelProperty(value = "用户账号", index = 1)
    private String loginName;

    @Schema(description = "操作内容")
    @ExcelProperty(value = "日志类型", index = 5)
    private String operator;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间", index = 6)
    private LocalDateTime gmtCreate;
}

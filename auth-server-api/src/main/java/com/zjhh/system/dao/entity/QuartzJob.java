package com.zjhh.system.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjhh.db.dao.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * <p>
 * 定时任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("quartz_job")
public class QuartzJob extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Schema(description = "任务编码")
    private String jobKey;

    @Schema(description = "任务类名")
    private String jobClass;

    /**
     * 是否启动
     */
    @Schema(description = "状态：是否启动")
    private Boolean start;

    /**
     * 任务运行时间表达式
     */
    @Schema(description = "cron表达式")
    private String cronExpression;

    @Schema(description = "描述")
    private String description;

    @TableField(exist = false)
    private Map<String, Object> dataMap;
}

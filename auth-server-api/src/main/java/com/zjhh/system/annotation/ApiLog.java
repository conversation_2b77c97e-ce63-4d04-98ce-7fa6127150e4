package com.zjhh.system.annotation;

import com.zjhh.system.enume.LogApiTypeEnum;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @since 2020/6/19 11:20
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiLog {

    /**
     * 操作名称
     *
     * @return
     */
    String value() default "";

    /**
     * 操作类型
     *
     * @return
     */
    LogApiTypeEnum type() default LogApiTypeEnum.OPERATE;
}

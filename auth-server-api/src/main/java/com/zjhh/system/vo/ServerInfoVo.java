package com.zjhh.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/19 12:06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServerInfoVo extends BaseVo {

    private static final long serialVersionUID = 7210359048666267705L;

    /**
     * cpu核心数
     */
    @Schema(description = "cpu核心数")
    private Integer cpuNum;

    /**
     * CPU总的使用率
     */
    @Schema(description = "CPU总的使用率")
    private double toTal;

    /**
     * CPU系统使用率
     */
    @Schema(description = "CPU系统使用率")
    private double sys;

    /**
     * CPU用户使用率
     */
    @Schema(description = "CPU用户使用率")
    private double used;

    /**
     * CPU当前等待率
     */
    @Schema(description = "CPU当前等待率")
    private double wait;

    /**
     * CPU当前空闲率
     */
    @Schema(description = "CPU当前空闲率")
    private double free;

    /**
     * CPU型号信息
     */
    @Schema(description = "CPU型号信息")
    private String cpuModel;

    @Schema(description = "总内存")
    private long memoryTotal;

    @Schema(description = "可用内存")
    private long memoryAvailable;

    @Schema(description = "当前时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime currentTime;

    @Schema(description = "磁盘信息")
    private List<DiskInfo> diskInfos;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class DiskInfo extends BaseVo {

        private static final long serialVersionUID = -708969322122205907L;

        @Schema(description = "磁盘名称和类型")
        private String model;

        @Schema(description = "磁盘大小GB")
        private Long size;

        @Schema(description = "分区信息")
        private List<PartitionInfoVo> partitionInfoVos;

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class PartitionInfoVo extends BaseVo {

        private static final long serialVersionUID = -2674795794763583028L;

        @Schema(description = "分区")
        private String mountPoint;

        @Schema(description = "大小GB")
        private Long size;
    }
}

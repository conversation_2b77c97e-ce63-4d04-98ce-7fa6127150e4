package com.zjhh.system.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021/3/19 15:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JvmInfoVo extends BaseVo {

    private static final long serialVersionUID = -5750984567984549714L;

    @Schema(description = "初始化堆内存")
    private BigDecimal heapInit;

    @Schema(description = "已使用堆内存")
    private BigDecimal heaoUsed;

    @Schema(description = "可使用堆内存")
    private BigDecimal heapCommitted;

    @Schema(description = "最大堆内存")
    private BigDecimal heapMax;

    @Schema(description = "初始化非堆内存")
    private BigDecimal noHeapInit;

    @Schema(description = "已使用非堆内存")
    private BigDecimal noHeapUsed;

    @Schema(description = "可使用非堆内存")
    private BigDecimal noHeapCommitted;

    @Schema(description = "最大非堆内存")
    private BigDecimal noHeapMax;

    @Schema(description = "总线程数(守护+非守护)")
    private Integer threadTotal;

    @Schema(description = "守护线程线程数")
    private Integer threadDaemon;

    @Schema(description = "峰值线程数")
    private Integer threadPeak;

    @Schema(description = "Java虚拟机启动后创建并启动的线程总数")
    private Long threadTotalStart;

    @Schema(description = "当前加载类数量")
    private Integer classCurrentLoad;

    @Schema(description = "未加载类数量")
    private Long classUnload;

    @Schema(description = "总加载类数量")
    private Long classTotalLoad;

    @Schema(description = "fullgc次数")
    private Long fullCount;

    @Schema(description = "fullgc总时间")
    private Long fullTime;

    @Schema(description = "younggc次数")
    private Long youngCount;

    @Schema(description = "younggc总时间")
    private Long youngTime;
}

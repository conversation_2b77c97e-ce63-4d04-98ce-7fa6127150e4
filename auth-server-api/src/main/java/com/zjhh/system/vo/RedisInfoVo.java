package com.zjhh.system.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/3/4 17:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RedisInfoVo extends BaseVo {

    private static final long serialVersionUID = -6934897215739801894L;

    @Schema(description = "关键字")
    private String key;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "值")
    private String value;
}

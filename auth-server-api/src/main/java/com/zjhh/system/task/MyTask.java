package com.zjhh.system.task;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2021/3/10 18:24
 */
public class MyTask extends QuartzJobBean {

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        System.out.println("简单的定时任务执行时间：" + LocalDateTime.now());
    }
}

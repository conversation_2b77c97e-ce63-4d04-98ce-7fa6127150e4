package com.zjhh.user.config;

/**
 * <AUTHOR>
 * @since 2021/7/20 17:10
 */
public class BaseThreadLocal {

    private static final ThreadLocal<String> thirdLoginToken = new ThreadLocal<>();

    public static String getThirdLoginToken() {
        return thirdLoginToken.get();
    }

    public static void setThirdLoginToken(String token) {
        thirdLoginToken.set(token);
    }

    public static void cleanThirdLoginToken() {
        thirdLoginToken.remove();
    }
}

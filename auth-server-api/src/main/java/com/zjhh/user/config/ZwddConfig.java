package com.zjhh.user.config;

import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.zjhh.comm.config.SystemProperties;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by lmh
 */
@Configuration
public class ZwddConfig {

    @Resource
    private SystemProperties systemProperties;

    @Bean("executableClient")
    public ExecutableClient executableClient() {
        ExecutableClient executableClient = ExecutableClient.getInstance();
        executableClient.setAccessKey(systemProperties.getZwddAuth().getAppKey());
        executableClient.setSecretKey(systemProperties.getZwddAuth().getAppSecret());
        executableClient.setDomainName(systemProperties.getZwddAuth().getDomainName());
        executableClient.setProtocal(systemProperties.getZwddAuth().getProtocal());
        executableClient.init();

        return executableClient;
    }
}

package com.zjhh.user.config;

import cn.dev33.satoken.stp.StpInterface;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/4/13
 */
@Component
public class StpInterfaceImpl implements StpInterface {

    @Resource
    private UserSession userSession;

    @Override
    public List<String> getPermissionList(Object loginId, String loginKey) {
        List<String> list = userSession.getAllPermission();
        list.addAll(userSession.getSessionLoginVo().getRoles());
        return list;
    }

    @Override
    public List<String> getRoleList(Object loginId, String loginKey) {
        return userSession.getSessionLoginVo().getRoles();
    }
}

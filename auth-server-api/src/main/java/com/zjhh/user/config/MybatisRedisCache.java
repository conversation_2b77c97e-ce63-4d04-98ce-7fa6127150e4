package com.zjhh.user.config;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.zjhh.cache.config.CacheStore;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cache.Cache;
import org.springframework.data.redis.connection.RedisServerCommands;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021/8/25 15:07
 */
@Slf4j
public class MybatisRedisCache implements Cache {

    private String id;

    public MybatisRedisCache(final String id) {
        this.id = id;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public void putObject(Object key, Object value) {
        if (ObjectUtil.isNotNull(value)) {
            getSqlCache().put(keyGen(key.toString()), value);
        }
    }

    @Override
    public Object getObject(Object key) {
        if (ObjectUtil.isNotNull(key)) {
            return getSqlCache().get(keyGen(key.toString()));
        }
        return null;
    }

    @Override
    public Object removeObject(Object key) {
        return getSqlCache().remove(keyGen(key.toString()));
    }

    @Override
    public void clear() {
        Set<String> set = getStringRedisTemplate().keys("*" + this.id + "*");
        getSqlCache().removeAll(set);
    }

    @Override
    public int getSize() {
        Long size = getStringRedisTemplate().execute(RedisServerCommands::dbSize);
        return ObjectUtil.isNull(size) ? 0 : size.intValue();
    }

    public StringRedisTemplate getStringRedisTemplate() {
        return SpringUtil.getBean(StringRedisTemplate.class);
    }

    public com.alicp.jetcache.Cache<String, Object> getSqlCache() {
        return SpringUtil.getBean(CacheStore.class).getSqlCache();
    }

    private String keyGen(String key) {
        return this.id + ":" + SecureUtil.md5(this.id + key);
    }
}

package com.zjhh.user.config;

import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/3/18 上午10:34
 */
public class MyStpLogic extends StpLogic {

    public MyStpLogic() {
        super(StpUtil.TYPE);
    }

    @Override
    public String createTokenValue(Object loginId, String device, long timeout, Map<String, Object> extraData) {
        // 先尝试获取本地 token
        String token = BaseThreadLocal.getThirdLoginToken();
        if (StrUtil.isBlank(token)) {
            token = super.createTokenValue(loginId, device, timeout, extraData);
        }
        // 清理本地 token 存储
        BaseThreadLocal.cleanThirdLoginToken();
        return token;
    }
}

package com.zjhh.user.service;


import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.zjhh.db.comm.Page;
import com.zjhh.user.dao.entity.User;
import com.zjhh.user.request.*;
import com.zjhh.user.vo.LoginVo;
import com.zjhh.user.vo.MobileUserDetailVo;
import com.zjhh.user.vo.UserDetailVo;
import com.zjhh.user.vo.UserVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/6/11 16:50
 */
public interface UserService {

    /**
     * 获取用户列表
     *
     * @param req
     * @return
     */
    Page<UserVo> pageUser(PageUserReq req);

    /**
     * 添加用户
     *
     * @param req
     * @return
     */
    void addUser(AddUserReq req);


    String addUserReturnCode(AddUserReq req);

    /**
     * 获取用户详情
     *
     * @param userId
     * @return
     */
    UserDetailVo getUserDetail(String userId);

    /**
     * 验证登录名是否存在
     *
     * @param loginName
     * @return
     */
    Boolean checkLoginName(String loginName);

    /**
     * 修改用户
     *
     * @param req
     * @return
     */
    void updateUser(UpdateUserReq req);

    /**
     * 删除用户
     *
     * @param userIds
     * @return
     */
    void deleteBatchUser(List<String> userIds);

    /**
     * 锁定用户
     *
     * @param userId
     * @return
     */
    void lockUser(String userId);

    /**
     * 解锁用户
     *
     * @param userId
     * @return
     */
    void unlockUser(String userId);

    /**
     * 重置密码
     *
     * @param req
     * @return
     */
    void resetPassword(ResetPasswordReq req);

    /**
     * 获取在线用户
     *
     * @return
     */
    List<User> listOnlineUser();

    /**
     * 修改密码
     *
     * @param req
     * @return
     */
    void updatePassword(UpdatePasswordReq req);

    /**
     * 保存主题配置
     *
     * @param config
     */
    void saveTheme(String config);

    /**
     * 获取个人中心数据
     *
     * @return
     */
    User getUserProfile();

    /**
     * 保存个人信息
     *
     * @param req
     */
    LoginVo saveUserProfile(SaveProfileReq req);

    /**
     * 根据用户名模糊查询用户code列表
     *
     * @param username
     * @return
     */
    List<String> listUserCodeByName(String username);

    /**
     * 获取用户列表
     *
     * @param userCodes
     * @param properties
     * @return
     */
    List<User> getUsers(List<String> userCodes, SFunction<User, ?>... properties);

    User getSuperUser();

    /**
     * 修改移动端信息
     *
     * @param req
     */
    void updateMobileUser(UpdateMobileUserReq req);

    /**
     * 获取移动端用户详情
     *
     * @return
     */
    MobileUserDetailVo getMobileUserDetail();

    String getUserZwddAccountId(String userCode);

    /**
     * 获取组织结构所有子节点
     *
     * @param areaCode
     * @return
     */
    List<String> getSelectAreaAllChild();

    /**
     * 判断是否绑定浙政钉
     *
     * @param userCodes
     * @return
     */
    List<User> checkZzdStatus(List<String> userCodes);

    /**
     * 获取用户信息
     *
     * @param userCode
     * @param columns
     * @return
     */
    User getUser(String userCode, SFunction<User, ?> columns);

    List<String> checkUserCode(List<String> userCodes);
}

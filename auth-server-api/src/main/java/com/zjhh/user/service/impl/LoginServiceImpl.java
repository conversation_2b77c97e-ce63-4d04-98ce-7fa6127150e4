package com.zjhh.user.service.impl;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.config.SystemProperties;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.vo.LicenceValidVo;
import com.zjhh.system.service.DictService;
import com.zjhh.user.config.BaseThreadLocal;
import com.zjhh.user.dao.entity.*;
import com.zjhh.user.dao.mapper.*;
import com.zjhh.user.request.FOALoginReq;
import com.zjhh.user.request.LoginReq;
import com.zjhh.user.request.OpenLoginReq;
import com.zjhh.user.request.StThirdLoginReq;
import com.zjhh.user.service.LoginService;
import com.zjhh.user.service.ZwddApiService;
import com.zjhh.user.utils.Encrypt;
import com.zjhh.user.vo.*;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020/6/12 10:46
 */
@DS("master")
@Slf4j
@Service
public class LoginServiceImpl implements LoginService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserSession userSession;

    @Resource
    private DictService dictService;

    @Resource
    private ZwddApiService zwddApiService;

    @Resource
    private ZwddUserRelationMapper zwddUserRelationMapper;

    @Resource
    private SystemProperties systemProperties;

    @Resource
    private OpenLoginMapper openLoginMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private OrganizeUserMapper organizeUserMapper;

    @Resource
    private VOrganizeMapper vOrganizeMapper;

    @Resource
    private FoaLoginMapper foaLoginMapper;

    private static final Encrypt ENCRYPT = new Encrypt();

    @Resource
    private CacheManager cacheManager;

    private Cache<String, String> captchaCache;

    private Cache<String, Integer> passwordErrorCache;

    @PostConstruct
    public void init() {
        QuickConfig captcha = QuickConfig.newBuilder("captcha:")
                .expire(Duration.ofSeconds(600))
                .cacheType(CacheType.REMOTE)
                .syncLocal(false)
                .build();
        captchaCache = cacheManager.getOrCreateCache(captcha);

        QuickConfig passwordError = QuickConfig.newBuilder("passwordError:")
                .expire(Duration.ofSeconds(600))
                .cacheType(CacheType.REMOTE)
                .syncLocal(false)
                .build();
        passwordErrorCache = cacheManager.getOrCreateCache(passwordError);
    }

    @Override
    public LoginVo login(LoginReq req) {
        return baseLogin(req, LoginType.PC);
    }

    @Override
    public void logout() {
        userSession.logout();
    }

    @Override
    public CaptchaVo createCaptcha() {
        LineCaptcha captcha = CaptchaUtil.createLineCaptcha(105, 35, 4, 10);
        String imageKey = String.valueOf(System.currentTimeMillis());
        captchaCache.put(imageKey, captcha.getCode());
        CaptchaVo vo = new CaptchaVo();
        vo.setImageKey(imageKey);
        vo.setImageData(captcha.getImageBase64Data());
        return vo;
    }

    @Override
    public InitConfigVo getInitConfig() {
        InitConfigVo vo = new InitConfigVo();
        vo.setNeedCaptcha(dictService.needCaptcha());
        vo.setSystemName(dictService.getSystemName());
        vo.setAdminName(dictService.getAdminName());
        String tip = "";
        try {
            LicenceValidVo validVo = userSession.getLicence();
            if (Objects.nonNull(validVo) && Objects.nonNull(validVo.getLicenceData())) {
                vo.setLicenceValid(userSession.checkLicence());
                String expire = validVo.getLicenceData().getExpireDate();
                vo.setExpireDate(expire);
                Date expireDate = DateUtil.parseDate(expire);
                long days = DateUtil.betweenDay(expireDate, new Date(), true);
                if (days < 30) {
                    tip = "该证书有效期至" + expire + "，距离过期只剩下" + days + "天";
                }
            } else {
                vo.setLicenceValid(false);
                tip = "该证书错误，请重新上传证书！";
            }
        } catch (Exception e) {
            vo.setLicenceValid(false);
            tip = "该证书错误，请重新上传证书！";
        }
        vo.setLicenceTips(tip);
        return vo;
    }

    @Override
    public ZwddLoginVo getEmployeeCodeByQrCode(String code) {
        ZwddLoginUserInfo zwddLoginUserInfo = zwddApiService.getUserInfoByCode(code);
        if (zwddLoginUserInfo == null) {
            throw new BizException("当前用户无权查看，请联系系统管理员!");
        }
        String employeeCode = zwddLoginUserInfo.getEmployeeCode();
        if (StrUtil.isEmpty(employeeCode)) {
            throw new BizException("当前用户无权查看，请联系系统管理员!");
        }
        QueryWrapper<ZwddUserRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ZwddUserRelation::getEmployeeCode, employeeCode);
        ZwddUserRelation zwddUser = zwddUserRelationMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNull(zwddUser)) {
            throw new BizException("当前用户无权查看，请联系系统管理员!");
        }
        QueryWrapper<User> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.lambda().eq(User::getCode, zwddUser.getUserCode());
        User user = userMapper.selectOne(userQueryWrapper);
        if (user == null) {
            throw new BizException("当前用户无权查看，请联系系统管理员!");
        }
        if (user.getLocked()) {
            throw new BizException("用户已被锁定，无法登录，请联系管理员解锁！");
        }
        ZwddLoginVo zwddLoginVo = BeanUtil.copyProperties(userSession.login(user), ZwddLoginVo.class);
        zwddLoginVo.setAccountId(user.getMobile());
        return zwddLoginVo;
    }

    /**
     * 移动端免登录
     *
     * @param authCode
     * @return
     */
    @Override
    public ZwddLoginVo getEmployeeCodeByAuthCode(String authCode) {
        ZwddLoginUserInfo zwddLoginUserInfo = zwddApiService.getUserInfoByMobileCode(authCode);
        if (ObjectUtil.isNull(zwddLoginUserInfo)) {
            throw new BizException("当前用户无权查看，请联系系统管理员!");
        }
        QueryWrapper<ZwddUserRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ZwddUserRelation::getEmployeeCode, zwddLoginUserInfo.getEmployeeCode());
        ZwddUserRelation zwddUser = zwddUserRelationMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNull(zwddUser)) {
            throw new BizException("当前用户无权查看，请联系系统管理员!");
        }
        QueryWrapper<User> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.lambda().eq(User::getCode, zwddUser.getUserCode());
        User user = userMapper.selectOne(userQueryWrapper);
        if (ObjectUtil.isNull(user)) {
            throw new BizException("当前用户无权查看，请联系系统管理员!");
        }
        if (user.getLocked()) {
            throw new BizException("用户已被锁定，无法登录，请联系管理员解锁！");
        }
        ZwddLoginVo zwddLoginVo = BeanUtil.copyProperties(userSession.login(user), ZwddLoginVo.class);
        zwddLoginVo.setAccountId(user.getMobile());
        if (systemProperties.isMobile()) {
            zwddLoginVo.setSuggest(dictService.getSuggest());
        }
        return zwddLoginVo;

    }

    @Override
    public boolean fullSyncByMobile(List<String> mobileList) {
        return zwddApiService.fullSyncUserByMobileList(mobileList);
    }


    @Override
    public InitSystemVo getInitSystem() {
        InitSystemVo vo = new InitSystemVo();
        vo.setSystemArea(dictService.getSystemArea());
        return vo;
    }

    @Override
    public LoginVo mobileLogin(LoginReq req) {
        LoginVo vo = baseLogin(req, LoginType.MOBILE);
        vo.setSuggest(dictService.getSuggest());
        return vo;
    }

    @Override
    public LoginVo mobileConsoleLogin(LoginReq req) {
        return baseLogin(req, LoginType.MOBILE_CONSOLE);
    }

    @Override
    public LoginVo openLogin(OpenLoginReq req) {
        long now = System.currentTimeMillis();
        long s = (now - Long.parseLong(req.getTimestamp())) / 1000;
        if (s > 60) {
            throw new BizException("登录超时，请联系管理员！");
        }
        QueryWrapper<OpenLogin> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpenLogin::getAppKey, req.getAppKey());
        OpenLogin openLogin = openLoginMapper.selectOne(wrapper);
        if (ObjectUtil.isNull(openLogin)) {
            throw new BizException("该appKey不存在，请联系管理员！");
        }
        String md5 = SecureUtil.md5(req.getAppKey() + openLogin.getAppSecret() + req.getTimestamp());
        if (!StrUtil.equalsAnyIgnoreCase(md5, req.getSign())) {
            throw new BizException("签名错误，请联系管理员！");
        }
        User user = userMapper.selectByCode(openLogin.getUserCode());
        if (ObjectUtil.isNull(user)) {
            throw new BizException("用户不存在，请联系管理员！");
        }
        return userSession.login(user);
    }

    @Override
    public Integer countOnlineUser() {
        Set<String> keys = stringRedisTemplate.keys("token:login:token:*");
        if (ObjectUtil.isNull(keys)) {
            return 0;
        } else {
            return keys.size();
        }
    }

    @Override
    public LoginVo thirdLogin(StThirdLoginReq req) {
        QueryWrapper<OpenLogin> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpenLogin::getAppKey, req.getAppKey());
        OpenLogin openLogin = openLoginMapper.selectOne(wrapper);
        if (ObjectUtil.isNull(openLogin)) {
            throw new BizException("省厅登录第三方用户不存在！");
        }
        long now = System.currentTimeMillis();
        long s = Math.abs(now - req.getTimestamp()) / (60 * 1000);
        if (s > 40) {
            throw new BizException("省厅登录请求过期！");
        }
        String str = req.getAppKey() + openLogin.getAppSecret() + req.getToken() + req.getTimestamp();
        if (ObjectUtil.notEqual(req.getSign(), SecureUtil.md5(str))) {
            throw new BizException("省厅登录认证不通过，请联系管理员！");
        }
        User user = userMapper.selectByCode(openLogin.getUserCode());
        if (ObjectUtil.isNull(user)) {
            throw new BizException("省厅第三方用户不存在！");
        }
        VOrganize organize = req.getOrganize();
        if (ObjectUtil.isNotNull(organize)) {
            QueryWrapper<OrganizeUser> organizeUserWrapper = new QueryWrapper<>();
            organizeUserWrapper.lambda().eq(OrganizeUser::getUserCode, user.getCode())
                    .select(OrganizeUser::getOrganizeCode);
            OrganizeUser organizeUser = organizeUserMapper.selectOne(organizeUserWrapper);
            // 判断下级用户的数据权限，如果组织结构超过该用户的最大权限，设置为对应用的的数据权限

            List<String> childCodes = vOrganizeMapper.listAllChild(organizeUser.getOrganizeCode());
            childCodes.add(organizeUser.getOrganizeCode());
            if (!childCodes.contains(organize.getCode())) {
                QueryWrapper<VOrganize> organizeWrapper = new QueryWrapper<>();
                organizeWrapper.lambda().eq(VOrganize::getCode, organizeUser.getOrganizeCode());
                organize = vOrganizeMapper.selectOne(organizeWrapper);
            }
        }
        BaseThreadLocal.setThirdLoginToken(req.getToken());
        return userSession.thirdLogin(user, req.getUserAuthAreaCodes(), organize);
    }

    @Override
    public LoginVo foaLogin(FOALoginReq req) {
        try {
            String code = ENCRYPT.createDecryptor(req.getPassport());
            if (StrUtil.isBlank(code)) {
                throw new BizException("登录失败，请联系管理员！");
            }
            String userUuid = null;
            QueryWrapper<FoaLogin> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(FoaLogin::getOaUserName, code);
            FoaLogin foaLogin = foaLoginMapper.selectOne(wrapper);
            if (ObjectUtil.isNull(foaLogin)) {
                foaLogin = new FoaLogin();
                foaLogin.setOaUserName(code);
                foaLogin.setGmtCreate(LocalDateTime.now());
                foaLogin.setGmtUpdate(LocalDateTime.now());
                foaLogin.setVersion(1);
                foaLoginMapper.insert(foaLogin);
            } else {
                userUuid = foaLogin.getUserUuid();
            }
            if (ObjectUtil.isNull(userUuid)) {
                wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(FoaLogin::getOaUserName, "default");
                foaLogin = foaLoginMapper.selectOne(wrapper);
                userUuid = foaLogin.getUserUuid();
            }
            return userSession.login(userMapper.selectByCode(userUuid));
        } catch (Exception e) {
            log.error("登录失败，请联系管理员！", e);
            throw new BizException("登录失败，请联系管理员！");
        }
    }

    @Override
    public LoginVo loginByLoginName(String loginName) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(User::getLoginName, loginName);
        User user = userMapper.selectOne(wrapper);
        if (ObjectUtil.isNull(user)) {
            throw new BizException("该用户不存在，请联系管理员！");
        }
        return userSession.login(user);
    }

    private LoginVo baseLogin(LoginReq req, LoginType loginType) {
        boolean needCaptcha = dictService.needCaptcha();
        if (needCaptcha) {
            if (StrUtil.isBlank(req.getCaptchaCode()) || StrUtil.isBlank(req.getImageKey())) {
                throw new BizException("验证码不能为空！");
            }
            String code = captchaCache.get(req.getImageKey());
            captchaCache.remove(req.getImageKey());
            if (StrUtil.isBlank(code) || !req.getCaptchaCode().equalsIgnoreCase(code)) {
                throw new BizException("验证码错误或已过期！");
            }
        }
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(User::getLoginName, req.getLoginName());
        User user = userMapper.selectOne(wrapper);
        if (ObjectUtil.isNull(user)) {
            throw new BizException("用户名密码错误！");
        }
        if (user.getLocked()) {
            throw new BizException("用户已被锁定，无法登录，请联系管理员解锁！");
        }
        if (loginType == LoginType.MOBILE && user.isSuperUser()) {
            throw new BizException("超级管理员不能登录手机端！");
        }
        if (loginType == LoginType.MOBILE_CONSOLE && !user.isSuperUser()) {
            throw new BizException("无权限登录！");
        }
        log.info("md5 : {}", SecureUtil.md5(req.getPassword() + user.getSalt()));
        if (!SecureUtil.md5(req.getPassword() + user.getSalt()).equals(user.getPassword())) {
            Integer times = passwordErrorCache.get(user.getCode());
            if (ObjectUtil.isNull(times)) {
                passwordErrorCache.put(user.getCode(), 1);
            } else {
                times++;
                if (times >= 5) {
                    User newUser = new User();
                    newUser.setId(user.getId());
                    newUser.setLocked(true);
                    userMapper.updateById(newUser);
                    throw new BizException("用户已被锁定，无法登录，请联系管理员解锁！");
                }
                passwordErrorCache.put(user.getCode(), times);
            }
            throw new BizException("用户名密码错误！");
        }
        // 登录成功删除失败记录
        passwordErrorCache.remove(user.getCode());
        return userSession.login(user);
    }
}

enum LoginType {

    /**
     * pc端登录
     */
    PC,

    /**
     * 移动端登录
     */
    MOBILE,

    /**
     * 移动端管理后台
     */
    MOBILE_CONSOLE
}

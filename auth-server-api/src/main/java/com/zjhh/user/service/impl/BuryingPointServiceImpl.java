package com.zjhh.user.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.utils.IpUtil;
import com.zjhh.db.comm.Page;
import com.zjhh.user.dao.entity.BuryingPoint;
import com.zjhh.user.dao.entity.Menu;
import com.zjhh.user.dao.mapper.BuryingPointMapper;
import com.zjhh.user.dao.mapper.MenuMapper;
import com.zjhh.user.enume.DefaultMenuEnum;
import com.zjhh.user.request.AddPointReq;
import com.zjhh.user.request.PageBuryingPointReq;
import com.zjhh.user.service.BuryingPointService;
import com.zjhh.user.vo.BuryingPointVo;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022/6/2 17:17
 */
@Slf4j
@Service
public class BuryingPointServiceImpl implements BuryingPointService {

    @Resource
    private BuryingPointMapper buryingPointMapper;

    @Resource
    private MenuMapper menuMapper;

    @Resource
    private UserSession userSession;

    @Override
    public void addPoint(AddPointReq req, HttpServletRequest request) {
//        if (StrUtil.equals(req.getFromMenuCode(), req.getToMenuCode())) {
//            // 两个menuCode一样，为页面刷新，不做处理
//            return;
//        }
        String curToken = StpUtil.getTokenValue();
        String ip = IpUtil.getIp(request);
        String userCode = userSession.getUserCode();
        if (StrUtil.isNotBlank(req.getFromMenuCode())) {
            LocalDateTime time = LocalDateTime.now().plusHours(-12);
            BuryingPoint buryingPoint = buryingPointMapper.findLastOpen(userCode, req.getFromMenuCode(), curToken, ip, time);
            if (ObjectUtil.isNotNull(buryingPoint)) {
                buryingPoint.setCloseTime(LocalDateTime.now());
                buryingPoint.setGmtUpdate(LocalDateTime.now());
                buryingPoint.setVersion(buryingPoint.getVersion() + 1);
                buryingPointMapper.updateById(buryingPoint);
            }
        }
        BuryingPoint buryingPoint = new BuryingPoint();
        buryingPoint.setUserCode(userCode);
        buryingPoint.setMenuCode(req.getToMenuCode());
        buryingPoint.setMenuName(getMenuName(req.getToMenuCode()));
        buryingPoint.setOpenTime(LocalDateTime.now());
        buryingPoint.setVersion(1);
        buryingPoint.setGmtCreate(LocalDateTime.now());
        buryingPoint.setGmtUpdate(LocalDateTime.now());
        buryingPoint.setCurToken(curToken);
        buryingPoint.setIp(ip);
        buryingPointMapper.insert(buryingPoint);
    }

    @Override
    public Page<BuryingPointVo> page(PageBuryingPointReq req) {
        return buryingPointMapper.page(req.getPage(BuryingPointVo.class), req);
    }

    private String getMenuName(String menuCode) {
        String menuName = DefaultMenuEnum.getNameByCode(menuCode);
        if (StrUtil.isBlank(menuName)) {
            QueryWrapper<Menu> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(Menu::getCode, menuCode)
                    .select(Menu::getName);
            Menu menu = menuMapper.selectOne(wrapper);
            if (ObjectUtil.isNotNull(menu)) {
                menuName = menu.getName();
            }
        }
        return menuName;
    }
}

package com.zjhh.user.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.zjhh.comm.exception.BizException;
import com.zjhh.db.comm.Page;
import com.zjhh.user.constant.UserConfigConstants;
import com.zjhh.user.dao.entity.*;
import com.zjhh.user.dao.mapper.*;
import com.zjhh.user.enume.UserTypeEnum;
import com.zjhh.user.request.*;
import com.zjhh.user.service.UserService;
import com.zjhh.user.service.ZwddApiService;
import com.zjhh.user.vo.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2020/6/11 16:50
 */
@DS("master")
@Service
public class UserServiceImpl extends BaseServiceImpl implements UserService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    private UserConfigMapper userConfigMapper;

    @Resource
    private UserSession userSession;

    @Resource
    private OrganizeUserMapper organizeUserMapper;

    @Resource
    private ZwddUserRelationMapper zwddUserRelationMapper;

    @Resource
    private VOrganizeMapper vOrganizeMapper;

    @Resource
    private AreaMapper areaMapper;

    @Override
    public Page<UserVo> pageUser(PageUserReq req) {
        Page<UserVo> page = req.getPage(UserVo.class);
        if (ObjectUtil.equals(BASE_ORGANIZE_CODE, req.getOrganizeCode())) {
            req.setOrganizeCode(null);
        } else {
            VOrganize vOrganize = vOrganizeMapper.selectByCode(req.getOrganizeCode());
            if (ObjectUtil.isNull(vOrganize)) {
                throw new BizException("该组织结构不存在！");
            }
            req.setOrganizeCodes(listChildOrganizeCode(vOrganize.getCode()));
        }
        req.setIsSuperManager(userSession.isSuperManager());
        page = userMapper.selectPageUserVo(page, req);
        List<String> userCodes = new ArrayList<>();
        page.getRecords().forEach(userVo -> userCodes.add(userVo.getCode()));
        if (CollUtil.isNotEmpty(userCodes)) {
            List<RoleVo> roleVos = userMapper.listRoleVo(userCodes);
            page.getRecords().forEach(userVo ->
                    roleVos.forEach(roleVo -> {
                        if (Objects.equals(userVo.getCode(), roleVo.getUserCode())) {
                            List<RoleVo> list = userVo.getRoles();
                            if (Objects.isNull(list)) {
                                list = new ArrayList<>();
                                userVo.setRoles(list);
                            }
                            list.add(roleVo);
                        }
                    })
            );
        }
        return page;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void addUser(AddUserReq req) {
        if (checkLoginName(req.getLoginName())) {
            throw new BizException("用户名已存在！");
        }
        if (ObjectUtil.isNotNull(req.getUserType()) && req.getUserType() == UserTypeEnum.SUPER_MANAGER.value()) {
            throw new BizException("不允许添加超级管理员！");
        }
        User user = new User();
        String userCode = IdUtil.simpleUUID();
        user.setCode(userCode);
        user.setUsername(req.getUsername());
        user.setLoginName(req.getLoginName());
        user.setMobile(req.getMobile());
        String salt = IdUtil.fastSimpleUUID();
        user.setSalt(salt);
        user.setLocked(false);
        user.setDescription(req.getDescription());
        user.setPassword(SecureUtil.md5(req.getPassword() + salt));
        if (userSession.isSuperManager()) {
            if (UserTypeEnum.SYSTEM_MANAGER.value() == req.getUserType() && userMapper.countOrganizeSystemManager(req.getOrganizeCode()) > 0) {
                throw new BizException("同一个组织结构下只能有一个系统管理员！");
            }
            user.setUserType(req.getUserType());
        } else {
            user.setUserType(UserTypeEnum.COMMON_USER.value());
        }
        user.setNewUser(true);
        user.setCreateUser(userSession.getUserCode());
        user.setAvatar(req.getAvatar());
        user.setEmail(req.getEmail());
        user.setZzdAccountId(req.getZzdAccountId());
        userMapper.insert(user);
        OrganizeUser organizeUser = new OrganizeUser();
        organizeUser.setUserCode(userCode);
        organizeUser.setOrganizeCode(req.getOrganizeCode());
        organizeUserMapper.insert(organizeUser);
        if (CollUtil.isNotEmpty(req.getRoleCodes())) {
            List<UserRole> userRoles = new ArrayList<>();
            req.getRoleCodes().forEach(roleCode -> {
                UserRole userRole = new UserRole();
                userRole.setUserCode(userCode);
                userRole.setRoleCode(roleCode);
                userRoles.add(userRole);
            });
            userRoleMapper.insertBatchSomeColumn(userRoles);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public String addUserReturnCode(AddUserReq req) {

        if (ObjectUtil.isNotNull(req.getUserType()) && req.getUserType() == UserTypeEnum.SUPER_MANAGER.value()) {
            throw new BizException("不允许添加超级管理员！");
        }
        User user = new User();
        String userCode = IdUtil.simpleUUID();
        user.setCode(userCode);
        user.setUsername(req.getUsername());
        user.setLoginName(req.getLoginName());
        user.setMobile(req.getMobile());
        String salt = IdUtil.fastSimpleUUID();
        user.setSalt(salt);
        user.setLocked(false);
        user.setDescription(req.getDescription());
        user.setPassword(SecureUtil.md5(req.getPassword() + salt));

        user.setUserType(UserTypeEnum.COMMON_USER.value());

        user.setNewUser(true);
        user.setCreateUser("test");
        user.setAvatar(req.getAvatar());
        user.setEmail(req.getEmail());
        userMapper.insert(user);
        OrganizeUser organizeUser = new OrganizeUser();
        organizeUser.setUserCode(userCode);
        organizeUser.setOrganizeCode(req.getOrganizeCode());
        organizeUserMapper.insert(organizeUser);
        if (CollUtil.isNotEmpty(req.getRoleCodes())) {
            List<UserRole> userRoles = new ArrayList<>();
            req.getRoleCodes().forEach(roleCode -> {
                UserRole userRole = new UserRole();
                userRole.setUserCode(userCode);
                userRole.setRoleCode(roleCode);
                userRoles.add(userRole);
            });
            userRoleMapper.insertBatchSomeColumn(userRoles);
        }
        return userCode;
    }


    @Override
    public UserDetailVo getUserDetail(String userId) {
        User user = checkUser(userId);
        UserDetailVo userDetailVo = BeanUtil.copyProperties(user, UserDetailVo.class);
        List<RoleVo> roleVos = userMapper.listRoleVo(Collections.singletonList(user.getCode()));
        userDetailVo.setRoles(roleVos);
        return userDetailVo;
    }

    @Override
    public Boolean checkLoginName(String loginName) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(User::getLoginName, loginName);
        return userMapper.selectCount(wrapper) >= 1;
    }

    @Override
    public void updateUser(UpdateUserReq req) {
        User checkUser = checkUser(req.getUserId());
        checkSuperUser(checkUser);

        User user = new User();
        user.setId(req.getUserId());
        user.setUsername(req.getUsername());
        user.setMobile(req.getMobile());
        user.setDescription(req.getDescription());
        user.setAvatar(req.getAvatar());
        user.setEmail(req.getEmail());
        user.setZzdAccountId(req.getZzdAccountId());
        if (userSession.isSuperManager()) {
            if (ObjectUtil.isNotNull(req.getUserType())) {
                if (req.getUserType() == UserTypeEnum.SUPER_MANAGER.value()) {
                    throw new BizException("不允许修改为超级管理员！");
                }
                if (UserTypeEnum.SYSTEM_MANAGER.value() == req.getUserType()) {
                    QueryWrapper<OrganizeUser> organizeUserWrapper = new QueryWrapper<>();
                    organizeUserWrapper.lambda().eq(OrganizeUser::getUserCode, checkUser.getCode())
                            .select(OrganizeUser::getOrganizeCode);
                    OrganizeUser organizeUser = organizeUserMapper.selectOne(organizeUserWrapper);
                    if (userMapper.countOrganizeSystemManager(organizeUser.getOrganizeCode()) > 0) {
                        throw new BizException("同一个组织结构下只能有一个系统管理员！");
                    }
                }
                user.setUserType(req.getUserType());
            }
        }
        userMapper.updateById(user);
        QueryWrapper<UserRole> userRoleWrapper = new QueryWrapper<>();
        userRoleWrapper.lambda().eq(UserRole::getUserCode, checkUser.getCode());
        userRoleMapper.delete(userRoleWrapper);
        if (CollUtil.isNotEmpty(req.getRoleCodes())) {
            List<UserRole> userRoles = new ArrayList<>();
            req.getRoleCodes().forEach(roleCode -> {
                UserRole userRole = new UserRole();
                userRole.setRoleCode(roleCode);
                userRole.setUserCode(checkUser.getCode());
                userRoles.add(userRole);
            });
            userRoleMapper.insertBatchSomeColumn(userRoles);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void deleteBatchUser(List<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            throw new BizException("删除的用户列表不能为空！");
        }
        QueryWrapper<User> userWrapper = new QueryWrapper<>();
        userWrapper.lambda().in(User::getId, userIds)
                .eq(User::getUserType, UserTypeEnum.SUPER_MANAGER.value());
        if (userMapper.selectCount(userWrapper) > 0) {
            throw new BizException("无法删除超级管理员！");
        }
        if (userIds.contains(userSession.getSessionLoginVo().getUserId())) {
            throw new BizException("无法删除自己！");
        }
        userWrapper.clear();
        userWrapper.lambda().in(User::getId, userIds).select(User::getCode);
        List<String> userCodes = userMapper.selectStrings(userWrapper);
        QueryWrapper<UserRole> roleWrapper = new QueryWrapper<>();
        roleWrapper.lambda().in(UserRole::getUserCode, userCodes);
        userRoleMapper.delete(roleWrapper);
        QueryWrapper<UserConfig> userConfigWrapper = new QueryWrapper<>();
        userConfigWrapper.lambda().in(UserConfig::getUserCode, userCodes);
        userConfigMapper.delete(userConfigWrapper);
        QueryWrapper<OrganizeUser> organizeUserWrapper = new QueryWrapper<>();
        organizeUserWrapper.lambda()
                .in(OrganizeUser::getUserCode, userCodes);
        organizeUserMapper.delete(organizeUserWrapper);
        QueryWrapper<ZwddUserRelation> relationQueryWrapper = new QueryWrapper<>();
        relationQueryWrapper.lambda().in(ZwddUserRelation::getUserCode, userCodes);
        zwddUserRelationMapper.delete(relationQueryWrapper);

        userMapper.deleteBatchIds(userIds);
    }

    @Override
    public void lockUser(String userId) {
        if (userId.equals(StpUtil.getLoginIdAsString())) {
            throw new BizException("无法冻结自己！");
        }
        User user = checkUser(userId);
        checkSuperUser(user);
        if (user.getLocked()) {
            throw new BizException("用户已冻结！");
        }
        user.setLocked(true);
        userMapper.updateById(user);
    }

    @Override
    public void unlockUser(String userId) {
        User user = checkUser(userId);
        if (!user.getLocked()) {
            throw new BizException("用户未冻结！");
        }
        user.setLocked(false);
        userMapper.updateById(user);
    }

    @Override
    public void resetPassword(ResetPasswordReq req) {
        User user = checkUser(req.getUserId());
        user.setPassword(SecureUtil.md5(req.getNewPassword() + user.getSalt()));
        userMapper.updateById(user);
        StpUtil.logout(user.getId());
    }

    @Override
    public List<User> listOnlineUser() {
        return null;
    }

    @Override
    public void updatePassword(UpdatePasswordReq req) {
        if (req.getOldPassword().equals(req.getNewPassword())) {
            throw new BizException("新密码不能跟旧密码相同！");
        }
        User user = checkUser(StpUtil.getLoginIdAsString());
        if (!user.getPassword().equals(SecureUtil.md5(req.getOldPassword() + user.getSalt()))) {
            throw new BizException("旧密码错误！");
        }
        user.setPassword(SecureUtil.md5(req.getNewPassword() + user.getSalt()));
        userMapper.updateById(user);
        userSession.logout();
    }

    @Override
    public void saveTheme(String config) {
        QueryWrapper<UserConfig> wrapper = new QueryWrapper<>();
        LoginVo vo = userSession.getSessionLoginVo();
        wrapper.lambda().eq(UserConfig::getUserCode, vo.getCode()).eq(UserConfig::getCode, UserConfigConstants.CUS_THEME_CONFIG);
        UserConfig themeConfig = userConfigMapper.selectOne(wrapper);
        if (Objects.isNull(themeConfig)) {
            themeConfig = new UserConfig();
            themeConfig.setUserCode(vo.getCode());
            themeConfig.setCode(UserConfigConstants.CUS_THEME_CONFIG);
            themeConfig.setValue(config);
            userConfigMapper.insert(themeConfig);
        } else {
            themeConfig.setValue(config);
            userConfigMapper.updateById(themeConfig);
        }
        userSession.setSessionTheme(config);
    }

    @Override
    public User getUserProfile() {
        return checkUser(StpUtil.getLoginIdAsString());
    }

    @Override
    public LoginVo saveUserProfile(SaveProfileReq req) {
        User user = checkUser(StpUtil.getLoginIdAsString());
        user.setUsername(req.getUsername());
        user.setMobile(req.getMobile());
        user.setEmail(req.getEmail());
        user.setDescription(req.getDescription());
        user.setAvatar(req.getAvatar());
        userMapper.updateById(user);
        return userSession.createLoginVo(user);
    }

    @Override
    public List<String> listUserCodeByName(String username) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.lambda().like(User::getUsername, username)
                .select(User::getCode);
        return userMapper.selectStrings(wrapper);
    }

    @Override
    public List<User> getUsers(List<String> userCodes, SFunction<User, ?>... properties) {
        if (CollUtil.isEmpty(userCodes)) {
            return new ArrayList<>();
        }
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(User::getCode, userCodes)
                .select(properties);
        return userMapper.selectList(wrapper);
    }

    @Override
    public User getSuperUser() {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(User::getUserType, 0).last("limit 1");
        return userMapper.selectOne(wrapper);
    }

    @Override
    public void updateMobileUser(UpdateMobileUserReq req) {
        User user = new User();
        user.setId(userSession.getSessionLoginVo().getUserId());
        if (StrUtil.isNotBlank(req.getUsername())) {
            user.setUsername(req.getUsername());
        }
        if (StrUtil.isNotBlank(req.getAvatarImg())) {
            user.setMobileAvatar(req.getAvatarImg());
        }
        user.setGmtUpdate(LocalDateTime.now());
        userMapper.updateById(user);
    }

    @Override
    public MobileUserDetailVo getMobileUserDetail() {
        User user = checkUser(userSession.getSessionLoginVo().getUserId());
        MobileUserDetailVo vo = BeanUtil.copyProperties(user, MobileUserDetailVo.class);
        QueryWrapper<OrganizeUser> organizeUserWrapper = new QueryWrapper<>();
        organizeUserWrapper.lambda().eq(OrganizeUser::getUserCode, user.getCode())
                .select(OrganizeUser::getOrganizeCode);
        OrganizeUser organizeUser = organizeUserMapper.selectOne(organizeUserWrapper);
        QueryWrapper<VOrganize> vOrganizeWrapper = new QueryWrapper<>();
        vOrganizeWrapper.lambda().eq(VOrganize::getCode, organizeUser.getOrganizeCode());
        VOrganize vOrganize = vOrganizeMapper.selectOne(vOrganizeWrapper);
        if (vOrganize.getOrganLevel() == 0) {
            vOrganizeWrapper.clear();
            vOrganizeWrapper.lambda().eq(VOrganize::getCode, vOrganize.getParentCode());
            vOrganize = vOrganizeMapper.selectOne(vOrganizeWrapper);
        }
        vo.setArea(vOrganize.getName());
        return vo;
    }

    @Override
    public String getUserZwddAccountId(String userCode) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(User::getCode, userCode);
        User user = userMapper.selectOne(queryWrapper);
        return user.getZzdAccountId();
    }

    @Override
    public List<String> getSelectAreaAllChild() {
        List<String> list = areaMapper.getAreaChild(userSession.getSelectedAreaCode());
        if (CollUtil.isEmpty(list)) {
            list.add(userSession.getSelectedAreaCode());
        }
        return list;
    }

    @Override
    public List<User> checkZzdStatus(List<String> userCodes) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(User::getCode, userCodes)
                .eq(User::getSyncZwdd, false)
                .select(User::getUsername, User::getCode);
        return userMapper.selectList(wrapper);
    }

    @Override
    public User getUser(String userCode, SFunction<User, ?> columns) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(User::getCode, userCode)
                .select(columns);
        return userMapper.selectOne(wrapper);
    }

    @Override
    public List<String> checkUserCode(List<String> userCodes) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(User::getCode, userCodes)
                .select(User::getCode);
        return userMapper.selectStrings(wrapper);
    }

    private void checkSuperUser(User user) {
        if (user.isSuperUser()) {
            throw new BizException("无法操作超级管理员！");
        }
    }

}

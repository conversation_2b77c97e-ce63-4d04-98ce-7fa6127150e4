package com.zjhh.user.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.comm.vo.TreeSelectedVo;
import com.zjhh.db.comm.Page;
import com.zjhh.system.request.PageSearchReq;
import com.zjhh.user.dao.entity.Area;
import com.zjhh.user.dao.entity.Unit;
import com.zjhh.user.request.AddAreaReq;
import com.zjhh.user.request.AddUnitReq;
import com.zjhh.user.request.UpdateAreaReq;
import com.zjhh.user.request.UpdateUnitReq;
import com.zjhh.user.vo.SelectAreaVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/9 9:26
 */
public interface OrganizeService {

    /**
     * 获取行政区划分页
     *
     * @param req
     * @return
     */
    Page<Area> pageAreas(PageSearchReq req);

    /**
     * 获取单位分页
     *
     * @param req
     * @return
     */
    Page<Unit> pageUnits(PageSearchReq req);

    /**
     * 添加行政区划
     *
     * @param req
     */
    void addArea(AddAreaReq req);

    /**
     * 添加预算单位
     *
     * @param req
     */
    void addUnit(AddUnitReq req);

    /**
     * 编辑行政区划
     *
     * @param req
     */
    void updateArea(UpdateAreaReq req);

    /**
     * 编辑单位
     *
     * @param req
     */
    void updateUnit(UpdateUnitReq req);

    /**
     * 批量删除行政区划
     *
     * @param ids
     */
    void deleteBatchArea(List<String> ids);

    /**
     * 批量删除单位
     *
     * @param ids
     */
    void deleteBatchUnit(List<String> ids);

    /**
     * 获取树形可选择的行政区划
     *
     * @return
     */
    List<Tree<String>> treeArea();

    /**
     * 新建单位-获取可供选择的上级
     *
     * @return
     */
    List<Tree<String>> treeSelectOrganize();

    /**
     * 获取组织结构树形
     *
     * @return
     */
    List<Tree<String>> treeOrganize();

    /**
     * 获取行政区划选择
     *
     * @return
     */
    SelectAreaVo getSelectArea();

    /**
     * 切换行政区划
     *
     * @param areaCode
     */
    void changeArea(String areaCode);

    /**
     * 组织结构公开范围
     *
     * @return
     */
    TreeSelectedVo treeOrganizeSelected();

    /**
     * 获取行政区划
     *
     * @param areaCode
     * @param properties
     * @return
     */
    Area getArea(String areaCode, SFunction<Area, ?>... properties);

    /**
     * 获取预算单位选择
     *
     * @return
     */
    TreeSelectVo listUnitSelect();
}

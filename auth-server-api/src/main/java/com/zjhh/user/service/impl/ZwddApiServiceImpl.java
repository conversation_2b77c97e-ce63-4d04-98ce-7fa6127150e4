package com.zjhh.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONValidator;
import com.alibaba.fastjson2.TypeReference;
import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.alibaba.xxpt.gateway.shared.client.http.GetClient;
import com.alibaba.xxpt.gateway.shared.client.http.PostClient;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.config.SystemProperties;
import com.zjhh.user.dao.entity.User;
import com.zjhh.user.dao.entity.ZwddUserRelation;
import com.zjhh.user.dao.mapper.UserMapper;
import com.zjhh.user.dao.mapper.ZwddUserRelationMapper;
import com.zjhh.user.request.AddUserReq;
import com.zjhh.user.service.UserService;
import com.zjhh.user.service.ZwddApiService;
import com.zjhh.user.utils.ZwddUtil;
import com.zjhh.user.vo.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2021-05-26 3:47 下午
 */
@Slf4j
@Service
public class ZwddApiServiceImpl implements ZwddApiService {

    @Resource
    private ExecutableClient executableClient;

    @Resource
    private ZwddUserRelationMapper zwddUserRelationMapper;

    @Resource
    @Lazy
    private UserService userService;

    @Resource
    @Lazy
    private UserMapper userMapper;

    @Resource
    private ZwddUtil zwddUtil;

    @Resource
    private SystemProperties systemProperties;

    // 每⻚条数, 默认20, 最⼤100
    private final static String PAGE_SIZE = "100";

    /**
     * 获取租户根组织
     *
     * @return 响应参数参考GetRootOrganization.java
     */
    @Override
    public GetOrganizationByCode getRootOrganization() {
        String api = "/mozi/organization/getRootOrganization";
        PostClient postClient = zwddUtil.getPostClient(api);
        // 设置参数
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();
        ResponseBean<GetOrganizationByCode> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<GetOrganizationByCode>>() {
                });
        log.info("获取租户根组织，tenantId: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("获取租户根组织失败");
            throw new RuntimeException("获取租户根组织失败");
        }
        return responseBean.getContent().getData();
    }

    /**
     * 分页获取下⼀级组织 Code 列表
     *
     * @param organizationCode 父组织code
     * @param pageNo           页码
     * @return
     */
    @Override
    public Content<List<String>> pageSubOrganizationCodes(String organizationCode, Integer pageNo) {
        String api = "/mozi/organization/pageSubOrganizationCodes";
        PostClient postClient = zwddUtil.getPostClient(api);

        // 设置参数
        // 是否返回查询结果总数
        postClient.addParameter("returnTotalSize", "true");
        postClient.addParameter("pageSize", PAGE_SIZE);
        postClient.addParameter("organizationCode", organizationCode);
        postClient.addParameter("pageNo", Objects.isNull(pageNo) ? "1" : pageNo.toString());
        // 查询下一级子组织状态条件 A - 查询有效的数据  F - 查询无效的数据  TOTAL - 查询所有的数据
        postClient.addParameter("status", "A");
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();
        log.info("根据组织code查询下一级组织列表,apiResult: {}", apiResult);
        //确保能够顺利跑下去，不会因为接口异常而终止查询
        if (!JSONValidator.from(apiResult).validate()) {
            return null;
        }
        ResponseBean<List<String>> responseBean = JSON.parseObject(apiResult, new TypeReference<ResponseBean<List<String>>>() {
        });
        log.info("⼀级组织 Code 列表，tenantId: {}, organizationCode: {}, pageNo: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), organizationCode,
                pageNo, responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("分页获取下一级组织 Code 列表失败");
            return null;
        }
        return responseBean.getContent();
    }

    /**
     * 根据组织code查询详情
     *
     * @param organizationCode 组织code
     * @return 组织详情
     */
    @Override
    public Future<GetOrganizationByCode> getOrganizationByCode(String organizationCode) {
        String api = "/mozi/organization/getOrganizationByCode";
        PostClient postClient = zwddUtil.getPostClient(api);
        // 设置参数
        postClient.addParameter("organizationCode", organizationCode);
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();
        log.info("apiResult: {}", apiResult);
        //确保能够顺利跑下去，不会因为接口异常而终止查询
        if (!JSONValidator.from(apiResult).validate()) {
            return null;
        }
        ResponseBean<GetOrganizationByCode> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<GetOrganizationByCode>>() {
                });
        log.info("根据组织code查询详情，tenantId: {}, organizationCode: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), organizationCode, responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("根据组织code查询详情失败");
            return null;
        } else {
            return new AsyncResult<>(responseBean.getContent().getData());
        }
    }

    /**
     * 根据组织code列表 查询组织详情
     *
     * @param organizationCodes 组织code列表
     * @return 组织集合
     */
    @Async
    @Override
    public Future<List<GetOrganizationByCode>> listOrganizationByCodes(List<String> organizationCodes) {
        String api = "/mozi/organization/listOrganizationsByCodes";
        PostClient postClient = zwddUtil.getPostClient(api);
        for (String code : organizationCodes) {
            postClient.addParameter("organizationCodes", code);
        }
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();
        System.out.println(apiResult);
        //确保能够顺利跑下去，不会因为接口异常而终止查询
        if (!JSONValidator.from(apiResult).validate()) {

            return null;
        }
        ResponseBean<List<GetOrganizationByCode>> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<List<GetOrganizationByCode>>>() {
                });
        log.info("根据组织code列表查询详情，tenantId: {}, organizationCodes: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), organizationCodes,
                responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("根据组织code列表批量查询详情失败");
            return null;
        }
        return new AsyncResult<>(responseBean.getContent().getData());
    }

    /**
     * 根据员工code列表 查询员工详情
     *
     * @param employeeCodes 员工code列表
     * @param dingIds
     * @return 员工集合
     */
    @Override
    public List<GetEmployeeByCode> listEmployeeByCodes(List<String> employeeCodes, List<String> dingIds) {
        String api = "/mozi/employee/listEmployeesByCodes";
        PostClient postClient = zwddUtil.getPostClient(api);
        for (String code : employeeCodes) {
            postClient.addParameter("employeeCodes", code);
        }
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();
        log.info("根据员工code列表查询详情，apiResult: {}", apiResult);
        //确保能够顺利跑下去，不会因为接口异常而终止查询
        if (!JSONValidator.from(apiResult).validate()) {
            return null;
        }
        ResponseBean<List<GetEmployeeByCode>> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<List<GetEmployeeByCode>>>() {
                });

        log.info("根据员工code列表查询详情，tenantId: {}, organizationCodes: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), employeeCodes, responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("根据员工code列表批量查询详情失败");
            return new ArrayList<>();
        }
        List<GetEmployeeByCode> employeeByCodeList = responseBean.getContent().getData();
        if (!CollectionUtils.isEmpty(employeeByCodeList) && !CollectionUtils.isEmpty(dingIds)) {
            for (int i = 0; i < employeeByCodeList.size() - 1; i++) {
                employeeByCodeList.get(i).setDingdingId(dingIds.get(i));
            }
        }

        return employeeByCodeList;
    }

    /**
     * 分页查询组织下的员⼯ Code
     *
     * @param organizationCode 父组织code
     * @param pageNo           页码
     * @return
     */
    @Override
    public Content<List<String>> pageOrganizationEmployeeCodes(String organizationCode, Integer pageNo) {
        String api = "/mozi/organization/pageOrganizationEmployeeCodes";
        PostClient postClient = zwddUtil.getPostClient(api);
        // 设置参数
        postClient.addParameter("returnTotalSize", "true"); // 是否返回查询结果总数
        postClient.addParameter("pageSize", PAGE_SIZE);
        postClient.addParameter("organizationCode", organizationCode);
        postClient.addParameter("pageNo", Objects.isNull(pageNo) ? "1" : pageNo.toString());
        postClient.addParameter("employeePositionStatus", "A"); // 员工在组织内任职状态条件： A - 查询有效的数据， F - 查询无效的数据， TOTAL - 查询所有的数据
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();

        log.info("分页查询组织下的员⼯ Code，apiResult: {}", apiResult);
        //确保能够顺利跑下去，不会因为接口异常而终止查询
        if (!JSONValidator.from(apiResult).validate()) {
            return null;
        }
        ResponseBean<List<String>> responseBean = JSON.parseObject(apiResult, new TypeReference<ResponseBean<List<String>>>() {
        });
        log.info("分页查询组织下的员⼯ Code，tenantId: {}, organizationCode: {}, pageNo: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), organizationCode,
                pageNo, responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("分页查询组织下的员⼯ Code失败");
            return null;
        }

        return responseBean.getContent();
    }

    /**
     * 根据员工 Code 查询详情
     *
     * @param employeeCode 员工code
     * @return
     */
    @Override
    public GetEmployeeByCode getEmployeeByCode(String employeeCode) {
        String api = "/mozi/employee/getEmployeeByCode";
        PostClient postClient = zwddUtil.getPostClient(api);
        // 设置参数
        postClient.addParameter("employeeCode", employeeCode);
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();
        log.info("根据员工 Code 查询详情，apiResult: {}", apiResult);
        //确保能够顺利跑下去，不会因为接口异常而终止查询
        if (!JSONValidator.from(apiResult).validate()) {

            return null;
        }
        ResponseBean<GetEmployeeByCode> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<GetEmployeeByCode>>() {
                });
        log.info("根据员工 Code 查询详情，tenantId: {}, employeeCode: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), employeeCode, responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("根据员工 Code 查询详情失败");
            return null;
        }
        return responseBean.getContent().getData();
    }

    /**
     * 根据员工 Code 查询详情
     *
     * @param employeeCode 员工code
     * @return 员工详情
     */
    @Override
    public GetEmployeeByCode getEmployeeByCode2DingDing(String employeeCode) {
        if (StrUtil.isEmpty(employeeCode)) {
            return null;
        }
        String api = "/mozi/employee/getEmployeeByCode";
        PostClient postClient = zwddUtil.getPostClient(api);
        // 设置参数
        postClient.addParameter("employeeCode", employeeCode);
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();
        log.info("根据员工 Code 查询详情，apiResult: {}", apiResult);
        //确保能够顺利跑下去，不会因为接口异常而终止查询
        if (!JSONValidator.from(apiResult).validate()) {
            return null;
        }
        ResponseBean<GetEmployeeByCode> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<GetEmployeeByCode>>() {
                });
        log.info("根据员工 Code 查询详情，tenantId: {}, employeeCode: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), employeeCode, responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("根据员工 Code 查询详情失败");
            return null;
        }
        return responseBean.getContent().getData();
    }

    /**
     * 根据员工code 获取员工任职
     *
     * @param employeeCode 员工code
     * @return 员工任职列表
     */
    @Override
    public List<OrgEmployeePositionsByCode> listEmployeePositionsByEmployeeCode(String employeeCode) {
        String api = "/mozi/employee/listEmployeePositionsByEmployeeCode";
        PostClient postClient = zwddUtil.getPostClient(api);
        //设置参数
        postClient.addParameter("employeePositionStatus", "A");
        postClient.addParameter("employeePositionType", "EMPLOYEE_POSITION_ALL");
        postClient.addParameter("employeeCode", employeeCode);
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        //调用API
        String apiResult = postClient.post();
        log.info("通过员工 Code 获取员工任职列表，apiResult: {}", apiResult);
        //确保能够顺利跑下去，不会因为接口异常而终止查询
        if (!JSONValidator.from(apiResult).validate()) {
            return null;
        }

        ResponseBean<List<OrgEmployeePositionsByCode>> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<List<OrgEmployeePositionsByCode>>>() {
                });
        log.info("根据员⼯ Code，tenantId: {},employeeCode: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), employeeCode, responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("通过员工 Code 获取员工任职列表失败");
            return new ArrayList<>();
        }
        return responseBean.getContent().getData();

    }

    /**
     * 通过员工 Code 列表获取员⼯账号 ID
     *
     * @param employeeCode 员工code
     * @return ListEmployeeAccountIds 员工账号id
     */
    @Override
    public List<ListEmployeeAccountIds> listEmployeeAccountIds(String employeeCode) {
        if (StrUtil.isEmpty(employeeCode)) {
            return new ArrayList<>();
        }

        String api = "/mozi/employee/listEmployeeAccountIds";
        PostClient postClient = zwddUtil.getPostClient(api);
        // 设置参数
        postClient.addParameter("employeeCodes", employeeCode);
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();
        log.info("通过员工 Code 列表获取员⼯账号 ID，apiResult: {}", apiResult);
        //确保能够顺利跑下去，不会因为接口异常而终止查询
        if (!JSONValidator.from(apiResult).validate()) {
            return new ArrayList<>();
        }
        ResponseBean<List<ListEmployeeAccountIds>> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<List<ListEmployeeAccountIds>>>() {
                });

        log.info("根据员⼯ Code，tenantId: {},employeeCode: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), employeeCode, responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("通过员工 Code 列表获取员⼯账号 ID失败");
            return new ArrayList<>();
        }
        return responseBean.getContent().getData();
    }

    /**
     * 根据员工code查询 任职列表
     *
     * @param employeeCode 员工code
     * @return 员工任职列表
     */
    @Override
    public List<OrgEmployeePositionsByCode> getEmployeePositionsByCode(String employeeCode) {

        if (StrUtil.isEmpty(employeeCode)) {
            return new ArrayList<>();
        }

        String api = "/mozi/employee/listEmployeePositionsByEmployeeCode";
        PostClient postClient = zwddUtil.getPostClient(api);
        // 设置参数
        //员工在组织内任职状态条件 A - 查询有效的数据 F - 查询无效的数据 TOTAL - 查询所有的数据
        postClient.addParameter("employeePositionStatus", "A");
        //所需员工任职类型 默认是主职
        // EMPLOYEE_POSITION_ALL - 所有  EMPLOYEE_POSITION_MAIN - 主职   EMPLOYEE_POSITION_ADJUNCT - 非主职
        postClient.addParameter("employeePositionType", "EMPLOYEE_POSITION_ALL");

        postClient.addParameter("employeeCode", employeeCode);
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();
        ResponseBean<List<OrgEmployeePositionsByCode>> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<List<OrgEmployeePositionsByCode>>>() {
                });

        log.info("根据员⼯ Code，tenantId: {},employeeCode: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), employeeCode, responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("根据员⼯ Code， 批量获取员工在该组织的任职失败");
            return new ArrayList<>();
        }
        return responseBean.getContent().getData();
    }

    /**
     * 根据组织 CODE、员⼯ Code 列表， 批量获取员工在该组织的任职
     *
     * @param employeeCodes    员工code列表
     * @param organizationCode 组织code
     * @return
     */
    @Override
    public List<ListOrgEmployeePositionsByCodes> listOrgEmployeePositionsByCodes(String organizationCode,
                                                                                 Collection<String> employeeCodes) {
        if (Objects.isNull(employeeCodes) || employeeCodes.isEmpty()) {
            return new ArrayList<>();
        }

        String api = "/mozi/employee/listOrgEmployeePositionsByCodes";
        PostClient postClient = zwddUtil.getPostClient(api);
        // 设置参数
        postClient.addParameter("organizationCode", organizationCode);
        for (String employeeCode : employeeCodes) {
            postClient.addParameter("employeeCodes", employeeCode);
        }
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();
        ResponseBean<List<ListOrgEmployeePositionsByCodes>> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<List<ListOrgEmployeePositionsByCodes>>>() {
                });
        log.info("根根据组织 CODE、员⼯ Code 列表， 批量获取员工在该组织的任职，tenantId: {}, organizationCode: {}, employeeCodes: {}, responseBean: {}",
                systemProperties.getZwddAuth().getTenantId(), organizationCode, employeeCodes, responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("根据组织 CODE、员⼯ Code 列表， 批量获取员工在该组织的任职失败");
            return new ArrayList<>();
        }

        return responseBean.getContent().getData();
    }

    /**
     * 查询定义的事件列表
     */
    @Override
    public List<CallbackDefine> queryCallbackDefine() {
        String api = "/openplatform/message/query_callback_define";
        PostClient postClient = zwddUtil.getPostClient(api);
        // 设置参数
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();
        // 如果从未注册过事件回调或者把注册过的回调事件全部删掉后，返回{}，坑爹
        if ("{}".equals(apiResult)) {
            return new ArrayList<>();
        }
        log.error("查询定义的事件列表，apiResult: {}", apiResult);
        CallbackResponse responseBean = JSON.parseObject(apiResult, CallbackResponse.class);
        log.info("查询定义的事件列表，tenantId: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), responseBean);
        if (!responseBean.isSuccess()) {
            log.error("查询定义的事件列表失败，tenantId: {}", systemProperties.getZwddAuth().getTenantId());
            throw new RuntimeException("查询定义的事件列表失败");
        }
        return responseBean.getContent();
    }

    /**
     * 注册消息回调
     *
     * @param eventTag
     * @param callbackUrl
     */
    @Override
    public boolean registerEvenCallback(ZwddCallBackEventEnum eventTag, String callbackUrl) {
        String api = "/openplatform/message/register_event_callback";
        PostClient postClient = zwddUtil.getPostClient(api);
        // 设置参数
        postClient.addParameter("eventTag", eventTag.getIndex());
        postClient.addParameter("callbackUrl", callbackUrl);
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();
        System.out.println(apiResult);
        ResponseBean<Object> responseBean = JSON.parseObject(apiResult, new TypeReference<ResponseBean<Object>>() {
        });
        log.info("注册消息回调，tenantId: {}, eventTag: {}, callbackUrl: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), eventTag.getIndex(),
                callbackUrl, responseBean);
        if (!responseBean.isSuccess()) {
            log.error("注册消息回调失败");
            return false;
        }
        return true;
    }

    /**
     * 删除事件回调的定义信息
     *
     * @param eventCallbackId 订阅回调的id，从this.queryCallbackDefine()返回的参数中获取id
     */
    @Override
    public void deleteEventCallbackDefine(Long eventCallbackId) {
        String api = "/openplatform/message/delete_event_callback_define";
        PostClient postClient = zwddUtil.getPostClient(api);
        // 设置参数
        postClient.addParameter("eventCallbackId", eventCallbackId.toString());
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        // 调用API
        String apiResult = postClient.post();
        ResponseBean<Object> responseBean = JSON.parseObject(apiResult, new TypeReference<ResponseBean<Object>>() {
        });
        log.info("删除事件回调的定义信息，tenantId: {}, eventCallbackId: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), eventCallbackId, responseBean);
        if (!responseBean.isSuccess()) {
            log.error("删除事件回调的定义信息失败");
            throw new RuntimeException("删除事件回调的定义信息失败");
        }

    }

    /**
     * 根据钉钉id 列表查询映射
     *
     * @param dingUserIdList 钉钉id 列表
     * @return 员工列表
     */
    @Override
    public List<GetEmployeeByCode> listEmployeeByDingdingId(List<String> dingUserIdList) {
        List<GetEmployeeByCode> employeeByCodeList = new ArrayList<>();

        for (String dingUserId : dingUserIdList) {
            String empCode = getEmpCodeByDingUserId(dingUserId);
            GetEmployeeByCode employeeByCode = getEmployeeByCode2DingDing(empCode);
            if (employeeByCode != null) {
                employeeByCode.setDingdingId(dingUserId);
                employeeByCodeList.add(employeeByCode);
            }
        }
        if (CollectionUtils.isEmpty(employeeByCodeList)) {
            return null;
        }
        return employeeByCodeList;
    }

    /**
     * 根据钉钉id 查询员工映射
     *
     * @param dingUserId 钉钉id
     * @return 员工code
     */
    @Override
    public String getEmpCodeByDingUserId(String dingUserId) {
        String api = "/zzd/idMap/getEmpCodeByDingUserId";
        PostClient postClient = zwddUtil.getPostClient(api);
        //设置参数
        postClient.addParameter("dingUserId", dingUserId);
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        //调用API
        String apiResult = postClient.post();
        System.out.println(apiResult);
        if (!JSONValidator.from(apiResult).validate()) {
            return null;
        }
        ResponseBean<String> responseBean = JSON.parseObject(apiResult, new TypeReference<ResponseBean<String>>() {
        });
        log.info("根据dingdingId查询政务钉钉映射，tenantId: {},dingUserId: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), dingUserId, responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("根据dingdingId查询政务钉钉映射失败");
            return null;
        }
        return responseBean.getContent().getData();
    }

    /**
     * 根据钉钉id 查询员工映射
     *
     * @param mobile 钉钉id
     * @return 员工code
     */
    @Override
    public String getEmployeeCodeByMobile(String mobile) {
        String api = "/mozi/employee/get_by_mobile" + "";
        PostClient postClient = zwddUtil.getPostClient(api);
        //设置参数
        postClient.addParameter("areaCode", "86");
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        postClient.addParameter("namespace", "local");
        postClient.addParameter("mobile", mobile);
        //调用API
        String apiResult = postClient.post();
        System.out.println(apiResult);
        if (!JSONValidator.from(apiResult).validate()) {
            return null;
        }
        ResponseBean<Map<String, String>> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<Map<String, String>>>() {
                });
        log.info("根据手机号查询人员编码，tenantId: {},mobile: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), mobile, responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("根据手机号查询人员编码失败");
            return null;
        }
        return responseBean.getContent().getData().get("employeeCode");
    }

    private Map<String, String> getInfoByMobile(String mobile) {
        String api = "/mozi/employee/get_by_mobile" + "";
        PostClient postClient = zwddUtil.getPostClient(api);
        //设置参数
        postClient.addParameter("areaCode", "86");
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        postClient.addParameter("namespace", "local");
        postClient.addParameter("mobile", mobile);
        //调用API
        String apiResult = postClient.post();
        System.out.println(apiResult);
        if (!JSONValidator.from(apiResult).validate()) {
            return null;
        }
        ResponseBean<Map<String, String>> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<Map<String, String>>>() {
                });
        log.info("根据手机号查询人员编码，tenantId: {},mobile: {}, responseBean: {}", systemProperties.getZwddAuth().getTenantId(), mobile, responseBean);
        if (!responseBean.isSuccess() || !responseBean.getContent().isSuccess()) {
            log.error("根据手机号查询人员编码失败");
            return null;
        }
        return responseBean.getContent().getData();
    }

    /**
     * 根据手机号列表，查询人员列表
     *
     * @param mobiles 手机号列表
     * @return 人员列表
     */
    @Override
    public List<GetEmployeeByCode> listEmployeeByMobile(List<String> mobiles) {

        List<GetEmployeeByCode> employeeByCodeList = new ArrayList<>();

        for (String mobile : mobiles) {
            String empCode = getEmployeeCodeByMobile(mobile);
            GetEmployeeByCode employeeByCode = getEmployeeByCode2DingDing(empCode);
            if (employeeByCode != null) {
                employeeByCode.setMobile(mobile);
                employeeByCodeList.add(employeeByCode);
            }
        }
        if (CollectionUtils.isEmpty(employeeByCodeList)) {
            return null;
        }
        return employeeByCodeList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SyncZwddInfoVO listEmployeeByUserIds(List<String> userIds) {

        List<GetEmployeeByCode> employeeByCodeList = new ArrayList<>();
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        SyncZwddResultVO mobileResult = new SyncZwddResultVO();
        mobileResult.setResultString("未设置手机号！");
        mobileResult.setResultList(new ArrayList<>());
        SyncZwddResultVO notFoundResult = new SyncZwddResultVO();
        notFoundResult.setResultList(new ArrayList<>());
        notFoundResult.setResultString("未找到浙政钉信息！");
        SyncZwddInfoVO syncZwddInfoVO = new SyncZwddInfoVO();
        Integer successCount = 0;
        Integer failCount = 0;
        List<User> users = userMapper.selectList(queryWrapper.lambda().in(User::getId, userIds));
        for (User user : users) {
            if (StrUtil.isBlank(user.getMobile())) {
                mobileResult.getResultList().add("【用户账号】" + user.getLoginName() + "," + "【用户姓名】" + user.getUsername());
                continue;
            }
            Map<String, String> empInfo = getInfoByMobile(user.getMobile());
            String employeeByCode = null;
            if (empInfo != null) {
                employeeByCode = empInfo.get("employeeCode");
            }
            if (employeeByCode == null) {
                notFoundResult.getResultList()
                        .add("【用户账号】" + user.getLoginName() + "," + "【用户姓名】" + user.getUsername() + "【手机号】" + user.getMobile());
            } else {
                QueryWrapper<ZwddUserRelation> zwddQuery = new QueryWrapper<>();
                zwddQuery.eq("user_code", user.getCode());
                ZwddUserRelation zwddUserRelation = zwddUserRelationMapper.selectOne(zwddQuery);
                if (zwddUserRelation == null) {
                    zwddUserRelation = new ZwddUserRelation();
                    zwddUserRelation.setId(IdUtil.getSnowflakeNextIdStr());
                    zwddUserRelation.setUsername(user.getUsername());
                    zwddUserRelation.setUserCode(user.getCode());
                    zwddUserRelation.setMobile(user.getMobile());
                    zwddUserRelation.setGmtCreate(LocalDateTime.now());
                    zwddUserRelation.setGmtUpdate(LocalDateTime.now());
                    zwddUserRelation.setEmployeeCode(employeeByCode);
                    zwddUserRelation.setAccountId(Long.parseLong(empInfo.get("accountId")));
                    zwddUserRelationMapper.insert(zwddUserRelation);
                } else {
                    zwddUserRelation.setAccountId(Long.parseLong(empInfo.get("accountId")));
                    zwddUserRelation.setEmployeeCode(employeeByCode);
                    zwddUserRelationMapper.updateById(zwddUserRelation);
                }
                user.setSyncZwdd(true);
                userMapper.updateById(user);
                GetEmployeeByCode getEmployeeByCode = new GetEmployeeByCode();
                getEmployeeByCode.setEmployeeCode(employeeByCode);
                getEmployeeByCode.setEmployeeName(user.getUsername());
                getEmployeeByCode.setMobile(user.getMobile());
                employeeByCodeList.add(getEmployeeByCode);

            }
        }

        successCount = employeeByCodeList.size();
        failCount = mobileResult.getResultList().size() + notFoundResult.getResultList().size();
        syncZwddInfoVO.setResultString(
                "共同步" + userIds.size() + "人" + "," + "成功" + successCount + "人" + "," + "失败" + failCount + "人 ");
        if (employeeByCodeList.size() == userIds.size()) {
            syncZwddInfoVO.setNotSetPhone(null);
            syncZwddInfoVO.setNotFoundZwdd(null);
        } else {
            syncZwddInfoVO.setNotSetPhone(mobileResult);
            syncZwddInfoVO.setNotFoundZwdd(notFoundResult);
        }
        return syncZwddInfoVO;

    }

    /**
     * 接口地址：https://openplatform-portal.dg-work.cn/#/doc-jsapi?apiType=serverapi&docKey=2641<br/>
     * 获取通讯录权限范围
     *
     * @return 用户部门 可见范围 用户list返回为空，部门list为当前应用根组织code
     */
    @Override
    public String getAuthorizedRootOrgCode() {
        String api = "/auth/scopes";
        PostClient postClient = zwddUtil.getPostClient(api);
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        String apiResult = postClient.post();
        log.info("获取通讯录权限范围, apiResult :{}", apiResult);
        if (!JSONValidator.from(apiResult).validate()) {
            log.error("政务钉钉获取通讯录权限范围接口异常，api: {}", api);
            return null;
        }
        Content<Map<String, List<String>>> responseBean = JSON
                .parseObject(apiResult, new TypeReference<Content<Map<String, List<String>>>>() {
                });
        if (!responseBean.isSuccess()) {
            log.error("政务钉钉获取通讯录权限范围接口返回异常，api: {}", api);
            return null;
        }
        Map<String, List<String>> scope = responseBean.getData();
        if (CollectionUtils.isEmpty(scope)) {
            return null;
        }
        //deptVisibleScopes:企业授权的部门编码列表。返回值为授权部门编码的并集 (设置“全部员工”时，返回授权的部门编码为根部门ID，员工userid列表为空)
        return scope.get("deptVisibleScopes").get(0);
    }

    /**
     * 获取access_token
     *
     * @return
     */
    @Override
    public String getAccessToken(boolean isScan) {
        String api = "/gettoken.json";
        GetClient getClient;
        String key;
        String secret;
        if (isScan) {
            getClient = zwddUtil.getScanGetClient(api);
            key = systemProperties.getZwddAuth().getScanAppKey();
            secret = systemProperties.getZwddAuth().getScanAppSecret();
        } else {
            getClient = zwddUtil.getGetClient(api);
            key = systemProperties.getZwddAuth().getAppKey();
            secret = systemProperties.getZwddAuth().getAppSecret();
        }
        log.info("appkey：{}", key);
        log.info("appsecret：{}", secret);
        // 设置参数
        getClient.addParameter("appkey", key);
        getClient.addParameter("appsecret", secret);
        // 调用API
        String apiResult = getClient.get();
        log.info("获取access_token,apiResult: {}", apiResult);
        //确保能够顺利跑下去，不会因为接口异常而终止查询
        if (!JSONValidator.from(apiResult).validate()) {
            return null;
        }
        ResponseBean<Map<String, String>> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<Map<String, String>>>() {
                });
        return responseBean.getContent().getData().get("accessToken");
    }

    /**
     * 接口地址：https://openplatform-portal.dg-work.cn/portal/#/helpdoc?docKey=kfzn&slug=engk1k<br/>
     * 根据code获取用户信息
     *
     * @return 用户信息
     */
    @Override
    public ZwddLoginUserInfo getUserInfoByCode(String code) {
        if (StrUtil.isEmpty(code)) {
            return null;
        }
        String api = "/rpc/oauth2/getuserinfo_bycode.json";
        PostClient postClient = zwddUtil.getScanPostClient(api);
        postClient.addParameter("access_token", this.getAccessToken(true));
        postClient.addParameter("code", code);
        String apiResult = postClient.post();
        log.info("扫码登录根据code获取用户信息, apiResult :{}", apiResult);
        if (!JSONValidator.from(apiResult).validate()) {
            log.error("扫码登录根据code获取用户信息接口异常，api: {}", api);
            return null;
        }
        ResponseBean<Map<String, String>> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<Map<String, String>>>() {
                });
        if (!responseBean.isSuccess()) {
            log.error("扫码登录根据code获取用户信息接口返回异常，api: {}", api);
            return null;
        }
        Content<Map<String, String>> content = responseBean.getContent();
        if (content == null) {
            log.error("扫码登录根据code获取用户信息接口Content为null，api: {}", api);
        }
        Map<String, String> data = content.getData();
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(data), ZwddLoginUserInfo.class);
    }

    /**
     * 接口地址：https://openplatform-portal.dg-work.cn/portal/#/helpdoc?docKey=kfzn&slug=bfno8h<br/>
     * 根据code获取用户信息
     *
     * @return 用户信息
     */
    @Override
    public ZwddLoginUserInfo getUserInfoByMobileCode(String authCode) {
        if (StrUtil.isEmpty(authCode)) {
            return null;
        }
        String api = "/rpc/oauth2/dingtalk_app_user.json";
        PostClient postClient = zwddUtil.getPostClient(api);
        postClient.addParameter("access_token", this.getAccessToken(false));
        postClient.addParameter("auth_code", authCode);
        String apiResult = postClient.post();
        log.info("移动端免登根据code获取用户信息, apiResult :{}", apiResult);
        if (!JSONValidator.from(apiResult).validate()) {
            log.error("移动端免登根据code获取用户信息接口异常，api: {}", api);
            return null;
        }
        ResponseBean<Map<String, String>> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<Map<String, String>>>() {
                });
        if (!responseBean.isSuccess()) {
            log.error("移动端免登根据code获取用户信息接口返回异常，api: {}", api);
            return null;
        }
        Content<Map<String, String>> content = responseBean.getContent();
        if (content == null) {
            log.error("移动端免登根据code获取用户信息接口Content为null，api: {}", api);
        }
        Map<String, String> data = content.getData();
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(data), ZwddLoginUserInfo.class);
    }

    @Override
    public boolean fullSyncUserByMobileList(List<String> mobileList) {

        List<GetEmployeeByCode> employeeByCodeList = listEmployeeByMobile(mobileList);

        employeeByCodeList.forEach(this::insertOrUpdateByMobile);
        return true;
    }

    @Override
    public boolean fullSyncDeptAndUser() throws Exception {
        //获取通讯录权限范围
        String rootOrganizationCode = getAuthorizedRootOrgCode();
        //1、根据组织code查询详情
        GetOrganizationByCode rootOrganization = getOrganizationByCode(rootOrganizationCode).get();
        if (rootOrganization == null) {
            return false;
        }
        //2、插入根组织
        //		addRootOrg(rootOrganization);
        //3、根据根组织code获取所有组织code
        List<String> orgCodeList = new ArrayList<>();
        List<String> changeParentList = new ArrayList<>();
        List<String> allOrgCode = getAllOrgCode(rootOrganizationCode, orgCodeList, changeParentList);
        //4、抽取方法 根据组织详情数据做组织插入
        for (String orgCode : allOrgCode) {
            // 5、抽取方法 根据组织code获取到该组织 所有员工code
            getAllUserCode(orgCode);
        }
        log.info("changeParentList size:{}", changeParentList.size());
        return !CollectionUtils.isEmpty(changeParentList);
    }

    @Override
    public int deleteZwddRelation(String userCode) {
        QueryWrapper<ZwddUserRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ZwddUserRelation::getUserCode, userCode);
        return zwddUserRelationMapper.delete(queryWrapper);
    }

    @Override
    public int updateZwddRelation(User user) {

        List<GetEmployeeByCode> list = listEmployeeByMobile(CollUtil.newArrayList(user.getMobile()));
        if (list.size() > 0) {
            QueryWrapper<ZwddUserRelation> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ZwddUserRelation::getUserCode, user.getCode());
            ZwddUserRelation zwddUserRelation = zwddUserRelationMapper.selectOne(queryWrapper);
            if (zwddUserRelation != null) {
                zwddUserRelation.setMobile(user.getMobile());
                zwddUserRelation.setEmployeeCode(list.get(0).getEmployeeCode());
                zwddUserRelationMapper.updateById(zwddUserRelation);
            } else {
                zwddUserRelation = new ZwddUserRelation();
                zwddUserRelation.setUserCode(user.getCode());
                zwddUserRelation.setMobile(user.getMobile());
                zwddUserRelation.setUsername(user.getUsername());
                zwddUserRelation.setGmtUpdate(LocalDateTime.now());
                zwddUserRelation.setGmtUpdate(LocalDateTime.now());
                zwddUserRelation.setEmployeeCode(list.get(0).getEmployeeCode());
                zwddUserRelationMapper.insert(zwddUserRelation);
            }
            return 1;
        }

        return 0;
    }

    @Override
    public String sendNotification(ZwddMsgDTO ddMsgDTO) {

        String api = "/chat/sendMsg";
        PostClient postClient = zwddUtil.getPostClient(api);
        //Set the parameters
        postClient.addParameter("msg", JSON.toJSONString(ddMsgDTO.getDdMsg()));
        postClient.addParameter("senderId", ddMsgDTO.getSenderId());
        postClient.addParameter("receiverId", StrUtil.join(",", ddMsgDTO.getReciverIds()));
        postClient.addParameter("chatId", "");
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        postClient.addParameter("chatType", "1");
        //Call API
        System.out.println(executableClient.toString());
        System.out.println(postClient.parameterList());
        String apiResult = postClient.post();
        System.out.println(apiResult);

        return JSONObject.parseObject(apiResult).getJSONObject("content").getObject("data", Map.class).get("messageId")
                .toString();
    }

    @Override
    public boolean checkMessageStatus(String messageId, String accountId) {

        String api = "/chat/group/messageReadUsers";
        PostClient postClient = zwddUtil.getPostClient(api);
        //Set the parameters
        postClient.addParameter("tenantId",  systemProperties.getZwddAuth().getTenantId());
        postClient.addParameter("accountId", accountId);
        postClient.addParameter("messageId", messageId);
        postClient.addParameter("pageSize", "3");
        postClient.addParameter("cursor", "");
        //Call API
        String apiResult = postClient.post();
        List list = JSONObject.parseObject(apiResult).getJSONObject("content").getJSONObject("data").getJSONArray("messageReadStatusList").toJavaList(Map.class);
        return list.size() > 1;

    }

    @Override
    public String getAccessToken() {

        String api = "/gettoken.json";
        GetClient postClient = executableClient.newGetClient(api);
        //Set the parameters
        postClient.addParameter("appkey", systemProperties.getZwddAuth().getAppKey());
        postClient.addParameter("appsecret", systemProperties.getZwddAuth().getAppSecret());

        //Call API
        String apiResult = postClient.get();

        return apiResult;
    }

    @Override
    public String sendUndoMessage(Map<String, String> params) {

        String api = "/tc/v2/openapi/task/create.json";
        PostClient postClient = zwddUtil.getPostClient(api);
        postClient.addParameter("subject", params.get("subject"));
        postClient.addParameter("creatorId", params.get("creatorId"));
        postClient.addParameter("bizTaskId", IdUtil.simpleUUID());
        postClient.addParameter("mobileUrl", params.get("mobileUrl"));
        postClient.addParameter("url", params.get("url"));
        postClient.addParameter("assigneeId", params.get("assigneeId"));
        postClient.addParameter("isSendDynamicCard", "true");
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        postClient.addParameter("isSendWindowNotice", "true");

        String apiResult = postClient.post();
        log.info("发送待办, apiResult :{}", apiResult);
        if (!JSONValidator.from(apiResult).validate()) {
            log.error("发送待办异常，api: {}", api);
            return null;
        }
        ResponseBean<Map<String, String>> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<Map<String, String>>>() {
                });
        if (!responseBean.isSuccess()) {
            log.error("发送待办异常，api: {}", api);
            return null;
        }
        Content<Map<String, String>> content = responseBean.getContent();
        if (content == null) {
            log.error("发送待办异常，api: {}", api);
        }
        Map<String, String> data = content.getData();
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(apiResult).getJSONObject("content").getJSONObject("data");
        return jsonObject.get("taskUuid").toString();


    }

    @Override
    public Integer sendDoMessage(Map<String, String> params) {
        String api = "/tc/openapi/task/finish.json";
        PostClient postClient = zwddUtil.getPostClient(api);
        postClient.addParameter("userId", params.get("userId"));
        postClient.addParameter("closePackage", "true");
        postClient.addParameter("taskUuid", params.get("taskUuid"));
        String apiResult = postClient.post();
        log.info("发送待办, apiResult :{}", apiResult);
        if (!JSONValidator.from(apiResult).validate()) {
            log.error("发送待办异常，api: {}", api);
            return null;
        }
        ResponseBean<Boolean> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<Boolean>>() {
                });

        if (!responseBean.isSuccess()) {
            log.error("发送待办异常，api: {}", api);
            return null;
        }
        Content<Boolean> content = responseBean.getContent();
        if (content == null) {
            log.error("发送待办异常，api: {}", api);
        }


        return content.getData() ? 1 : 0;

    }

    @Override
    public Integer cancelUndoEvent(Map<String, String> params) {
        String api = "/tc/openapi/task/cancel.json";
        PostClient postClient = zwddUtil.getPostClient(api);
        postClient.addParameter("userId", params.get("userId"));
        postClient.addParameter("taskUuid", params.get("taskUuid"));
        String apiResult = postClient.post();
        log.info("发送待办, apiResult :{}", apiResult);
        if (!JSONValidator.from(apiResult).validate()) {
            log.error("发送待办异常，api: {}", api);
            return null;
        }
        ResponseBean<Boolean> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<Boolean>>() {
                });
        if (!responseBean.isSuccess()) {
            log.error("发送待办异常，api: {}", api);
            return null;
        }
        Content<Boolean> content = responseBean.getContent();
        if (content == null) {
            log.error("发送待办异常，api: {}", api);
        }


        return content.getData() ? 1 : 0;
    }

    @Override
    public Boolean notification(NotificationMsgVo msgVo) {
        String api = "/message/workNotification";
        PostClient postClient = zwddUtil.getPostClient(api);
        String receiverIdsStr = StrUtil.join(",", msgVo.getReceiverIds());
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        postClient.addParameter("receiverIds", receiverIdsStr);
        postClient.addParameter("organizationCodes", "");
        postClient.addParameter("bizMsgId", IdUtil.simpleUUID());
        Map<String, Object> jsonMap = new HashMap<>();
        Map<String, Object> contentMap = new HashMap<>();
        Map<String, String> btnMap = new HashMap<>();
        btnMap.put("title", "阅读详情");
        btnMap.put("action_url", msgVo.getUrl());
        contentMap.put("title", msgVo.getTitle());
        contentMap.put("markdown", msgVo.getDesc());
        contentMap.put("imageUrl",msgVo.getImageUrl());
        contentMap.put("btn_orientation", "1");
        contentMap.put("btn_json_list", CollUtil.newArrayList(btnMap));
        jsonMap.put("msgtype", msgVo.getMsgType());
        jsonMap.put("action_card", contentMap);
        String msg = JSON.toJSONString(jsonMap);

        postClient.addParameter("msg", msg);
        List params = postClient.parameterList();
        //Call API
        String apiResult = postClient.post();

        ResponseBean<Boolean> responseBean = JSON
                .parseObject(apiResult, new TypeReference<ResponseBean<Boolean>>() {
                });
        log.info("浙政钉发送消息{}", apiResult);
        return responseBean.isSuccess();
    }

    /**
     * {
     * "msgtype": "action_card",
     * "action_card": {
     * "title": "习近平关于[不忘初心，牢记使命]摘编",
     * "imageUrl": "http://www.dingding.com/image.png",
     * "markdown": "中国共产党人的初心和使命，就是为中国人民谋幸福，为中华民族谋复兴，习近平说，中国共产党第十九次全国代表大会，是在全面建成小康社会决胜阶段、中国特色社会主义进入新时代的关键时期召开的一次十分重要的大会。",
     * "btn_orientation": "1",
     * "btn_json_list": [
     * {
     * "title": "阅读详情",
     * "action_url": "https://gov.dingtalk.com/"
     * }
     * ]
     * }
     * }
     */
    @Override
    public void notification() {

        String api = "/message/workNotification";
        PostClient postClient = zwddUtil.getPostClient(api);
        //Set the parameters
        postClient.addParameter("tenantId", systemProperties.getZwddAuth().getTenantId());
        postClient.addParameter("receiverIds", "78649792");
        postClient.addParameter("bizMsgId", IdUtil.simpleUUID());
        Map<String, String> jsonMap = new HashMap<>();
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("content", "test");
        jsonMap.put("msgType", "text");
        jsonMap.put("text", JSON.toJSONString(contentMap));
        postClient.addParameter("msg", JSON.toJSONString(jsonMap));
        //Call API
        String apiResult = postClient.post();
        System.out.println(apiResult);
//        List list = JSONObject.parseObject(apiResult).getJSONObject("content").getJSONObject("data").getJSONArray("messageReadStatusList").toJavaList(Map.class);
    }

    private List<String> getAllOrgCode(String organizationCode, List<String> orgCodeList, List<String> changeParentList)
            throws ExecutionException, InterruptedException {
        ArrayList<String> childCodeList = new ArrayList<>();
        Content<List<String>> content = null;
        Integer pageNo = 1;
        //拿到所有下一级code
        do {
            content = pageSubOrganizationCodes(organizationCode, pageNo);
            if (content != null && !CollectionUtils.isEmpty(content.getData())) {
                List<String> list = content.getData();
                //累加到最终code列表
                orgCodeList.addAll(list);
                //添加到子code列表
                childCodeList.addAll(list);
            }
            pageNo++;
        } while (Objects.nonNull(content) && !CollectionUtils.isEmpty(content.getData()) && content.getPageSize() >= content
                .getData().size());

        List<GetOrganizationByCode> orgList = new ArrayList<>();
        for (String orgCode : childCodeList) {
            //根据组织code查询详情
            GetOrganizationByCode organizationByCode = getOrganizationByCode(orgCode).get();
            if (Objects.nonNull(organizationByCode)) {
                orgList.add(organizationByCode);
            }
        }
        //批量插入组织详情
        if (!CollectionUtils.isEmpty(orgList)) {
            //						insertOrgBatch(orgList,changeParentList);
        }
        //循环最新查出来的code列表，查询下一个直到返回为空
        for (String orgCode : childCodeList) {
            content = pageSubOrganizationCodes(orgCode, 1);
            //如果不为空 继续累加
            if (content != null && !CollectionUtils.isEmpty(content.getData())) {
                this.getAllOrgCode(orgCode, orgCodeList, changeParentList);
            }
        }
        return orgCodeList;
    }

    private List<String> getAllUserCode(String orgCode) {
        ArrayList<String> userCodeList = new ArrayList<>();
        Content<List<String>> listContent = null;
        int pageNo = 1;
        do {
            listContent = pageOrganizationEmployeeCodes(orgCode, pageNo);
            if (Objects.nonNull(listContent) && !CollectionUtils.isEmpty(listContent.getData())) {
                userCodeList.addAll(listContent.getData());
                for (String userCode : listContent.getData()) {
                    //更新或插入用户
                    this.updateOrInsertUser(userCode, orgCode);
                }
            }
            pageNo++;
        } while (Objects.nonNull(listContent) && !CollectionUtils.isEmpty(listContent.getData())
                && listContent.getPageSize() >= listContent.getData().size());
        return userCodeList;
    }

    public void updateOrInsertUser(String userCode, String orgCode) {
        GetEmployeeByCode employeeByCode = getEmployeeByCode(userCode);
        if (employeeByCode == null) {
            return;
        }
        //1、调用接口，获取政务钉钉account_id
        List<ListEmployeeAccountIds> listEmployeeAccountIds = listEmployeeAccountIds(userCode);
        if (CollectionUtils.isEmpty(listEmployeeAccountIds)) {
            return;
        }

        //因为只传一个 所以第一个即为该员工的account_id
        ListEmployeeAccountIds employeeByAccountId = listEmployeeAccountIds.get(0);

        QueryWrapper<ZwddUserRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ZwddUserRelation::getEmployeeCode, employeeByCode.getEmployeeCode());
        ZwddUserRelation zwddUserRelation = zwddUserRelationMapper.selectOne(queryWrapper);
        if (zwddUserRelation == null) {
            AddUserReq user = new AddUserReq();
            user.setMobile(employeeByAccountId.getAccountCode());
            user.setUsername(employeeByCode.getEmployeeName());
            user.setUserType(2);
            user.setOrganizeCode("000000");
            user.setLoginName(PinyinUtil.getPinyin(employeeByCode.getEmployeeName(), ""));
            user.setPassword(PinyinUtil.getPinyin(employeeByCode.getEmployeeName(), "") + 123456);
            String code = userService.addUserReturnCode(user);
            zwddUserRelation = new ZwddUserRelation();
            zwddUserRelation.setUsername(employeeByCode.getEmployeeName());
            zwddUserRelation.setUserCode(code);
            zwddUserRelation.setMobile(employeeByCode.getMobile());
            zwddUserRelation.setGmtCreate(LocalDateTime.now());
            zwddUserRelation.setGmtUpdate(LocalDateTime.now());
            zwddUserRelation.setEmployeeCode(employeeByCode.getEmployeeCode());
            zwddUserRelationMapper.insert(zwddUserRelation);
        }

    }

    private void insertOrUpdateByMobile(GetEmployeeByCode employeeByCode) {

        List<ListEmployeeAccountIds> listEmployeeAccountIds = listEmployeeAccountIds(employeeByCode.getEmployeeCode());
        if (CollectionUtils.isEmpty(listEmployeeAccountIds)) {
            return;
        }

        //因为只传一个 所以第一个即为该员工的account_id
        ListEmployeeAccountIds employeeByAccountId = listEmployeeAccountIds.get(0);
        QueryWrapper<ZwddUserRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ZwddUserRelation::getEmployeeCode, employeeByCode.getEmployeeCode());
        ZwddUserRelation zwddUserRelation = zwddUserRelationMapper.selectOne(queryWrapper);
        if (zwddUserRelation == null) {
            AddUserReq user = new AddUserReq();
            user.setMobile(employeeByCode.getMobile());
            user.setUsername(employeeByCode.getEmployeeName());
            user.setUserType(2);
            user.setOrganizeCode("000000");
            user.setLoginName(PinyinUtil.getPinyin(employeeByCode.getEmployeeName(), ""));
            user.setPassword(PinyinUtil.getPinyin(employeeByCode.getEmployeeName(), "") + 123456);
            String code = userService.addUserReturnCode(user);
            zwddUserRelation = new ZwddUserRelation();
            zwddUserRelation.setUsername(employeeByCode.getEmployeeName());
            zwddUserRelation.setUserCode(code);
            zwddUserRelation.setMobile(employeeByCode.getMobile());
            zwddUserRelation.setGmtCreate(LocalDateTime.now());
            zwddUserRelation.setGmtUpdate(LocalDateTime.now());
            zwddUserRelation.setEmployeeCode(employeeByCode.getEmployeeCode());
            zwddUserRelationMapper.insert(zwddUserRelation);
        }
    }

}

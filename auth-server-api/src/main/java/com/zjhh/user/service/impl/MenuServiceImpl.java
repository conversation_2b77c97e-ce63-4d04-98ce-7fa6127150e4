package com.zjhh.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.zjhh.comm.base.SortComparable;
import com.zjhh.comm.config.SystemProperties;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.utils.TreeUtils;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.user.dao.entity.Component;
import com.zjhh.user.dao.entity.Menu;
import com.zjhh.user.dao.entity.RoleMenu;
import com.zjhh.user.dao.entity.UserRole;
import com.zjhh.user.dao.mapper.ComponentMapper;
import com.zjhh.user.dao.mapper.MenuMapper;
import com.zjhh.user.dao.mapper.RoleMenuMapper;
import com.zjhh.user.dao.mapper.UserRoleMapper;
import com.zjhh.user.dto.MenuDto;
import com.zjhh.user.enume.MenuTypeEnum;
import com.zjhh.user.request.AddMenuReq;
import com.zjhh.user.request.UpdateMenuReq;
import com.zjhh.user.service.MenuService;
import com.zjhh.user.vo.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/3/2 16:45
 */
@Slf4j
@DS("master")
@Service
public class MenuServiceImpl extends BaseServiceImpl implements MenuService {

    @Resource
    private MenuMapper menuMapper;

    @Resource
    private UserSession userSession;

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    private RoleMenuMapper roleMenuMapper;

    @Resource
    private ComponentMapper componentMapper;

    private List<Menu> personalMenus;

    private List<Menu> licenceMenus;

    @Resource
    private SystemProperties systemProperties;

    @Override
    public Tree<String> getRouter() {
        Tree<String> tree = new Tree<>();
        tree.put("router", "base");
        if (!userSession.checkLicence()) {
            tree.setChildren(createRouter(new ArrayList<>()));
            return tree;
        }
        if (userSession.isSuperManager()) {
            List<Menu> list = menuMapper.selectList(null);
            tree.setChildren(createRoutesChildren(createRouter(list)));
            return tree;
        }
        LoginVo loginVo = userSession.getSessionLoginVo();
        QueryWrapper<UserRole> userRoleWrapper = new QueryWrapper<>();
        userRoleWrapper.lambda().eq(UserRole::getUserCode, loginVo.getCode())
                .select(UserRole::getRoleCode);
        List<String> roleCodes = userRoleMapper.selectStrings(userRoleWrapper);
        if (CollUtil.isEmpty(roleCodes)) {
            tree.setChildren(createRoutesChildren(createRouter(new ArrayList<>())));
            return tree;
        }
        QueryWrapper<RoleMenu> roleMenuWrapper = new QueryWrapper<>();
        roleMenuWrapper.lambda().in(RoleMenu::getRoleCode, roleCodes)
                .select(RoleMenu::getMenuCode);
        List<String> roleMenuCodes = roleMenuMapper.selectStrings(roleMenuWrapper);
        tree.setChildren(createRoutesChildren(createRouter(listAllMenuCodes(roleMenuCodes, true))));
        return tree;
    }

    private List<Tree<String>> createRoutesChildren(List<Tree<String>> list) {
        List<Tree<String>> res = list.get(0).getChildren();
        if (CollUtil.isNotEmpty(res)) {
            res.forEach(vo -> {
                if (ObjectUtil.equals("navMenu", vo.get("router"))) {
                    Tree<String> navTree = new Tree<>();
                    navTree.putExtra("path", "nav");
                    navTree.putExtra("router", "nav");
                    navTree.putExtra("name", vo.getName() + "菜单总览");
                    navTree.putExtra("invisible", true);
                    navTree.putExtra("menuCode", vo.get("menuCode"));
                    if (ObjectUtil.isNull(vo.getChildren())) {
                        vo.setChildren(new ArrayList<>());
                    }
                    vo.getChildren().add(navTree);
                }
            });
        } else {
            list.get(0).setChildren(new ArrayList<>());
        }
        return list;
    }

    @Override
    public void addMenu(AddMenuReq req) {
        Menu menu = new Menu();
        menu.setCode(IdUtil.simpleUUID());
        menu.setName(req.getName());
        menu.setIcon(req.getIcon());
        menu.setMenuType(req.getMenuType());
        menu.setSort(req.getSort());
        menu.setShowHome(BooleanUtil.isTrue(req.getShowHome()));
        menu.setHomeSort(req.getHomeSort());
        if (req.getMenuType() == MenuTypeEnum.BUTTON.value()) {
            if (ObjectUtil.isNull(req.getParentId())) {
                throw new BizException("添加按钮父菜单不能为空！");
            }
            if (StrUtil.isBlank(req.getPerm())) {
                throw new BizException("添加按钮权限标识不能为空！");
            }
            Menu parentMenu = checkMenu(req.getParentId());
            QueryWrapper<Menu> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(Menu::getMenuType, MenuTypeEnum.BUTTON.value())
                    .eq(Menu::getParentCode, parentMenu.getParentCode())
                    .eq(Menu::getPerm, req.getPerm());
            if (menuMapper.selectCount(wrapper) > 0) {
                throw new BizException("已存在相同的按钮，请勿重复添加！");
            }
            menu.setParentCode(parentMenu.getCode());
            menu.setChildMenu(parentMenu.getChildMenu() + 1);
            menu.setExternalLink(false);
            menu.setExternalInner(true);
            menu.setPerm(req.getPerm());
        } else {
            if (Objects.isNull(req.getParentId())) {
                menu.setParentCode("base");
                menu.setChildMenu(1);
            } else {
                Menu parentMenu = checkMenu(req.getParentId());
                menu.setParentCode(parentMenu.getCode());
                menu.setChildMenu(parentMenu.getChildMenu() + 1);
            }
            if (Objects.isNull(req.getExternalLink())) {
                menu.setExternalLink(false);
            } else {
                menu.setExternalLink(req.getExternalLink());
            }
            if (Objects.isNull(req.getExternalInner())) {
                menu.setExternalInner(false);
            } else {
                menu.setExternalInner(req.getExternalInner());
            }
            if (menu.getExternalLink()) {
                menu.setRouter("externalPage");
            } else {
                menu.setRouter(req.getRouter());
            }
            String path = menu.getRouter().toLowerCase() + System.currentTimeMillis();
            menu.setPath(path);
            menu.setRedirect(req.getRedirect());
            menu.setPage(req.getPage());
        }
        menu.setMenuImg(req.getMenuImg());
        menu.setMappingMenu(req.getMappingMenu());
        menuMapper.insert(menu);
    }

    @Override
    public List<Tree<String>> allMenus() {
        QueryWrapper<Menu> wrapper = new QueryWrapper<>();
        List<Menu> list;
        if (userSession.nonSuperManager()) {
            List<String> roleCodes = listAssignRoleCodes(userSession.getUserCode());
            if (CollUtil.isEmpty(roleCodes)) {
                list = CollUtil.newArrayList();
            } else {
                QueryWrapper<RoleMenu> roleMenuWrapper = new QueryWrapper<>();
                roleMenuWrapper.lambda().in(RoleMenu::getRoleCode, roleCodes).select(RoleMenu::getMenuCode);
                List<String> menuCodes = roleMenuMapper.selectStrings(roleMenuWrapper);
                list = listAllMenuCodes(menuCodes, true);
            }
        } else {
            list = menuMapper.selectList(wrapper);
        }
        list.forEach(menu -> {
            if (menu.getMenuType() == MenuTypeEnum.BUTTON.value()) {
                menu.setRouter("按钮");
            } else if (ObjectUtil.equals(menu.getRouter(), "navMenu")) {
                menu.setRouter("导航菜单");
            } else if (ObjectUtil.equals(menu.getRouter(), "menu")) {
                menu.setRouter("菜单");
            } else {
                menu.setRouter("页面");
            }
        });
        return createMenu(list);
    }

    @Override
    public List<Tree<String>> listParentMenus() {
        List<Menu> list;
        if (userSession.nonSuperManager()) {
            List<String> roleCodes = listAssignRoleCodes(userSession.getUserCode());
            if (CollUtil.isEmpty(roleCodes)) {
                list = CollUtil.newArrayList();
            } else {
                QueryWrapper<RoleMenu> roleMenuWrapper = new QueryWrapper<>();
                roleMenuWrapper.lambda().in(RoleMenu::getRoleCode, roleCodes).select(RoleMenu::getMenuCode);
                List<String> menuCodes = roleMenuMapper.selectStrings(roleMenuWrapper);
                list = listAllMenuCodes(menuCodes, false);
            }
        } else {
            QueryWrapper<Menu> wrapper = new QueryWrapper<>();
            wrapper.lambda().ne(Menu::getMenuType, MenuTypeEnum.BUTTON.value());
            list = menuMapper.selectList(wrapper);
        }
        return createMenu(list);
    }

    @Override
    public void updateMenu(UpdateMenuReq req) {
        Menu menu = checkMenu(req.getMenuId());
        BeanUtil.copyProperties(req, menu);
        menu.setPath(menu.getRouter().toLowerCase() + System.currentTimeMillis());
        menu.setGmtUpdate(LocalDateTime.now());
        if (StrUtil.isNotBlank(req.getParentId())) {
            Menu parentMenu = checkMenu(req.getParentId());
            menu.setParentCode(parentMenu.getCode());
        } else {
            menu.setParentCode("base");
            menu.setChildMenu(1);
        }
        if (req.getExternalLink()) {
            menu.setRouter("externalPage");
        }
        menu.setMenuImg(req.getMenuImg());
        menu.setMappingMenu(req.getMappingMenu());
        menuMapper.updateById(menu);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void deleteMenu(String menuId) {
        Menu menu = checkMenu(menuId);
        QueryWrapper<Menu> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Menu::getParentCode, menu.getParentCode());
        if (menuMapper.selectCount(wrapper) > 0) {
            throw new BizException("存在子菜单，请先删除子菜单或者进行批量删除操作！");
        }
        menuMapper.deleteById(menuId);
        QueryWrapper<RoleMenu> roleMenuWrapper = new QueryWrapper<>();
        roleMenuWrapper.lambda().eq(RoleMenu::getMenuCode, menu.getCode());
        roleMenuMapper.delete(roleMenuWrapper);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void deleteBatch(List<String> menuIds) {
        List<Menu> menus = menuMapper.selectBatchIds(menuIds);
        Set<String> menuCodes = CollUtil.newHashSet();
        menus.forEach(menu -> menuCodes.add(menu.getCode()));
        QueryWrapper<Menu> menuWrapper = new QueryWrapper<>();
        menuWrapper.lambda().in(Menu::getParentCode, menuCodes).select(Menu::getCode);
        List<String> existCodes = menuMapper.selectStrings(menuWrapper);
        if (!menuCodes.containsAll(existCodes)) {
            throw new BizException("子菜单必须跟父菜单一起删除！");
        }
        QueryWrapper<RoleMenu> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(RoleMenu::getMenuCode, menuCodes);
        roleMenuMapper.delete(wrapper);
        menuMapper.deleteBatchIds(menuIds);
    }

    @Override
    public MenuVo getMenu(String id) {
        Menu menu = checkMenu(id);
        MenuVo vo = BeanUtil.copyProperties(menu, MenuVo.class);
        if (ObjectUtil.equals("base", vo.getParentCode())) {
            vo.setParentId("0");
            return vo;
        }
        QueryWrapper<Menu> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Menu::getCode, menu.getParentCode());
        vo.setParentId(menuMapper.selectOne(wrapper).getId());
        return vo;
    }

    @Override
    public List<ComponentVo> listComponents() {
        QueryWrapper<Component> wrapper = new QueryWrapper<>();
        wrapper.lambda().orderByAsc(Component::getSort);
        return createComponents(componentMapper.selectList(wrapper));
    }

    @Override
    public List<ComponentVo> listMenuSelect() {
        QueryWrapper<Component> wrapper = new QueryWrapper<>();
        wrapper.lambda().ne(Component::getComponent, "button")
                .orderByAsc(Component::getSort);
        return createComponents(componentMapper.selectList(wrapper));
    }

    @Override
    public List<Component> listButtonSelect(String menuId) {
        Menu menu = checkMenu(menuId);
        QueryWrapper<Menu> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Menu::getParentCode, menu.getCode())
                .eq(Menu::getMenuType, MenuTypeEnum.BUTTON.value())
                .select(Menu::getPerm);
        List<String> existButtons = menuMapper.selectStrings(wrapper);
        List<Component> buttonSelectList = componentMapper.listComponentButton(menu.getRouter());
        return buttonSelectList.stream().filter(buttonSelect -> !existButtons.contains(buttonSelect.getPerm())).collect(Collectors.toList());
    }


    @Override
    public FullPathVo getFullPath(String router) {
        QueryWrapper<Menu> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Menu::getRouter, router)
                .orderByAsc(Menu::getId).last("limit 1");
        Menu menu = menuMapper.selectOne(wrapper);
        return getFullPath(menu);
    }

    @Override
    public MenuInfoVo getMenuInfo(String router) {
        MenuInfoVo vo = new MenuInfoVo();
        QueryWrapper<Menu> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Menu::getRouter, router)
                .orderByAsc(Menu::getId).last("limit 1");
        Menu menu = menuMapper.selectOne(wrapper);
        if (ObjectUtil.isNull(menu)) {
            throw new BizException("该菜单不存在，请联系管理员！");
        }
        vo.setMenuCode(menu.getCode());
        if (userSession.isSuperManager()) {
            wrapper.clear();
            wrapper.lambda().eq(Menu::getParentCode, menu.getCode())
                    .eq(Menu::getMenuType, MenuTypeEnum.BUTTON.value())
                    .select(Menu::getPerm);
            vo.setButtons(menuMapper.selectStrings(wrapper));
        } else {
            vo.setButtons(menuMapper.listButton(menu.getCode(), userSession.getUserCode()));
        }
        FullPathVo fullPathVo = getFullPath(menu);
        vo.setIsRedirect(fullPathVo.getIsRedirect());
        vo.setFullPath(fullPathVo.getFullPath());
        return vo;
    }

    @Override
    public FullPathVo getFullPathByMenuCode(String menuCode) {
        return getFullPath(menuMapper.selectByCode(menuCode));
    }

    private FullPathVo getFullPath(Menu menu) {
        FullPathVo vo = new FullPathVo();
        if (ObjectUtil.isNull(menu)) {
            vo.setIsRedirect(false);
            return vo;
        }
        if (userSession.nonSuperManager()) {
            int count = roleMenuMapper.countUserMenu(userSession.getUserCode(), menu.getCode());
            if (count <= 0) {
                vo.setIsRedirect(false);
                return vo;
            }
        }
        StringBuilder path = new StringBuilder(menu.getPath());
        while (ObjectUtil.notEqual("root", menu.getParentCode()) && ObjectUtil.notEqual("base", menu.getParentCode())) {
            menu = menuMapper.selectByCode(menu.getParentCode());
            path.insert(0, menu.getPath() + "/");
        }
        vo.setIsRedirect(true);
        vo.setFullPath("/" + path);
        return vo;
    }

    @Override
    public List<CockpitMenuVo> listCockpitMenu() {
        QueryWrapper<Menu> menuWrapper = new QueryWrapper<>();
        menuWrapper.lambda().eq(Menu::getRouter, "navMenu")
                .eq(Menu::getParentCode, "root");
        List<Menu> menus = menuMapper.selectList(menuWrapper);
        List<CockpitMenuVo> res = new ArrayList<>();
        if (userSession.isSuperManager()) {
            menus.forEach(menu -> res.add(BeanUtil.copyProperties(menu, CockpitMenuVo.class)));
        } else {
            LoginVo loginVo = userSession.getSessionLoginVo();
            QueryWrapper<UserRole> userRoleWrapper = new QueryWrapper<>();
            userRoleWrapper.lambda().eq(UserRole::getUserCode, loginVo.getCode())
                    .select(UserRole::getRoleCode);
            List<String> roleCodes = userRoleMapper.selectStrings(userRoleWrapper);
            if (CollUtil.isEmpty(roleCodes)) {
                menus.forEach(menu -> res.add(BeanUtil.copyProperties(menu, CockpitMenuVo.class, "path")));
            } else {
                QueryWrapper<RoleMenu> roleMenuWrapper = new QueryWrapper<>();
                roleMenuWrapper.lambda().in(RoleMenu::getRoleCode, roleCodes)
                        .select(RoleMenu::getMenuCode);
                List<String> roleMenuCodes = roleMenuMapper.selectStrings(roleMenuWrapper);
                List<Menu> perMenus = listAllMenuCodes(roleMenuCodes, false);
                menus.forEach(menu -> res.add(perMenus.contains(menu) ? BeanUtil.copyProperties(menu, CockpitMenuVo.class)
                        : BeanUtil.copyProperties(menu, CockpitMenuVo.class, "path")));
            }

        }
        CollUtil.sort(res, (o1, o2) -> {
            if (o1.getSort().length() != o2.getSort().length()) {
                return o1.getSort().length() - o2.getSort().length();
            }
            return o1.getSort().compareTo(o2.getSort());
        });
        return res;
    }

    @Override
    public Menu getMenuProperties(String menuCode, SFunction<Menu, ?>... properties) {
        QueryWrapper<Menu> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Menu::getCode, menuCode)
                .select(properties);
        return menuMapper.selectOne(wrapper);
    }

    @Override
    public TreeVo getMenuTree() {
        List<TreeVo> list = menuMapper.findMenuTree();
//        Set<String> set = new HashSet<>();
//        list.forEach(treeVo -> set.add(treeVo.getParentKey()));
//        list.removeIf(treeVo -> (("menu".equals(treeVo.getExtra()) || "navMenu".equals(treeVo.getExtra())) && !set.contains(treeVo.getKey())));
        List<TreeVo> res = TreeUtils.menuTree(list, "base");
        if (CollUtil.isEmpty(res)) {
            return new TreeVo();
        }
        deleteMenuTree(res);
        TreeVo treeVo = new TreeVo();
        treeVo.setKey("base");
        treeVo.setTitle("可配置菜单");
        treeVo.setChildren(res);
        return treeVo;
    }

    @Override
    public List<MenuDto> listMenu(String menuCode) {
        List<MenuDto> res;
        if (StrUtil.equals(menuCode, "base")) {
            List<Menu> menus = menuMapper.selectList(null);
            res = new ArrayList<>();
            menus.forEach(menu -> {
                MenuDto menuDto = new MenuDto();
                menuDto.setMenuCode(menu.getCode());
                menuDto.setMenuName(menu.getName());
                menuDto.setSort(menu.getSort());
                res.add(menuDto);
            });
        } else {
            res = menuMapper.listAllChildMenu(menuCode);
        }
        return res;
    }

    @Override
    public List<ShowHomeMenuVo> listShowHomeMenu() {
        QueryWrapper<Menu> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Menu::getShowHome, true)
                .orderByAsc(Menu::getHomeSort);
        List<Menu> menus = menuMapper.selectList(wrapper);
        List<ShowHomeMenuVo> list = new ArrayList<>(menus.size());
        Tree<String> tree = getRouter();
        for (Menu menu : menus) {
            if (ObjectUtil.isNotNull(tree.getNode(menu.getCode()))) {
                ShowHomeMenuVo vo = new ShowHomeMenuVo();
                vo.setMenuCode(menu.getCode());
                vo.setMenuName(menu.getName());
                vo.setIcon(menu.getIcon());
                vo.setRouter(menu.getRouter());
                vo.setLeaf(checkLeaf(menu.getRouter()));
                list.add(vo);
            }
            if (list.size() >= 7) {
                break;
            }
        }
        return list;
    }

    @Override
    public TreeVo getDescMenuTree() {
        List<TreeVo> list = menuMapper.findDescMenuTree();
        List<TreeVo> res = TreeUtils.menuTree(list, "base");
        TreeVo vo = new TreeVo();
        vo.setKey("base");
        vo.setTitle("数据说明");
        vo.setExtra("desc");
        vo.setChildren(res);
        return vo;
    }

    @Override
    public String getOneMenuByComponent(String component) {
        return menuMapper.findOneMenuByComponent(component);
    }

    private void deleteMenuTree(List<TreeVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        Iterator<TreeVo> iterator = list.iterator();
        while (iterator.hasNext()) {
            TreeVo treeVo = iterator.next();
            if ("root".equals(treeVo.getExtra()) || "menu".equals(treeVo.getExtra()) || "navMenu".equals(treeVo.getExtra())) {
                deleteMenuTree(treeVo.getChildren());
                if (CollUtil.isEmpty(treeVo.getChildren())) {
                    iterator.remove();
                }
            }
        }
    }

    @Override
    public List<TreeSelectVo> listSelectMapMenu() {
        List<TreeSelectVo> list = menuMapper.listSelectMapMenu();
        return TreeUtils.listToTree(list, "base");
    }

    @Override
    public Menu getMenu(String code, SFunction<Menu, ?>... columns) {
        QueryWrapper<Menu> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Menu::getCode, code)
                .select(columns);
        return menuMapper.selectOne(wrapper);
    }

    @Override
    public TreeVo getJumpMenuTree() {
        List<TreeVo> list = menuMapper.findJumpMenuTree();
        List<TreeVo> res = TreeUtils.menuTree(list, "base");
        if (CollUtil.isEmpty(res)) {
            return new TreeVo();
        }
        deleteMenuTree(res);
        TreeVo treeVo = new TreeVo();
        treeVo.setKey("base");
        treeVo.setTitle("可配置菜单");
        treeVo.setChildren(res);
        return treeVo;
    }

    private Boolean checkLeaf(String router) {
        if (ObjectUtil.isNull(router)) {
            return false;
        }
        return !StrUtil.equals("root", router) && !StrUtil.equals("menu", router) && !StrUtil.equals("navMenu", router);
    }

    private List<ComponentVo> createComponents(List<Component> list) {
        ComponentVo menuVo = new ComponentVo();
        menuVo.setComponent("menu");
        menuVo.setKey(IdUtil.simpleUUID());
        menuVo.setParentKey("root");
        menuVo.setTitle("菜单");
        menuVo.setDisabled(false);
        ComponentVo navMenuVo = new ComponentVo();
        navMenuVo.setComponent("navMenu");
        navMenuVo.setKey(IdUtil.simpleUUID());
        navMenuVo.setParentKey("root");
        navMenuVo.setTitle("导航菜单");
        navMenuVo.setDisabled(false);
        List<ComponentVo> child = new ArrayList<>();
        child.add(menuVo);
        child.add(navMenuVo);
        list.forEach(component -> {
            ComponentVo componentVo = new ComponentVo();
            if ("menu".equals(component.getComponent())) {
                componentVo.setComponent(IdUtil.simpleUUID());
                componentVo.setDisabled(true);
            } else {
                componentVo.setComponent(component.getComponent());
                componentVo.setDisabled(false);
            }
            componentVo.setKey(component.getCode());
            componentVo.setTitle(component.getTitle());
            componentVo.setParentKey(component.getParentCode());
            child.add(componentVo);
        });
        return componentTree(child, "root");
    }

    public static List<ComponentVo> componentTree(List<ComponentVo> list, String parentKey) {
        List<ComponentVo> res = list.stream().filter(item -> StrUtil.equals(parentKey, item.getParentKey())).collect(Collectors.toList());
        if (CollUtil.isEmpty(res)) {
            return res;
        }
        res.forEach(item -> item.setChildren(componentTree(list, item.getKey())));
        return res;
    }

    /**
     * 构建路由信息列表
     *
     * @param menus
     * @return
     */
    List<Tree<String>> createRouter(List<Menu> menus) {
        List<Menu> buttonList = menus.stream().filter(menu -> menu.getMenuType() == MenuTypeEnum.BUTTON.value()).collect(Collectors.toList());
        List<Menu> menuList = menus.stream().filter(menu -> menu.getMenuType() != MenuTypeEnum.BUTTON.value()).collect(Collectors.toList());
        if (!checkHasRoot(menus)) {
            menuList.add(menuMapper.selectByCode("root"));
        }
        if (systemProperties.isPc()) {
            menuList.addAll(getPersonalMenus());
            if (userSession.isSuperManager()) {
                menuList.addAll(getLicenceMenus());
            }
        }
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        menuList.forEach(menu -> {
            TreeNode<String> treeNode = new TreeNode<>(menu.getCode(), menu.getParentCode(), menu.getName(), new SortComparable(menu.getSort()));
            Map<String, Object> map = new HashMap<>();
            map.put("menu", menu);
            treeNode.setExtra(map);
            nodeList.add(treeNode);
        });
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("menuCode");
        treeNodeConfig.setNameKey("name");
        treeNodeConfig.setParentIdKey("parentCode");
        return TreeUtil.build(nodeList, "base", treeNodeConfig, (treeNode, tree) -> {
            tree.setId(treeNode.getId());
            tree.setName(treeNode.getName());
            tree.setParentId(treeNode.getParentId());
            tree.setWeight(treeNode.getWeight());
            Menu menu = (Menu) treeNode.getExtra().get("menu");
            tree.putExtra("icon", menu.getIcon());
            tree.putExtra("path", menu.getPath());
            tree.putExtra("router", menu.getRouter());
            tree.putExtra("sort", menu.getSort());
            if (menu.getExternalInner()) {
                tree.putExtra("page", menu.getPage());
            } else {
                tree.putExtra("link", menu.getPage());
            }
            List<String> operates = new ArrayList<>();
            buttonList.forEach(button -> {
                if (button.getParentCode().equals(menu.getCode())) {
                    operates.add(button.getPerm());
                }
            });
            tree.putExtra("operates", operates);
            tree.putExtra("menuImg", menu.getMenuImg());
        });
    }

    /**
     * 构建所有菜单列表
     *
     * @param menus
     * @return
     */
    List<Tree<String>> createMenu(List<Menu> menus) {
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        menus.forEach(menu -> {
            TreeNode<String> treeNode = new TreeNode<>(menu.getCode(), menu.getParentCode(), menu.getName(), new SortComparable(menu.getSort()));
            Map<String, Object> map = new HashMap<>();
            map.put("menu", menu);
            treeNode.setExtra(map);
            nodeList.add(treeNode);
        });
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("menuCode");
        treeNodeConfig.setNameKey("name");
        treeNodeConfig.setParentIdKey("parentCode");
        return TreeUtil.build(nodeList, "base", treeNodeConfig, (treeNode, tree) -> {
            tree.setId(treeNode.getId());
            tree.setName(treeNode.getName());
            tree.setParentId(treeNode.getParentId());
            tree.setWeight(treeNode.getWeight());
            Menu menu = (Menu) treeNode.getExtra().get("menu");
            tree.putExtra("icon", menu.getIcon());
            tree.putExtra("menuId", menu.getId());
            tree.putExtra("menuType", menu.getMenuType());
            tree.putExtra("path", menu.getPath());
            tree.putExtra("router", menu.getRouter());
            tree.putExtra("sort", menu.getSort());
            tree.putExtra("showHome", menu.getShowHome());
            tree.putExtra("homeSort", menu.getHomeSort());
            tree.putExtra("externalLink", menu.getExternalLink());
            if (menu.getExternalInner()) {
                tree.putExtra("page", menu.getPage());
            } else {
                tree.putExtra("link", menu.getPage());
            }
        });
    }

    private List<Menu> getPersonalMenus() {
        if (CollUtil.isNotEmpty(personalMenus)) {
            return personalMenus;
        }
        personalMenus = new ArrayList<>();
        Menu personalMenu = new Menu();
        personalMenu.setCode("personal");
        personalMenu.setName("个人页");
        personalMenu.setPath("personal");
        personalMenu.setIcon("user");
        personalMenu.setRouter("navMenu");
        personalMenu.setParentCode("root");
        personalMenu.setExternalInner(true);
        personalMenu.setSort(String.valueOf(Integer.MAX_VALUE - 1));
        personalMenus.add(personalMenu);
        Menu profileMenu = new Menu();
        profileMenu.setCode("profile");
        profileMenu.setName("个人中心");
        profileMenu.setPath("profile");
        profileMenu.setRouter("profile");
        profileMenu.setExternalInner(true);
        profileMenu.setParentCode("personal");
        profileMenu.setSort("1");
        personalMenus.add(profileMenu);
        return personalMenus;
    }

    private List<Menu> getLicenceMenus() {
        if (CollUtil.isNotEmpty(licenceMenus)) {
            return licenceMenus;
        }
        licenceMenus = new ArrayList<>();
        Menu licenceMenu = new Menu();
        licenceMenu.setCode("licence");
        licenceMenu.setName("密钥管理");
        licenceMenu.setPath("licence");
        licenceMenu.setIcon("key");
        licenceMenu.setRouter("navMenu");
        licenceMenu.setParentCode("root");
        licenceMenu.setExternalInner(true);
        licenceMenu.setSort(String.valueOf(Integer.MAX_VALUE));
        licenceMenus.add(licenceMenu);
        Menu uploadLicenseMenu = new Menu();
        uploadLicenseMenu.setCode("uploadlicense");
        uploadLicenseMenu.setName("上传licence");
        uploadLicenseMenu.setPath("uploadLicense");
        uploadLicenseMenu.setRouter("uploadLicense");
        uploadLicenseMenu.setExternalInner(true);
        uploadLicenseMenu.setParentCode("licence");
        uploadLicenseMenu.setSort("1");
        licenceMenus.add(uploadLicenseMenu);
        Menu secretKeyMenu = new Menu();
        secretKeyMenu.setCode("secretkey");
        secretKeyMenu.setName("密钥生成");
        secretKeyMenu.setPath("secretKey");
        secretKeyMenu.setRouter("secretKey");
        secretKeyMenu.setExternalInner(true);
        secretKeyMenu.setParentCode("licence");
        secretKeyMenu.setSort("2");
        licenceMenus.add(secretKeyMenu);
        return licenceMenus;
    }

    private boolean checkHasRoot(List<Menu> menus) {
        if (CollUtil.isEmpty(menus)) {
            return false;
        }
        for (Menu menu : menus) {
            if (StrUtil.equals(menu.getCode(), "root")) {
                return true;
            }
        }
        return false;
    }
}

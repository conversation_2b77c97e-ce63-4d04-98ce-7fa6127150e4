package com.zjhh.user.service;

import com.zjhh.user.request.FOALoginReq;
import com.zjhh.user.request.LoginReq;
import com.zjhh.user.request.OpenLoginReq;
import com.zjhh.user.request.StThirdLoginReq;
import com.zjhh.user.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/6/12 10:46
 */
public interface LoginService {

    /**
     * 登录
     *
     * @param req
     * @return
     */
    LoginVo login(LoginReq req);

    /**
     * 退出登录
     *
     * @return
     */
    void logout();

    /**
     * 生成图形验证码
     *
     * @return
     */
    CaptchaVo createCaptcha();

    /**
     * 获取初始化参数配置
     *
     * @return
     */
    InitConfigVo getInitConfig();

    /**
     * 扫码登录
     *
     * @param code
     * @return
     */
    ZwddLoginVo getEmployeeCodeByQrCode(String code);

    ZwddLoginVo getEmployeeCodeByAuthCode(String authCode);

    boolean fullSyncByMobile(List<String> mobileList);

    InitSystemVo getInitSystem();


    /**
     * 移动端登录
     *
     * @param req
     * @return
     */
    LoginVo mobileLogin(LoginReq req);

    /**
     * 移动端后台管理登录
     *
     * @param req
     * @return
     */
    LoginVo mobileConsoleLogin(LoginReq req);

    /**
     * 通用登录
     *
     * @param req
     * @return
     */
    LoginVo openLogin(OpenLoginReq req);

    /**
     * @return
     */
    Integer countOnlineUser();

    /**
     * 第三方登录
     *
     * @param req
     * @return
     */
    LoginVo thirdLogin(StThirdLoginReq req);

    /**
     * 省厅OA单点登录
     *
     * @param req
     * @return
     */
    LoginVo foaLogin(FOALoginReq req);

    /**
     * 根据用户名登录
     *
     * @param loginName
     * @return
     */
    LoginVo loginByLoginName(String loginName);
}

package com.zjhh.user.service;

import com.zjhh.db.comm.Page;
import com.zjhh.user.dao.entity.Role;
import com.zjhh.user.dao.entity.User;
import com.zjhh.user.request.*;
import com.zjhh.user.vo.RoleButtonVo;
import com.zjhh.user.vo.RoleMenuAllVo;
import com.zjhh.user.vo.RoleVo;
import com.zjhh.user.vo.UserVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/6/15 10:05
 */
public interface RoleService {

    /**
     * 新增角色
     *
     * @param req
     * @return
     */
    void addRole(AddRoleReq req);

    /**
     * 修改角色
     *
     * @param req
     * @return
     */
    void updateRole(UpdateRoleReq req);

    /**
     * 验证role name
     *
     * @param roleName
     * @return
     */
    Boolean checkRoleName(String roleName);

    /**
     * 获取角色列表
     *
     * @param req
     * @return
     */
    Page<RoleVo> pageRole(PageRoleReq req);

    /**
     * 获取所有的角色数据
     *
     * @return
     */
    List<RoleVo> listRole();

    /**
     * 删除角色
     *
     * @param roleId
     * @return
     */
    void deleteRole(String roleId);

    /**
     * 添加用户角色
     *
     * @param roleId
     * @param userIds
     * @return
     */
    void addRoleUser(String roleId, List<String> userIds);

    /**
     * 删除用户角色
     *
     * @param roleId
     * @param userIds
     * @return
     */
    void deleteRoleUser(String roleId, List<String> userIds);


    /**
     * 角色分配权限
     *
     * @param req
     * @return
     */
    void saveRoleMenu(AddRoleMenuReq req);

    /**
     * 获取角色的用户
     *
     * @param req
     * @return
     */
    Page<UserVo> pageRoleUsers(PageRoleKeyReq req);

    /**
     * 获取角色对应权限
     *
     * @param roleId
     * @return
     */
    RoleMenuAllVo listRoleMenus(String roleId);

    /**
     * 获取没有分配该角色的用户列表
     *
     * @param req
     * @return
     */
    Page<User> pageAllRoleUsers(PageRoleKeyReq req);

    /**
     * 获取角色按钮
     *
     * @param roleId
     * @param menuCode
     * @return
     */
    RoleButtonVo listRoleButtons(String roleId, String menuCode);

    /**
     * 获取用户角色
     *
     * @param userCode
     * @return
     */
    List<Role> listUserRole(String userCode);

    /**
     * 获取用户roleKey
     *
     * @param userCode
     * @return
     */
    List<String> listRoleKey(String userCode);
}

package com.zjhh.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.base.SortComparable;
import com.zjhh.comm.exception.BizException;
import com.zjhh.user.dao.entity.*;
import com.zjhh.user.dao.mapper.*;
import com.zjhh.user.enume.MenuTypeEnum;
import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/3/15 15:38
 */
public class BaseServiceImpl {

    protected static final String BASE_ORGANIZE_CODE = "000000";
    @Resource
    private UserMapper userMapper;
    @Resource
    private RoleMapper roleMapper;
    @Resource
    private MenuMapper menuMapper;
    @Resource
    private VOrganizeMapper vOrganizeMapper;
    @Resource
    private UserRoleMapper userRoleMapper;
    @Resource
    private UserSession userSession;

    User checkUser(String userId) {
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) {
            throw new BizException("用户不存在！");
        }
        return user;
    }

    List<Tree<String>> createRolePermission(List<Menu> menus, List<String> checkedKeys, List<String> buttonCheckedKeys) {
        List<Menu> buttons = menus.stream().filter(menu -> menu.getMenuType() == MenuTypeEnum.BUTTON.value()).collect(Collectors.toList());
        menus.removeAll(buttons);
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("key");
        treeNodeConfig.setNameKey("title");
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        menus.forEach(menu -> nodeList.add(new TreeNode<>(menu.getCode(), menu.getParentCode(), menu.getName(), new SortComparable(menu.getSort()))));
        return TreeUtil.build(nodeList, "base", treeNodeConfig, (treeNode, tree) -> {
            tree.setId(treeNode.getId());
            tree.setName(treeNode.getName());
            tree.setParentId(treeNode.getParentId());
            tree.setWeight(treeNode.getWeight());
            tree.putExtra("isCheck", checkedKeys.contains(tree.getId()));
            List<Map<String, Object>> buttonList = new ArrayList<>();
            buttons.forEach(button -> {
                if (button.getParentCode().equals(treeNode.getId())) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("key", button.getCode());
                    map.put("title", button.getName());
                    map.put("parentId", button.getParentCode());
                    map.put("isCheck", buttonCheckedKeys.contains(button.getCode()));
                    buttonList.add(map);
                }
            });
            if (CollUtil.isNotEmpty(buttonList)) {
                tree.putExtra("buttonList", buttonList);
                Map<String, String> scopedSlots = new HashMap<>(1);
                scopedSlots.put("icon", "custom");
                tree.putExtra("scopedSlots", scopedSlots);
                Map<String, String> slots = new HashMap<>(1);
                slots.put("icon", "title");
                tree.putExtra("slots", slots);
            }
        });
    }

    Role checkRole(String roleId) {
        Role role = roleMapper.selectById(roleId);
        if (Objects.isNull(role)) {
            throw new BizException("角色不存在或已删除！");
        }
        return role;
    }

    Menu checkMenu(String menuId) {
        Menu menu = menuMapper.selectById(menuId);
        if (Objects.isNull(menu)) {
            throw new BizException("菜单不存在！");
        }
        return menu;
    }

    /**
     * 获取所有的下级组织结构
     *
     * @param parentCode
     * @return
     */
    List<String> listChildOrganizeCode(String parentCode) {
        QueryWrapper<VOrganize> wrapper = new QueryWrapper<>();
        List<String> parentCodes = new ArrayList<>();
        List<String> codes = new ArrayList<>();
        codes.add(parentCode);
        parentCodes.add(parentCode);
        while (true) {
            wrapper.clear();
            wrapper.lambda().in(VOrganize::getParentCode, parentCodes);
            parentCodes = vOrganizeMapper.selectStrings(wrapper);
            if (CollUtil.isEmpty(parentCodes)) {
                break;
            }
            codes.addAll(parentCodes);
        }
        return codes;
    }

    /**
     * 获取用户分配的角色和创建的角色
     *
     * @param userCode
     * @return
     */
    Set<String> listAllRoleCodes(String userCode) {
        return CollUtil.unionDistinct(listAssignRoleCodes(userCode), listCreateRoleCodes(userCode));
    }

    /**
     * 获取用户分配的角色
     *
     * @param userCode
     * @return
     */
    List<String> listAssignRoleCodes(String userCode) {
        QueryWrapper<UserRole> userRoleWrapper = new QueryWrapper<>();
        userRoleWrapper.lambda().eq(UserRole::getUserCode, userCode)
                .select(UserRole::getRoleCode);
        return userRoleMapper.selectStrings(userRoleWrapper);
    }

    /**
     * 获取用户创建的角色
     *
     * @param userCode
     * @return
     */
    List<String> listCreateRoleCodes(String userCode) {
        QueryWrapper<Role> roleWrapper = new QueryWrapper<>();
        roleWrapper.lambda().eq(Role::getCreateUser, userCode)
                .select(Role::getCode);
        return roleMapper.selectStrings(roleWrapper);
    }

    /**
     * 获取所有的menuCodes,包含父菜单
     *
     * @param codes
     * @return
     */
    List<Menu> listAllMenuCodes(List<String> codes, boolean isButton) {
        Queue<String> menuCodes = new LinkedList<>(codes);
        List<Menu> list;
        if (isButton) {
            list = menuMapper.selectList(null);
        } else {
            QueryWrapper<Menu> wrapper = new QueryWrapper<>();
            wrapper.lambda().ne(Menu::getMenuType, MenuTypeEnum.BUTTON.value());
            list = menuMapper.selectList(wrapper);
        }
        Set<Menu> menuSet = new HashSet<>();
        while (!menuCodes.isEmpty()) {
            String menuCode = menuCodes.poll();
            list.forEach(menu -> {
                if (menu.getCode().equals(menuCode)) {
                    menuSet.add(menu);
                    if (!Objects.equals("base", menu.getParentCode())) {
                        menuCodes.add(menu.getParentCode());
                    }
                }
            });
        }
        return new ArrayList<>(menuSet);
    }
}

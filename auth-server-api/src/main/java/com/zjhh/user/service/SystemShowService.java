package com.zjhh.user.service;

import com.alibaba.fastjson2.JSONObject;
import com.zjhh.user.dao.entity.MobileDefaultSet;
import com.zjhh.user.request.systemshow.*;
import com.zjhh.user.vo.FullPathVo;
import com.zjhh.user.vo.TreeVo;
import com.zjhh.user.vo.systemshow.*;

import java.util.List;

/**
 * 显示配置
 *
 * <AUTHOR>
 * @since 2021/10/25 15:11
 */
public interface SystemShowService {

    /**
     * 获取tab列表
     *
     * @param menuCode
     * @return
     */
    List<SysTabVo> listSysTab(String menuCode);

    /**
     * 获取搜索框列表
     *
     * @param menuCode
     * @return
     */
    List<SysSearchVo> listSysSearch(String menuCode);

    /**
     * 获取按钮列表
     *
     * @param menuCode
     * @return
     */
    List<SysButtonVo> listSysButton(String menuCode);

    /**
     * 修改页签设置
     *
     * @param req
     */
    void updateSysTab(UpdateSysTabReq req);

    /**
     * 修改查询条件
     *
     * @param req
     */
    void updateSysSearch(UpdateSysSearchReq req);

    /**
     * 修改按钮
     *
     * @param req
     */
    void updateSysButton(UpdateSysButtonReq req);

    /**
     * 获取树形结构
     *
     * @return
     */
    TreeVo getMenuTree();

    /**
     * 获取系统显示配置
     *
     * @return
     */
    SystemShowVo getSystemShow(GetSystemShowReq req);

    /**
     * 获取字段跳转链接
     *
     * @param req
     * @return
     */
    FullPathVo getColumnLinkMenu(ColumnLinkMenuReq req);

    /**
     * 获取移动端默认配置
     *
     * @return
     */
    List<MobileDefaultSet> getMobileDefaultSet();

    /**
     * 获取移动端配置的值
     *
     * @param code
     * @return
     */
    JSONObject getMobileDefaultSetValue(String code);

    /**
     * 获取字段信息
     *
     * @param menuCode
     * @param tabId
     * @return
     */
    List<GetSystemColumnVo> getSystemColumn(String menuCode, String tabId);

    /**
     * 获取系统显示配置--根据组件
     *
     * @return
     */
    SystemShowVo getSystemShowByComponent(String component);

    /**
     * 获取系统显示配置-列跳转菜单
     *
     * @return
     */
    TreeVo getJumpMenuTree();
}

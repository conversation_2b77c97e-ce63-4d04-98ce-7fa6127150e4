package com.zjhh.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.exception.BizException;
import com.zjhh.db.comm.Page;
import com.zjhh.user.dao.entity.*;
import com.zjhh.user.dao.mapper.*;
import com.zjhh.user.enume.MenuTypeEnum;
import com.zjhh.user.request.*;
import com.zjhh.user.service.RoleService;
import com.zjhh.user.vo.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2020/6/15 10:12
 */
@DS("master")
@Service
public class RoleServiceImpl extends BaseServiceImpl implements RoleService {

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    private MenuMapper menuMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private RoleMenuMapper roleMenuMapper;

    @Resource
    private UserSession userSession;

    @Override
    public void addRole(AddRoleReq req) {
        if (checkRoleName(req.getName())) {
            throw new BizException("角色名称已存在！");
        }
        Role role = new Role();
        role.setCode(IdUtil.simpleUUID());
        role.setRoleKey(PinyinUtil.getFirstLetter(req.getName(), "").concat(RandomUtil.randomNumbers(3)));
        role.setName(req.getName());
        role.setDescription(req.getDescription());
        role.setCreateUser(userSession.getUserCode());
        roleMapper.insert(role);
    }

    @Override
    public void updateRole(UpdateRoleReq req) {
        Role role = checkRole(req.getRoleId());
        checkPermission(role.getCreateUser());
        if (!Objects.equals(role.getName(), req.getName()) && checkRoleName(req.getName())) {
            throw new BizException("角色名称已存在！");
        }
        role.setDescription(req.getDescription());
        role.setName(req.getName());
        roleMapper.updateById(role);
    }

    @Override
    public Boolean checkRoleName(String roleName) {
        QueryWrapper<Role> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Role::getName, roleName);
        return roleMapper.selectCount(wrapper) > 0;
    }

    @Override
    public Page<RoleVo> pageRole(PageRoleReq req) {
        QueryWrapper<Role> wrapper = new QueryWrapper<>();
        wrapper.lambda().orderByDesc(Role::getGmtCreate);
        if (userSession.nonSuperManager()) {
            wrapper.lambda().in(Role::getCode, listAllRoleCodes(userSession.getUserCode()));
        }
        if (StrUtil.isNotBlank(req.getRoleKey())) {
            wrapper.lambda().eq(Role::getRoleKey, req.getRoleKey());
        }
        if (StrUtil.isNotBlank(req.getName())) {
            wrapper.lambda().and(wp -> wp.like(Role::getName, req.getName()));
        }
        Page<Role> page = new Page<>();
        page.setCurrent(req.getCurrent());
        page.setSize(req.getSize());
        page = roleMapper.selectPage(page, wrapper);
        Page<RoleVo> voPage = new Page<>();
        BeanUtil.copyProperties(page, voPage);
        List<RoleVo> list = new ArrayList<>();
        page.getRecords().forEach(role -> {
            RoleVo vo = BeanUtil.copyProperties(role, RoleVo.class);
            vo.setRoleCode(role.getCode());
            list.add(vo);
        });
        voPage.setRecords(list);
        return voPage;
    }

    @Override
    public List<RoleVo> listRole() {
        QueryWrapper<Role> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(Role::getId, Role::getRoleKey, Role::getName, Role::getCode);
        List<Role> roles = roleMapper.selectList(wrapper);
        List<RoleVo> list = new ArrayList<>();
        roles.forEach(role -> {
            RoleVo vo = BeanUtil.copyProperties(role, RoleVo.class);
            vo.setRoleCode(role.getCode());
            list.add(vo);
        });
        return list;
    }

    @Override
    public void deleteRole(String roleId) {
        Role role = checkRole(roleId);
        checkPermission(role.getCreateUser());
        QueryWrapper<UserRole> userRoleWrapper = new QueryWrapper<>();
        userRoleWrapper.lambda().eq(UserRole::getRoleCode, role.getCode());
        if (userRoleMapper.selectCount(userRoleWrapper) > 0) {
            throw new BizException("角色已绑定相关用户，请先解绑再删除角色！");
        }
        roleMapper.deleteById(role.getId());
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void addRoleUser(String roleId, List<String> userIds) {
        Role role = checkRole(roleId);
        checkPermission(role.getCreateUser());
        List<String> userCodes = roleMapper.listUser(role.getCode(), userIds);
        if (CollUtil.isEmpty(userCodes)) {
            return;
        }
        List<UserRole> list = new ArrayList<>();
        userCodes.forEach(userCode -> {
            UserRole userRole = new UserRole();
            userRole.setUserCode(userCode);
            userRole.setRoleCode(role.getCode());
            list.add(userRole);
        });
        userRoleMapper.insertBatchSomeColumn(list);
    }

    @Override
    public void deleteRoleUser(String roleId, List<String> userIds) {
        Role role = checkRole(roleId);
        checkPermission(role.getCreateUser());
        QueryWrapper<UserRole> wrapper = new QueryWrapper<>();
        List<User> users = userMapper.selectBatchIds(userIds);
        List<String> userCodes = new ArrayList<>();
        users.forEach(user -> userCodes.add(user.getCode()));
        wrapper.lambda().eq(UserRole::getRoleCode, role.getCode()).in(UserRole::getUserCode, userCodes);
        userRoleMapper.delete(wrapper);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void saveRoleMenu(AddRoleMenuReq req) {
        Role role = checkRole(req.getRoleId());
        checkPermission(role.getCreateUser());
        List<RoleMenu> roleMenus = new ArrayList<>();
        QueryWrapper<RoleMenu> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(RoleMenu::getRoleCode, role.getCode());
        roleMenuMapper.delete(wrapper);
        if (CollUtil.isNotEmpty(req.getMenuCodes())) {
            // 过滤掉父节点
            QueryWrapper<Menu> menuWrapper = new QueryWrapper<>();
            menuWrapper.select("distinct parent_code")
                    .lambda().in(Menu::getParentCode, req.getMenuCodes())
                    .ne(Menu::getMenuType, MenuTypeEnum.BUTTON.value());
            List<String> parentCodes = menuMapper.selectStrings(menuWrapper);
            List<String> menuCodes = req.getMenuCodes();
            menuCodes.removeAll(parentCodes);
            menuCodes.forEach(menuCode -> {
                RoleMenu roleMenu = new RoleMenu();
                roleMenu.setRoleCode(role.getCode());
                roleMenu.setMenuCode(menuCode);
                roleMenus.add(roleMenu);
            });
            roleMenuMapper.insertBatchSomeColumn(roleMenus);
        }
    }

    @Override
    public Page<UserVo> pageRoleUsers(PageRoleKeyReq req) {
        checkRole(req.getRoleId());
        return userRoleMapper.pageRoleUser(req.getPage(UserVo.class), req);
    }

    @Override
    public RoleMenuAllVo listRoleMenus(String roleId) {
        Role role = checkRole(roleId);
        List<Menu> menus;
        if (userSession.isSuperManager()) {
            menus = menuMapper.selectList(null);
        } else {
            List<String> assignRoleCodes = listAssignRoleCodes(userSession.getUserCode());
            if (CollUtil.isEmpty(assignRoleCodes)) {
                return new RoleMenuAllVo();
            }
            QueryWrapper<RoleMenu> roleMenuWrapper = new QueryWrapper<>();
            roleMenuWrapper.lambda().in(RoleMenu::getRoleCode, assignRoleCodes)
                    .select(RoleMenu::getMenuCode);
            List<String> menuCodes = roleMenuMapper.selectStrings(roleMenuWrapper);
            menus = listAllMenuCodes(menuCodes, true);
        }
        List<Menu> checkedMenus = roleMenuMapper.listCheckedMenu(role.getCode());
        List<String> checkedKeys = new ArrayList<>();
        List<String> buttonCheckedKeys = new ArrayList<>();
        checkedMenus.forEach(checkedMenu -> {
            if (checkedMenu.getMenuType() == MenuTypeEnum.BUTTON.value()) {
                buttonCheckedKeys.add(checkedMenu.getCode());
            } else {
                checkedKeys.add(checkedMenu.getCode());
            }
        });
        List<String> allTreeKeys = new ArrayList<>();
        menus.forEach(menu -> {
            if (menu.getMenuType() != MenuTypeEnum.BUTTON.value()) {
                allTreeKeys.add(menu.getCode());
            }
        });
        RoleMenuAllVo vo = new RoleMenuAllVo();
        vo.setTreeData(createRolePermission(menus, checkedKeys, buttonCheckedKeys));
        vo.setCheckedKeys(checkedKeys);
        vo.setAllTreeKeys(allTreeKeys);
        return vo;
    }

    @Override
    public Page<User> pageAllRoleUsers(PageRoleKeyReq req) {
        Role role = checkRole(req.getRoleId());
        req.setRoleCode(role.getCode());
        if (userSession.nonSuperManager()) {
            req.setOrganizeCodes(listChildOrganizeCode(userSession.getSessionLoginVo().getOrganize().getCode()));
        }
        return userMapper.selectPageRoleUserVo(req.getPage(User.class), req);
    }

    @Override
    public RoleButtonVo listRoleButtons(String roleId, String menuCode) {
        RoleButtonVo vo = new RoleButtonVo();
        Role role = checkRole(roleId);
        QueryWrapper<Menu> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Menu::getParentCode, menuCode).eq(Menu::getMenuType, MenuTypeEnum.BUTTON.value());
        List<Menu> list = menuMapper.selectList(wrapper);
        if (CollUtil.isEmpty(list)) {
            return vo;
        }
        List<String> menuCodes = new ArrayList<>();
        List<CommButtonVo> buttons = new ArrayList<>();
        list.forEach(m -> {
            menuCodes.add(m.getCode());
            buttons.add(new CommButtonVo(m.getCode(), m.getName()));
        });
        QueryWrapper<RoleMenu> roleMenuWrapper = new QueryWrapper<>();
        roleMenuWrapper.lambda().eq(RoleMenu::getRoleCode, role.getCode()).in(RoleMenu::getMenuCode, menuCodes)
                .select(RoleMenu::getMenuCode);
        List<String> checkedKeys = roleMenuMapper.selectStrings(roleMenuWrapper);
        vo.setButtons(buttons);
        vo.setCheckedKeys(checkedKeys);
        return vo;
    }

    @Override
    public List<Role> listUserRole(String userCode) {
        return roleMapper.listUserRole(userCode);
    }

    @Override
    public List<String> listRoleKey(String userCode) {
        List<Role> roles = listUserRole(userCode);
        List<String> roleKeys = new ArrayList<>();
        roles.forEach(role -> roleKeys.add(role.getRoleKey()));
        return roleKeys;
    }

    /**
     * 判断操作权限
     *
     * @param createUser
     */
    private void checkPermission(String createUser) {
        if (userSession.nonSuperManager() && ObjectUtil.notEqual(createUser, userSession.getUserCode())) {
            throw new BizException("无操作权限，只能操作自己创建的角色！");
        }
    }
}

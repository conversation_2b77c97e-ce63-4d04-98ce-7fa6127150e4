package com.zjhh.user.service;

import com.zjhh.comm.request.LicenceApplyReq;
import com.zjhh.comm.vo.LicenceValidVo;
import com.zjhh.user.vo.LicenceVo;

import java.io.File;

/**
 * <AUTHOR>
 * @since 2021/3/23 16:20
 */
public interface LicenceService {

    /**
     * 重置licence信息
     */
    LicenceValidVo refreshLicence(File file, Boolean flag);

    /**
     * 获取密钥生成单信息
     *
     * @return
     */
    LicenceVo getLicenceApply();


    /**
     * 保存注册文件
     *
     * @param req
     */
    void licenceApplySave(LicenceApplyReq req);
}

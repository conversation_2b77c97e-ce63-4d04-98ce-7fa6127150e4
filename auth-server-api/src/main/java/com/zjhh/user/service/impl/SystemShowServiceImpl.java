package com.zjhh.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.config.SystemProperties;
import com.zjhh.comm.exception.BizException;
import com.zjhh.user.dao.entity.*;
import com.zjhh.user.dao.mapper.*;
import com.zjhh.user.dto.SelectedColumnDto;
import com.zjhh.user.dto.SysColumnDto;
import com.zjhh.user.dto.SysTabDto;
import com.zjhh.user.enume.SysDisplayEnum;
import com.zjhh.user.request.systemshow.*;
import com.zjhh.user.service.MenuService;
import com.zjhh.user.service.SystemShowService;
import com.zjhh.user.vo.FullPathVo;
import com.zjhh.user.vo.SysColumnVo;
import com.zjhh.user.vo.TreeVo;
import com.zjhh.user.vo.systemshow.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/10/25 15:11
 */
@Service
@DS("master")
public class SystemShowServiceImpl implements SystemShowService {

    @Resource
    private MenuService menuService;

    @Resource
    private SetTabMapper setTabMapper;

    @Resource
    private SetSearchMapper setSearchMapper;

    @Resource
    private SetColumnMapper setColumnMapper;

    @Resource
    private SetButtonMapper setButtonMapper;

    @Resource
    private UserSession userSession;

    @Resource
    private SystemProperties systemProperties;

    @Resource
    private MobileDefaultSetMapper mobileDefaultSetMapper;

    @Override
    public List<SysTabVo> listSysTab(String menuCode) {
        Menu menu = getMenu(menuCode);
        List<SysTabVo> tabList = setTabMapper.listSysTab(menu.getCode(), menu.getRouter());
        if (CollUtil.isEmpty(tabList)) {
            return Collections.emptyList();
        }
        List<String> tabUuids = tabList.stream().map(SysTabVo::getTabUuid).collect(Collectors.toList());
        List<SysColumnVo> columnVos = listSysColumn(menuCode, menu.getRouter(), tabUuids);
        Map<String, List<SysColumnVo>> mapColumn = columnVos.stream().collect(Collectors.groupingBy(SysColumnVo::getTabUuid));
        List<SelectedColumnDto> selectedColumns = setColumnMapper.listSelectedLeftColumn(menuCode, menu.getRouter(), tabUuids);
        Map<String, Set<String>> selectedColumnsMap = selectedColumns.stream().collect(Collectors.groupingBy(SelectedColumnDto::getTabUuid, Collectors.mapping(SelectedColumnDto::getColumnUuid, Collectors.toSet())));
        tabList.forEach(tab -> {
            tab.setColumns(mapColumn.get(tab.getTabUuid()));
            tab.setSelectedColumnUuids(selectedColumnsMap.get(tab.getTabUuid()));
        });
        return tabList;
    }

    @Override
    public List<SysSearchVo> listSysSearch(String menuCode) {
        Menu menu = getMenu(menuCode);
        return setSearchMapper.listSysSearch(menu.getCode(), menu.getRouter());
    }

    @Override
    public List<SysButtonVo> listSysButton(String menuCode) {
        Menu menu = getMenu(menuCode);
        return setButtonMapper.listSysButton(menu.getCode(), menu.getRouter());
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public synchronized void updateSysTab(UpdateSysTabReq req) {
        userSession.checkSuperManager();
        QueryWrapper<SetTab> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SetTab::getMenuCode, req.getMenuCode());
        setTabMapper.delete(wrapper);
        List<SetTab> list = new ArrayList<>();
        List<SetColumn> columns = new ArrayList<>();
        int showTabSize = 0;
        for (UpdateSysTabReq.ChildSysTab tab : req.getTabList()) {
            list.add(SetTab.builder()
                    .uuid(IdUtil.fastUUID())
                    .tabUuid(tab.getTabUuid())
                    .tabName(tab.getTabName())
                    .menuCode(req.getMenuCode())
                    .tagExplain(tab.getTagExplain())
                    .showed(tab.getShowed())
                    .sort(ObjectUtil.isNull(tab.getSort()) ? 1 : tab.getSort())
                    .build());
            if (tab.getShowed()) {
                showTabSize++;
            }
            if (tab.getDisplayArea() == SysDisplayEnum.TABLE.value() && CollUtil.isNotEmpty(tab.getColumns())) {
                QueryWrapper<SetColumn> setColumnWrapper = new QueryWrapper<>();
                setColumnWrapper.lambda().eq(SetColumn::getMenuCode, req.getMenuCode())
                        .eq(SetColumn::getTabUuid, tab.getTabUuid());
                setColumnMapper.delete(setColumnWrapper);
                columns.addAll(treeSysColumnToList(tab.getColumns(), req.getMenuCode(), tab.getTabUuid(), "root", tab.getSelectedColumnUuids()));
            }
        }
        if (showTabSize == 0) {
            throw new BizException("至少有一个页签必须显示！");
        }
        setTabMapper.insertBatchSomeColumn(list);
        if (CollUtil.isNotEmpty(columns)) {
            setColumnMapper.insertBatchSomeColumn(columns);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public synchronized void updateSysSearch(UpdateSysSearchReq req) {
        userSession.checkSuperManager();
        QueryWrapper<SetSearch> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SetSearch::getMenuCode, req.getMenuCode());
        setSearchMapper.delete(wrapper);
        List<SetSearch> list = new ArrayList<>();
        req.getSearchList().forEach(search ->
                list.add(SetSearch.builder()
                        .uuid(IdUtil.fastUUID())
                        .searchUuid(search.getSearchUuid())
                        .searchName(search.getSearchName())
                        .defaultValue(search.getDefaultValue())
                        .displaySet(search.getDisplaySet())
                        .menuCode(req.getMenuCode())
                        .sort(ObjectUtil.isNull(search.getSort()) ? 1 : search.getSort())
                        .build()
                )
        );
        setSearchMapper.insertBatchSomeColumn(list);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public synchronized void updateSysButton(UpdateSysButtonReq req) {
        userSession.checkSuperManager();
        QueryWrapper<SetButton> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SetButton::getMenuCode, req.getMenuCode());
        setButtonMapper.delete(wrapper);
        List<SetButton> list = new ArrayList<>();
        req.getButtonList().forEach(button ->
                list.add(SetButton.builder()
                        .uuid(IdUtil.fastUUID())
                        .buttonUuid(button.getButtonUuid())
                        .buttonName(button.getButtonName())
                        .menuCode(req.getMenuCode())
                        .displaySet(button.getDisplaySet())
                        .sort(ObjectUtil.isNull(button.getSort()) ? 1 : button.getSort())
                        .build()
                )
        );
        setButtonMapper.insertBatchSomeColumn(list);
    }

    @Override
    public TreeVo getMenuTree() {
        return menuService.getMenuTree();
    }

    @Override
    public SystemShowVo getSystemShow(GetSystemShowReq req) {
        String menuCode = req.getMenuCode();
        Menu menu = getMenu(menuCode);
        SystemShowVo vo = new SystemShowVo();
        if (systemProperties.isPc()) {
            vo.setChartTabs(setTabMapper.findCharsTab(menuCode, menu.getRouter()));
            vo.setTableTabs(setTabMapper.findTableTab(menuCode, menu.getRouter()));
        } else if (systemProperties.isMobile()) {
            vo.setTabs(setTabMapper.findAllTab(menuCode, menu.getRouter()));
        }
        vo.setSearches(setTabMapper.findSearches(menuCode, menu.getRouter()));
        vo.setButtons(setTabMapper.findButtons(menuCode, menu.getRouter()));
        List<SysColumnDto> sysColumnList = setColumnMapper.listSysShowColumn(menuCode, menu.getRouter());
        Set<String> columnParentUuid = new HashSet<>();
        sysColumnList.forEach(sysColumn -> columnParentUuid.add(sysColumn.getParentUuid()));
        sysColumnList.removeIf(item -> !item.getIsLeaf() && !columnParentUuid.contains(item.getUuid()));
        Map<String, List<SysColumnDto>> showColumns = sysColumnList.stream().collect(Collectors.groupingBy(SysColumnDto::getTabUuid));
        List<SysTabDto> tabDtos = new ArrayList<>();
        if (systemProperties.isPc()) {
            tabDtos = vo.getTableTabs();
        } else if (systemProperties.isMobile()) {
            tabDtos = vo.getTabs().stream().filter(item -> item.getDisplayArea() == 2).collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(tabDtos)) {
            tabDtos.forEach(tab -> {
                List<SysColumnDto> list = showColumns.get(tab.getUuid());
                if (CollUtil.isEmpty(list)) {
                    return;
                }
                List<TreeNode<String>> nodeList = CollUtil.newArrayList();
                list.forEach(t -> {
                    TreeNode<String> node = new TreeNode<>(t.getUuid(), t.getParentUuid(), t.getColumnName(), t.getSort());
                    Map<String, Object> extra = new HashMap<>();
                    extra.put("columnId", t.getColumnId());
                    extra.put("columnName", t.getColumnName());
                    extra.put("isLeaf", t.getIsLeaf());
                    extra.put("linkMenuCode", t.getLinkMenuCode());
                    node.setExtra(extra);
                    nodeList.add(node);
                    List<Tree<String>> treeNodes = TreeUtil.build(nodeList, "root", (treeNode, tree) -> {
                        tree.setId(treeNode.getId());
                        tree.setParentId(treeNode.getParentId());
                        tree.setWeight(treeNode.getWeight());
                        tree.setName(treeNode.getName());
                        if ((Boolean) treeNode.getExtra().get("isLeaf")) {
                            tree.putExtra("field", treeNode.getExtra().get("columnId"));
                        } else {
                            tree.putExtra("field", treeNode.getExtra().get("columnId"));
                        }
                        tree.putExtra("title", treeNode.getExtra().get("columnName"));
                        tree.putExtra("linkMenuCode", treeNode.getExtra().get("linkMenuCode"));
                    });
                    tab.setColumns(treeNodes);
                });
            });
        }
        if (systemProperties.isMobile()) {
            vo.setTabs(sysTabDtoTree(vo.getTabs(), "root"));
        }
        return vo;
    }

    @Override
    public FullPathVo getColumnLinkMenu(ColumnLinkMenuReq req) {
        FullPathVo vo = new FullPathVo();
        String linkMenuCode = setColumnMapper.findLinkMenuCode(req);
        if (StrUtil.isBlank(linkMenuCode)) {
            vo.setIsRedirect(false);
            return vo;
        }
        return menuService.getFullPathByMenuCode(linkMenuCode);
    }

    @Override
    public List<MobileDefaultSet> getMobileDefaultSet() {
        return mobileDefaultSetMapper.selectList(null);
    }

    @Override
    public JSONObject getMobileDefaultSetValue(String code) {
        QueryWrapper<MobileDefaultSet> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(MobileDefaultSet::getCode, code)
                .select(MobileDefaultSet::getValue);
        MobileDefaultSet mobileDefaultSet = mobileDefaultSetMapper.selectOne(wrapper);
        return JSONObject.parseObject(mobileDefaultSet.getValue());
    }

    @Override
    public List<GetSystemColumnVo> getSystemColumn(String menuCode, String tabId) {
        Menu menu = getMenu(menuCode);
        return setColumnMapper.getSystemColumn(menu.getRouter(), menuCode, tabId);
    }

    @Override
    public SystemShowVo getSystemShowByComponent(String component) {
        String menuCode = menuService.getOneMenuByComponent(component);
        if (StrUtil.isNotBlank(menuCode)) {
            GetSystemShowReq req = new GetSystemShowReq();
            req.setMenuCode(menuCode);
            return getSystemShow(req);
        }
        return new SystemShowVo();
    }

    @Override
    public TreeVo getJumpMenuTree() {
        return menuService.getJumpMenuTree();
    }

    private static List<SysTabDto> sysTabDtoTree(List<SysTabDto> list, String parentUuid) {
        List<SysTabDto> res = list.stream().filter(item -> StrUtil.equals(parentUuid, item.getParentUuid())).collect(Collectors.toList());
        if (CollUtil.isEmpty(res)) {
            return res;
        }
        res.forEach(item -> {
            if (!item.getLeaf()) {
                item.setChildren(sysTabDtoTree(list, item.getUuid()));
            }
        });
        return res;
    }

    private List<SysColumnVo> listSysColumn(String menuCode, String component, List<String> tabUuids) {
        List<SysColumnVo> list = setColumnMapper.listSysColumn(menuCode, component, tabUuids);
        return columnTree(list, "root");
    }

    public static List<SysColumnVo> columnTree(List<SysColumnVo> list, String parentKey) {
        List<SysColumnVo> res = list.stream().filter(item -> StrUtil.equals(parentKey, item.getParentUuid())).collect(Collectors.toList());
        if (CollUtil.isEmpty(res)) {
            return res;
        }
        res.forEach(item -> item.setChildren(columnTree(list, item.getColumnUuid())));
        return res;
    }

    private List<SetColumn> treeSysColumnToList(List<UpdateSysTabReq.ChildSysColumn> columns, String menuCode, String tabUuid, String parentUuid, List<String> selectedColumnUuids) {
        if (CollUtil.isEmpty(selectedColumnUuids)) {
            selectedColumnUuids = new ArrayList<>();
        }
        List<SetColumn> list = new ArrayList<>();
        for (UpdateSysTabReq.ChildSysColumn column : columns) {
            list.add(SetColumn.builder()
                    .uuid(IdUtil.fastUUID())
                    .tabUuid(tabUuid)
                    .columnUuid(column.getColumnUuid())
                    .columnName(column.getColumnName())
                    .linkMenuCode(column.getLinkMenuCode())
                    .menuCode(menuCode)
                    .parentUuid(parentUuid)
                    .showed(selectedColumnUuids.contains(column.getColumnUuid()))
                    .sort(ObjectUtil.isNull(column.getSort()) ? 1 : column.getSort())
                    .build()
            );
            if (CollUtil.isNotEmpty(column.getChildren())) {
                list.addAll(treeSysColumnToList(column.getChildren(), menuCode, tabUuid, column.getColumnUuid(), selectedColumnUuids));
            }
        }
        return list;
    }

    private Menu getMenu(String menuCode) {
        return Optional.ofNullable(menuService.getMenuProperties(menuCode, Menu::getId, Menu::getCode, Menu::getRouter)).orElseThrow(() -> new BizException("该菜单不存在！"));
    }
}



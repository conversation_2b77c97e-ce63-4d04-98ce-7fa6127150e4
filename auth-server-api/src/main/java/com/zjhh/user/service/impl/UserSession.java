package com.zjhh.user.service.impl;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.config.SystemProperties;
import com.zjhh.comm.constant.CommConstants;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.exception.NeedLoginException;
import com.zjhh.comm.vo.LicenceValidVo;
import com.zjhh.system.dto.WatermarkDto;
import com.zjhh.system.service.DictService;
import com.zjhh.user.constant.UserConfigConstants;
import com.zjhh.user.dao.entity.*;
import com.zjhh.user.dao.mapper.*;
import com.zjhh.user.enume.UserTypeEnum;
import com.zjhh.user.vo.LoginVo;
import com.zjhh.user.vo.ThirdLoginVo;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 用户session操作
 *
 * <AUTHOR>
 * @since 2021/3/1 10:28
 */
@Service
@DS("master")
public class UserSession {

    @Resource
    private OrganizeUserMapper organizeUserMapper;

    @Resource
    private VOrganizeMapper vOrganizeMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private DictService dictService;

    @Resource
    private UserConfigMapper userConfigMapper;

    @Resource
    private UnitMapper unitMapper;

    @Resource
    private AreaMapper areaMapper;

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    private UserDataAuthMapper userDataAuthMapper;

    @Resource
    private SystemProperties systemProperties;

    @Resource
    private CacheManager cacheManager;

    private Cache<String, LicenceValidVo> licenceCache;

    @PostConstruct
    public void init() {
        QuickConfig licence = QuickConfig.newBuilder("licence:")
                .cacheType(CacheType.LOCAL)
                .syncLocal(true)
                .build();
        licenceCache = cacheManager.getOrCreateCache(licence);
    }

    /**
     * 登录
     *
     * @param user
     * @return
     */
    public LoginVo login(User user) {
        StpUtil.login(user.getId());
        return createLoginVo(user);
    }

    public LoginVo thirdLogin(User user, List<String> userAuthAreaCodes, VOrganize vOrganize) {
        StpUtil.login(user.getId());
        return createLoginVo(user, userAuthAreaCodes, vOrganize, 2);
    }

    /**
     * 创建登录信息
     *
     * @param user
     * @return
     */
    public LoginVo createLoginVo(User user, List<String> userAuthAreaCodes, VOrganize vOrganize, Integer loginType) {
        if (!UserTypeEnum.isSuperUser(user.getUserType()) && !checkLicence()) {
            throw new BizException("证书错误，请联系管理员！");
        }
        LoginVo vo = BeanUtil.copyProperties(user, LoginVo.class);
        vo.setUserId(user.getId());
        String token = StpUtil.getTokenValue();
        vo.setToken(token);
        SaSession session = StpUtil.getTokenSession();
        QueryWrapper<OrganizeUser> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OrganizeUser::getUserCode, user.getCode())
                .select(OrganizeUser::getOrganizeCode);
        OrganizeUser organizeUser = organizeUserMapper.selectOne(wrapper);
        if (ObjectUtil.isNull(organizeUser)) {
            throw new BizException("该用户对应的组织结构错误，请联系管理员！");
        }
        if (ObjectUtil.isNull(vOrganize)) {
            QueryWrapper<VOrganize> vOrganizeWrapper = new QueryWrapper<>();
            vOrganizeWrapper.lambda().eq(VOrganize::getCode, organizeUser.getOrganizeCode());
            vOrganize = vOrganizeMapper.selectOne(vOrganizeWrapper);
            if (ObjectUtil.isNull(vOrganize)) {
                throw new BizException("该用户对应的组织结构错误，请联系管理员！");
            }
        }
        vo.setOrganize(vOrganize);
        //设置选择行政区划编码和默认行政区划编码
        if (vOrganize.getOrganLevel() == 0) {
            // 0 为预算单位
            QueryWrapper<Unit> unitWrapper = new QueryWrapper<>();
            unitWrapper.lambda().eq(Unit::getCode, vOrganize.getCode());
            Unit unit = unitMapper.selectOne(unitWrapper);
            QueryWrapper<Area> areaWrapper = new QueryWrapper<>();
            areaWrapper.lambda().eq(Area::getCode, unit.getAreaCode());
            Area area = areaMapper.selectOne(areaWrapper);
            vo.setDefaultAreaCode(area.getCode());
            vo.setSelectedAreaCode(area.getCode());
            vo.setSelectedAreaName(area.getName());
        } else {
            vo.setDefaultAreaCode(vOrganize.getCode());
            vo.setSelectedAreaCode(vOrganize.getCode());
            vo.setSelectedAreaName(vOrganize.getName());
        }
        if (vo.getNewUser()) {
            // 新用户判断
            User newUser = new User();
            newUser.setId(vo.getUserId());
            newUser.setNewUser(false);
            userMapper.updateById(newUser);
        }
        if (user.isSuperUser()) {
            // 超级管理员使用省厅账号权限的组织结构
            vo.setThirdLoginVo(createThirdLoginVo(token, vo.getAuthAreaCodes(), null));
        } else {
            vo.setThirdLoginVo(createThirdLoginVo(token, vo.getAuthAreaCodes(), vo.getOrganize()));
        }
        if (CollUtil.isNotEmpty(userAuthAreaCodes)) {
            vo.setAuthAreaCodes(userAuthAreaCodes);
        } else {
            if (loginType == 2) {
                vo.setAuthAreaCodes(Collections.singletonList(vOrganize.getCode()));
            } else {
                QueryWrapper<UserDataAuth> authWrapper = new QueryWrapper<>();
                authWrapper.lambda().eq(UserDataAuth::getUserCode, user.getCode()).select(UserDataAuth::getAreaCode);
                vo.setAuthAreaCodes(userDataAuthMapper.selectStrings(authWrapper));
            }
        }
        vo.setLoginType(loginType);
        setRoles(vo);
        // 设置密码校验
        vo.setComplexPassword(dictService.complexPassword());

        // 设置用户配置
        QueryWrapper<UserConfig> userConfigWrapper = new QueryWrapper<>();
        userConfigWrapper.lambda().eq(UserConfig::getUserCode, user.getCode())
                .eq(UserConfig::getCode, UserConfigConstants.CUS_THEME_CONFIG)
                .select(UserConfig::getValue);
        UserConfig userConfig = userConfigMapper.selectOne(userConfigWrapper);
        if (ObjectUtil.isNotNull(userConfig)) {
            vo.setThemeConfig(userConfig.getValue());
        }
        // 添加水印
        WatermarkDto watermarkDto = dictService.getWatermark();
        if (ObjectUtil.isNotNull(watermarkDto)) {
            vo.setWatermarks(watermarkDto.getWatermarkStr(vo.getUsername()));
        }
        // 代表监督录入
        vo.setRepSupervision(dictService.repSupervision());
        session.set(CommConstants.USER_SESSION, vo);
        session.set(CommConstants.USER_PERMISSION, userMapper.listAllPermission(user.isSuperUser() ? null : user.getCode()));
        return vo;
    }

    /**
     * 验证手动获取的token
     *
     * @param token
     */
    public void checkToken(String token) {
        if (StrUtil.isBlank(token)) {
            throw new NeedLoginException();
        }
        Object loginId = StpUtil.getLoginIdByToken(token);
        if (loginId == null) {
            throw new NeedLoginException();
        }
    }

    /**
     * 创建登录信息
     *
     * @param user
     * @return
     */
    public LoginVo createLoginVo(User user) {
        return createLoginVo(user, null, null, 1);
    }

    /**
     * 获取所有用户权限
     *
     * @param user
     * @return
     */
    public List<String> getAllPermission() {
        return (List<String>) StpUtil.getTokenSession().get(CommConstants.USER_PERMISSION);
    }


    public void setSelectArea(String areaCode, String areaName) {
        SaSession session = StpUtil.getTokenSession();
        LoginVo vo = (LoginVo) session.get(CommConstants.USER_SESSION);
        vo.setSelectedAreaCode(areaCode);
        vo.setSelectedAreaName(areaName);
        session.set(CommConstants.USER_SESSION, vo);
    }

    /**
     * 获取登录详情
     *
     * @return
     */
    public LoginVo getSessionLoginVo() {
        return (LoginVo) StpUtil.getTokenSession().get(CommConstants.USER_SESSION);
    }

    /**
     * 获取用户编码
     *
     * @return
     */
    public String getUserCode() {
        return getSessionLoginVo().getCode();
    }

    /**
     * 获取用户组织结构代码
     *
     * @return
     */
    public String getOrganizeCode() {
        return getSessionLoginVo().getOrganize().getCode();
    }

    /**
     * 获取用户选择的行政区划编码
     *
     * @return
     */
    public String getSelectedAreaCode() {
        return getSessionLoginVo().getSelectedAreaCode();
    }

    /**
     * 获取用户选择的行政区划名称
     *
     * @return
     */
    public String getSelectedAreaName() {
        return getSessionLoginVo().getSelectedAreaName();
    }

    /**
     * 获取用户默认的行政区划编码
     *
     * @return
     */
    public String getDefaultAreaCode() {
        return getSessionLoginVo().getDefaultAreaCode();
    }

    /**
     * 当前用户是否超级管理员
     *
     * @return
     */
    public boolean isSuperManager() {
        return UserTypeEnum.isSuperUser(getSessionLoginVo().getUserType());
    }

    /**
     * 当前用户不为超级管理员
     *
     * @return
     */
    public boolean nonSuperManager() {
        return !isSuperManager();
    }

    /**
     * 设置主题
     *
     * @return
     */
    public void setSessionTheme(String themeConfig) {
        getSessionLoginVo().setThemeConfig(themeConfig);
    }

    /**
     * 登出
     */
    public void logout() {
        StpUtil.logout();
    }

    /**
     * 获取认证信息
     *
     * @return
     */
    public LicenceValidVo getLicence() {
        return licenceCache.get("licence");
    }

    public List<String> listUserAuthAreaCodes() {
        return this.getSessionLoginVo().getAuthAreaCodes();
    }

    /**
     * 设置认证信息
     *
     * @param vo
     */
    public void setLicence(LicenceValidVo vo) {
        licenceCache.put("licence", vo);
    }

    public boolean checkLicence() {
        try {
            LicenceValidVo validVo = getLicence();
            if (Objects.isNull(validVo) || !validVo.getValid()) {
                return false;
            }
            if (StrUtil.isBlank(validVo.getLicenceData().getExpireDate())) {
                return false;
            }
            LocalDateTime expireDate = DateUtil.parseLocalDateTime(validVo.getLicenceData().getExpireDate(), DatePattern.NORM_DATE_PATTERN);
            return expireDate.isAfter(LocalDateTime.now());
        } catch (Exception e) {
            return false;
        }

    }

    /**
     * 获取可选择的行政区划子节点
     *
     * @return
     */
    public List<Area> listCanSelectLeafArea() {
        QueryWrapper<UserDataAuth> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(UserDataAuth::getUserCode, getUserCode())
                .select(UserDataAuth::getAreaCode);
        List<String> areaCodes = userDataAuthMapper.selectStrings(wrapper);
        Set<String> setAreaCodes = new HashSet<>(areaCodes);
        setAreaCodes.add(getDefaultAreaCode());
        return areaMapper.listLeafAreas(setAreaCodes);
    }

    public void checkSuperManager() {
        if (nonSuperManager()) {
            throw new BizException("只有管理员才能操作！");
        }
    }

    private void setRoles(LoginVo vo) {
        List<String> roles = CollUtil.newArrayList();
        switch (vo.getUserType()) {
            case 0:
                roles.add("super-manager");
                roles.add("system-manager");
                roles.add("common-user");
                break;
            case 1:
                roles.add("system-manager");
                roles.add("common-user");
                break;
            case 2:
                roles.add("common-user");
                break;
            default:
                break;
        }
        roles.addAll(userRoleMapper.listRoleKeys(vo.getCode()));
        vo.setRoles(roles);
    }

    /**
     * 创建第三方登录省厅参数
     *
     * @param token
     * @return
     */
    private ThirdLoginVo createThirdLoginVo(String token, List<String> userAuthAreaCodes, VOrganize vOrganize) {
        if (ObjectUtil.isNull(systemProperties.getStThirdLogin()) || StrUtil.isBlank(systemProperties.getStThirdLogin().getAppKey())
                || StrUtil.isBlank(systemProperties.getStThirdLogin().getAppSecret())) {
            return null;
        }
        ThirdLoginVo vo = new ThirdLoginVo();
        vo.setAppKey(systemProperties.getStThirdLogin().getAppKey());
        long timestamp = System.currentTimeMillis();
        vo.setTimestamp(timestamp);
        vo.setToken(token);
        String str = systemProperties.getStThirdLogin().getAppKey() + systemProperties.getStThirdLogin().getAppSecret() + token + timestamp;
        vo.setSign(SecureUtil.md5(str));
        vo.setUserAuthAreaCodes(userAuthAreaCodes);
        vo.setOrganize(vOrganize);
        return vo;
    }
}

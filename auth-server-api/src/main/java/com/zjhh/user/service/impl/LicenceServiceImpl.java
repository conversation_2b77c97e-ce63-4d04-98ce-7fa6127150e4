package com.zjhh.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.system.oshi.OshiUtil;
import com.alibaba.fastjson2.JSONObject;
import com.zjhh.comm.constant.CommConstants;
import com.zjhh.comm.dto.LicenceServerInfo;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.request.LicenceApplyReq;
import com.zjhh.comm.vo.LicenceValidVo;
import com.zjhh.comm.vo.LicenceXmlVo;
import com.zjhh.system.service.DictService;
import com.zjhh.user.service.LicenceService;
import com.zjhh.user.utils.LicenceUtil;
import com.zjhh.user.vo.LicenceVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import oshi.hardware.NetworkIF;

import java.io.*;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/3/24 15:44
 */
@Slf4j
@Service
public class LicenceServiceImpl implements LicenceService {

    @Resource
    private DictService dictService;

    @Resource
    private UserSession userSession;

    @Override
    public LicenceValidVo refreshLicence(File file, Boolean flag) {
        LicenceValidVo validVo;
        try (InputStream inputStream = Files.newInputStream(file.toPath())) {
            String xml = IoUtil.readUtf8(inputStream);
            Map<String, Object> map = XmlUtil.xmlToMap(xml);
            LicenceXmlVo xmlVo = new LicenceXmlVo();
            BeanUtil.fillBeanWithMap(map, xmlVo, true);
            validVo = LicenceUtil.checkLicence(xmlVo);
            userSession.setLicence(validVo);
            return validVo;
        } catch (Exception e) {
            log.error("证书加载失败", e);
            validVo = new LicenceValidVo();
            validVo.setValid(false);
            if (flag) {
                userSession.setLicence(validVo);
            }
        }
        return validVo;
    }

    @Override
    public LicenceVo getLicenceApply() {
        LicenceVo vo = new LicenceVo();
        vo.setService(dictService.getLicenceService());
        vo.setProduct(dictService.getLicenceProduct());
        vo.setUser(dictService.getLicenceUser());
        List<NetworkIF> list = OshiUtil.getNetworkIFs();
        List<LicenceServerInfo> serverInfos = new ArrayList<>();
        list.forEach(networkIF -> {
            if (networkIF.getIPv4addr().length > 0 && !networkIF.getName().contains("docker")) {
                LicenceServerInfo serverInfo = new LicenceServerInfo();
                serverInfo.setIp(networkIF.getIPv4addr()[0]);
                serverInfo.setMacAddress(networkIF.getMacaddr());
                serverInfos.add(serverInfo);
            }
        });
        vo.setServers(serverInfos);
        return vo;
    }

    @Override
    public void licenceApplySave(LicenceApplyReq req) {
        dictService.addLicenceService(req.getLicenceService());
        dictService.addLicenceUser(req.getLicenceUser());
        dictService.addLicenceProduct(req.getLicenceProduct());
        String str = JSONObject.toJSONString(req);
        str = LicenceUtil.encryptLicenceId(str);
        File file = FileUtil.touch(CommConstants.LICENCE_PATH + CommConstants.LICENCE_APPLY);
        try {
            OutputStream outputStream = new FileOutputStream(file);
            IoUtil.write(outputStream, true, str.getBytes());
        } catch (IOException e) {
            throw new BizException("生成申请文件失败！");
        }

    }
}

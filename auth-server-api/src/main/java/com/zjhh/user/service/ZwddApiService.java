package com.zjhh.user.service;

import com.zjhh.user.dao.entity.User;
import com.zjhh.user.vo.*;
import org.springframework.scheduling.annotation.Async;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * 政务钉钉接口调用
 * 第三方接口调用
 */

public interface ZwddApiService {

    /**
     * 获取租户根组织
     *
     * @return 响应参数参考GetRootOrganization.java
     */
    GetOrganizationByCode getRootOrganization();

    /**
     * 分页获取下⼀级组织 Code 列表
     *
     * @param organizationCode 父组织code
     * @param pageNo           页码
     * @return
     */

    Content<List<String>> pageSubOrganizationCodes(String organizationCode, Integer pageNo);

    /**
     * 根据组织code查询详情
     *
     * @param organizationCode 组织code
     * @return 组织详情
     */

    Future<GetOrganizationByCode> getOrganizationByCode(String organizationCode);

    /**
     * 根据组织code列表 查询组织详情
     *
     * @param organizationCodes 组织code列表
     * @return 组织集合
     */
    @Async
    Future<List<GetOrganizationByCode>> listOrganizationByCodes(List<String> organizationCodes);

    /**
     * 根据员工code列表 查询员工详情
     *
     * @param employeeCodes 员工code列表
     * @param dingIds
     * @return 员工集合
     */
    List<GetEmployeeByCode> listEmployeeByCodes(List<String> employeeCodes, List<String> dingIds);

    /**
     * 分页查询组织下的员⼯ Code
     *
     * @param organizationCode 父组织code
     * @param pageNo           页码
     * @return
     */
    Content<List<String>> pageOrganizationEmployeeCodes(String organizationCode, Integer pageNo);

    /**
     * 根据员工 Code 查询详情
     *
     * @param employeeCode 员工code
     * @return
     */
    GetEmployeeByCode getEmployeeByCode(String employeeCode);

    /**
     * 根据员工 Code 查询详情
     *
     * @param employeeCode 员工code
     * @return 员工详情
     */
    GetEmployeeByCode getEmployeeByCode2DingDing(String employeeCode);

    /**
     * 根据员工code 获取员工任职
     *
     * @param employeeCode 员工code
     * @return 员工任职列表
     */
    List<OrgEmployeePositionsByCode> listEmployeePositionsByEmployeeCode(String employeeCode);

    /**
     * 通过员工 Code 列表获取员⼯账号 ID
     *
     * @param employeeCode 员工code
     * @return ListEmployeeAccountIds 员工账号id
     */
    List<ListEmployeeAccountIds> listEmployeeAccountIds(String employeeCode);

    /**
     * 根据员工code查询 任职列表
     *
     * @param employeeCode 员工code
     * @return 员工任职列表
     */
    List<OrgEmployeePositionsByCode> getEmployeePositionsByCode(String employeeCode);

    /**
     * 根据组织 CODE、员⼯ Code 列表， 批量获取员工在该组织的任职
     *
     * @param employeeCodes    员工code列表
     * @param organizationCode 组织code
     * @return
     */
    List<ListOrgEmployeePositionsByCodes> listOrgEmployeePositionsByCodes(String organizationCode,
                                                                          Collection<String> employeeCodes);

    /**
     * 查询定义的事件列表
     */
    List<CallbackDefine> queryCallbackDefine();

    /**
     * 注册消息回调
     *
     * @param eventTag
     * @param callbackUrl
     */
    boolean registerEvenCallback(ZwddCallBackEventEnum eventTag, String callbackUrl);

    /**
     * 删除事件回调的定义信息
     *
     * @param eventCallbackId 订阅回调的id，从this.queryCallbackDefine()返回的参数中获取id
     */
    void deleteEventCallbackDefine(Long eventCallbackId);

    /**
     * 根据钉钉id 列表查询映射
     *
     * @param dingUserIdList 钉钉id 列表
     * @return 员工列表
     */

    List<GetEmployeeByCode> listEmployeeByDingdingId(List<String> dingUserIdList);

    /**
     * 根据钉钉id 查询员工映射
     *
     * @param dingUserId 钉钉id
     * @return 员工code
     */
    String getEmpCodeByDingUserId(String dingUserId);

    /**
     * 根据钉钉id 查询员工映射
     *
     * @param mobile 钉钉id
     * @return 员工code
     */
    String getEmployeeCodeByMobile(String mobile);

    /**
     * 根据手机号列表，查询人员列表
     *
     * @param mobiles 手机号列表
     * @return 人员列表
     */
    List<GetEmployeeByCode> listEmployeeByMobile(List<String> mobiles);

    SyncZwddInfoVO listEmployeeByUserIds(List<String> userIds);

    /**
     * 接口地址：https://openplatform-portal.dg-work.cn/#/doc-jsapi?apiType=serverapi&docKey=2641<br/>
     * 获取通讯录权限范围
     *
     * @return 用户部门 可见范围 用户list返回为空，部门list为当前应用根组织code
     */
    String getAuthorizedRootOrgCode();

    /**
     * 获取access_token
     *
     * @return
     */

    String getAccessToken(boolean isLogin);

    /**
     * 接口地址：https://openplatform-portal.dg-work.cn/portal/#/helpdoc?docKey=kfzn&slug=engk1k<br/>
     * 根据code获取用户信息
     *
     * @return 用户信息
     */
    ZwddLoginUserInfo getUserInfoByCode(String code);

    /**
     * 接口地址：https://openplatform-portal.dg-work.cn/portal/#/helpdoc?docKey=kfzn&slug=bfno8h<br/>
     * 根据code获取用户信息
     *
     * @return 用户信息
     */
    ZwddLoginUserInfo getUserInfoByMobileCode(String authCode);

    boolean fullSyncUserByMobileList(List<String> mobileList);

    boolean fullSyncDeptAndUser() throws Exception;

    int deleteZwddRelation(String userCode);

    int updateZwddRelation(User user);

    /**
     * 发送消息
     *
     * @param content
     * @param accountId
     * @return
     */
    String sendNotification(ZwddMsgDTO ddMsgDTO);

    boolean checkMessageStatus(String messageId, String accountId);

    String getAccessToken();

    String sendUndoMessage(Map<String, String> params);

    Integer sendDoMessage(Map<String, String> params);

    Integer cancelUndoEvent(Map<String, String> params);

    Boolean notification(NotificationMsgVo msgVo);

    void notification();


}

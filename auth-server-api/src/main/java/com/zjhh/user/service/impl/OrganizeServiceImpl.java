package com.zjhh.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.utils.TreeUtils;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.comm.vo.TreeSelectedVo;
import com.zjhh.db.comm.Page;
import com.zjhh.system.request.PageSearchReq;
import com.zjhh.user.dao.entity.*;
import com.zjhh.user.dao.mapper.*;
import com.zjhh.user.request.AddAreaReq;
import com.zjhh.user.request.AddUnitReq;
import com.zjhh.user.request.UpdateAreaReq;
import com.zjhh.user.request.UpdateUnitReq;
import com.zjhh.user.service.OrganizeService;
import com.zjhh.user.vo.LoginVo;
import com.zjhh.user.vo.SelectAreaVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/3/9 9:27
 */
@DS("master")
@Service
public class OrganizeServiceImpl extends BaseServiceImpl implements OrganizeService {

    @Resource
    private AreaMapper areaMapper;

    @Resource
    private UnitMapper unitMapper;

    @Resource
    private UserSession userSession;

    @Resource
    private VOrganizeMapper vOrganizeMapper;

    @Resource
    private UserDataAuthMapper userDataAuthMapper;

    @Resource
    private OrganizeUserMapper organizeUserMapper;

    @Override
    public Page<Area> pageAreas(PageSearchReq req) {
        Page<Area> page = req.getPage(Area.class);
        if (userSession.nonSuperManager()) {
            return page;
        }
        QueryWrapper<Area> wrapper = new QueryWrapper<>();
        wrapper.lambda().ne(Area::getCode, BASE_ORGANIZE_CODE)
                .orderByAsc(Area::getCode);
        if (StrUtil.isNotBlank(req.getSearchKey())) {
            wrapper.lambda().and(wp -> wp.
                    like(StrUtil.isNotBlank(req.getSearchKey()), Area::getCode, req.getSearchKey())
                    .or()
                    .like(StrUtil.isNotBlank(req.getSearchKey()), Area::getName, req.getSearchKey()));
        }
        return areaMapper.selectPage(page, wrapper);
    }

    @Override
    public Page<Unit> pageUnits(PageSearchReq req) {
        QueryWrapper<Unit> wrapper = new QueryWrapper<>();
        wrapper.lambda().orderByAsc(Unit::getCode);
        if (userSession.nonSuperManager()) {
            wrapper.lambda().in(Unit::getCode, listChildOrganizeCode(userSession.getSessionLoginVo().getOrganize().getCode()));
        }
        if (StrUtil.isNotBlank(req.getSearchKey())) {
            wrapper.lambda().and(wp -> wp.like(StrUtil.isNotBlank(req.getSearchKey()), Unit::getCode, req.getSearchKey())
                    .or()
                    .like(StrUtil.isNotBlank(req.getSearchKey()), Unit::getName, req.getSearchKey()));
        }
        Page<Unit> page = req.getPage(Unit.class);
        return unitMapper.selectPage(page, wrapper);
    }

    @Override
    public void addArea(AddAreaReq req) {
        if (!req.getCode().matches("^\\d+")) {
            throw new BizException("不符合行政区划编码规则！");
        }
        if (checkOrganize(req.getCode())) {
            throw new BizException("该机构编码已存在！");
        }
        QueryWrapper<Unit> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Unit::getAreaCode, req.getParentCode());
        if (unitMapper.selectCount(wrapper) > 0) {
            throw new BizException("上级机构存在下级单位，不允许在建下级机构！");
        }
        Area area = new Area();
        area.setCode(req.getCode());
        area.setName(req.getName());
        VOrganize vOrganize = Optional.ofNullable(vOrganizeMapper.selectByCode(req.getParentCode())).<BizException>orElseThrow(() -> {
            throw new BizException("上级机构不存在！");
        });
        area.setOrganLevel(req.getOrganLevel());
        area.setParentCode(vOrganize.getCode());
        area.setParentName(vOrganize.getName());
        area.setCreateUser(userSession.getUserCode());
        areaMapper.insert(area);
    }

    @Override
    public void addUnit(AddUnitReq req) {
        if (!req.getCode().matches("^\\d+_(\\d{3})+$")) {
            throw new BizException("不符合单位编码规则！");
        }
        if (checkOrganize(req.getCode())) {
            throw new BizException("该单位编码已存在！");
        }
        Unit unit = new Unit();
        unit.setCode(req.getCode());
        unit.setName(req.getName());
        VOrganize vOrganize = Optional.ofNullable(vOrganizeMapper.selectByCode(req.getParentCode())).<BizException>orElseThrow(() -> {
            throw new BizException("上级机构或单位不存在！");
        });
        unit.setParentCode(vOrganize.getCode());
        unit.setParentName(vOrganize.getName());
        if (vOrganize.getOrganLevel() > 0) {
            // 上级为行政区划
            QueryWrapper<Area> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(Area::getParentCode, vOrganize.getCode());
            if (areaMapper.selectCount(wrapper) > 0) {
                throw new BizException("末级行政区划下才能添加单位！");
            }
            unit.setAreaCode(vOrganize.getCode());
            unit.setAreaName(vOrganize.getName());
        } else {
            Unit parentUnit = Optional.ofNullable(unitMapper.selectByCode(req.getParentCode())).<BizException>orElseThrow(() -> {
                throw new BizException("上级预算单位不存在！");
            });
            unit.setAreaCode(parentUnit.getAreaCode());
            unit.setAreaName(parentUnit.getAreaName());
        }
        unit.setCreateUser(userSession.getUserCode());
        unitMapper.insert(unit);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateArea(UpdateAreaReq req) {
        Area checkArea = Optional.ofNullable(areaMapper.selectById(req.getId())).<BizException>orElseThrow(() -> {
            throw new BizException("该行政区划不存在！");
        });
        Area area = new Area();
        area.setId(req.getId());
        area.setName(req.getName());
        area.setOrganLevel(req.getOrganLevel());
        areaMapper.updateById(area);
        areaMapper.updateParentName(req.getName(), checkArea.getCode());
        unitMapper.updateParentName(req.getName(), checkArea.getCode());
        unitMapper.updateAreaName(req.getName(), checkArea.getCode());
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateUnit(UpdateUnitReq req) {
        Unit checkUnit = Optional.ofNullable(unitMapper.selectById(req.getId())).<BizException>orElseThrow(() -> {
            throw new BizException("该行政区划不存在！");
        });
        Unit unit = new Unit();
        unit.setId(req.getId());
        unit.setName(req.getName());
        unitMapper.updateById(unit);
        unitMapper.updateParentName(req.getName(), checkUnit.getCode());
    }

    @Override
    public void deleteBatchArea(List<String> ids) {
        QueryWrapper<Area> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(Area::getId, ids).select(Area::getCode);
        List<String> areaCodes = areaMapper.selectStrings(wrapper);
        if (CollUtil.isEmpty(areaCodes)) {
            throw new BizException("行政区划ID为空或不存在！");
        }
        QueryWrapper<VOrganize> organizeWrapper = new QueryWrapper<>();
        organizeWrapper.lambda().in(VOrganize::getParentCode, areaCodes);
        if (vOrganizeMapper.selectCount(organizeWrapper) > 0) {
            throw new BizException("存在下级行政区划或者单位，不允许删除！");
        }
        checkExistUser(areaCodes);
        areaMapper.deleteBatchIds(ids);
    }

    @Override
    public void deleteBatchUnit(List<String> ids) {
        QueryWrapper<Unit> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(Unit::getId, ids).select(Unit::getCode);
        List<String> unitCodes = unitMapper.selectStrings(wrapper);
        if (CollUtil.isEmpty(unitCodes)) {
            throw new BizException("单位ID为空或不存在！");
        }
        wrapper.clear();
        wrapper.lambda().in(Unit::getParentCode, unitCodes).select(Unit::getCode);
        List<String> existCodes = unitMapper.selectStrings(wrapper);
        if (!CollUtil.containsAll(unitCodes, existCodes)) {
            throw new BizException("存在下级单位，不允许删除！");
        }
        checkExistUser(unitCodes);
        unitMapper.deleteBatchIds(ids);
    }

    @Override
    public List<Tree<String>> treeArea() {
        QueryWrapper<Unit> unitWrapper = new QueryWrapper<>();
        unitWrapper.lambda().select(Unit::getAreaCode).groupBy(Unit::getAreaCode);
        List<String> existAreaCodes = unitMapper.selectStrings(unitWrapper);
        QueryWrapper<Area> wrapper = new QueryWrapper<>();
        wrapper.lambda().orderByAsc(Area::getCode);
        List<Area> areas = areaMapper.selectList(wrapper);
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        areas.forEach(area -> nodeList.add(new TreeNode<>(area.getCode(), area.getParentCode(), area.getName(), 1)));
        return TreeUtil.build(nodeList, "0", createTreeNodeConfig(), (treeNode, tree) -> {
            tree.setId(treeNode.getId());
            tree.setName(treeNode.getName());
            tree.setParentId(treeNode.getParentId());
            tree.putExtra("value", tree.getId());
            if (existAreaCodes.contains(treeNode.getId())) {
                tree.putExtra("disabled", true);
            } else {
                tree.putExtra("disabled", false);
            }
        });
    }

    @Override
    public List<Tree<String>> treeSelectOrganize() {
        QueryWrapper<Area> areaWrapper = new QueryWrapper<>();
        areaWrapper.lambda().select(Area::getParentCode).groupBy(Area::getParentCode);
        List<String> existCodes = areaMapper.selectStrings(areaWrapper);
        String parentId;
        if (userSession.isSuperManager()) {
            parentId = BASE_ORGANIZE_CODE;
        } else {
            LoginVo loginVo = userSession.getSessionLoginVo();
            parentId = loginVo.getOrganize().getParentCode();
        }
        List<VOrganize> organizes = vOrganizeMapper.selectList(null);
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        organizes.forEach(organize -> {
            TreeNode<String> treeNode = new TreeNode<>(organize.getCode(), organize.getParentCode(), organize.getName(), 1);
            Map<String, Object> map = new HashMap<>();
            if (organize.getOrganLevel() > 0) {
                if (existCodes.contains(organize.getCode())) {
                    map.put("disabled", true);
                } else {
                    map.put("disabled", false);
                }
                map.put("type", "area");
            } else {
                map.put("disabled", false);
                map.put("type", "unit");
            }
            treeNode.setExtra(map);
            nodeList.add(treeNode);
        });
        return TreeUtil.build(nodeList, parentId, createTreeNodeConfig(), (treeNode, tree) -> {
            tree.setId(treeNode.getId());
            tree.setName(treeNode.getName());
            tree.setParentId(treeNode.getParentId());
            tree.putExtra("value", tree.getId());
            tree.putExtra("disabled", treeNode.getExtra().get("disabled"));
            tree.putExtra("type", treeNode.getExtra().get("type"));
        });
    }

    private TreeNodeConfig createTreeNodeConfig() {
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("key");
        treeNodeConfig.setNameKey("title");
        treeNodeConfig.setParentIdKey("parentKey");
        return treeNodeConfig;
    }

    @Override
    public List<Tree<String>> treeOrganize() {
        QueryWrapper<VOrganize> wrapper = new QueryWrapper<>();
        String parentId = "0";
        if (userSession.nonSuperManager()) {
            LoginVo loginVo = userSession.getSessionLoginVo();
            wrapper.lambda().in(VOrganize::getCode, listChildOrganizeCode(loginVo.getOrganize().getCode()));
            parentId = loginVo.getOrganize().getParentCode();
        }
        wrapper.lambda().orderByAsc(VOrganize::getCode);
        List<VOrganize> organizes = vOrganizeMapper.selectList(wrapper);
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        organizes.forEach(organize -> {
            TreeNode<String> treeNode = new TreeNode<>(organize.getCode(), organize.getParentCode(), organize.getName(), organize.getCode());
            Map<String, Object> slot = new HashMap<>();
            if (organize.getOrganLevel() == 0) {
                slot.put("icon", "unit");
            } else {
                if (ObjectUtil.equal(BASE_ORGANIZE_CODE, organize.getCode())) {
                    slot.put("icon", "organization");
                } else {
                    slot.put("icon", "area");
                }
            }
            treeNode.setExtra(slot);
            nodeList.add(treeNode);
        });
        JSONObject scopedSlots = new JSONObject();
        scopedSlots.put("title", "title");
        return TreeUtil.build(nodeList, parentId, createTreeNodeConfig(), (treeNode, tree) -> {
            tree.setId(treeNode.getId());
            tree.setName(treeNode.getName());
            tree.setParentId(treeNode.getParentId());
            tree.putExtra("scopedSlots", scopedSlots);
            tree.putExtra("slots", treeNode.getExtra());
        });
    }

    @Override
    public SelectAreaVo getSelectArea() {
        List<Area> leafAreas = userSession.listCanSelectLeafArea();
        if (CollUtil.isEmpty(leafAreas)) {
            return SelectAreaVo.builder()
                    .defaultValue(userSession.getSelectedAreaCode())
                    .build();
        }
        Set<String> selectCodes = leafAreas.stream().map(Area::getCode).collect(Collectors.toSet());
        QueryWrapper<Area> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(Area::getCode, Area::getName, Area::getParentCode);
        List<Area> allAreas = areaMapper.selectList(wrapper);
        Set<TreeSelectVo> result = new HashSet<>();
        recursionAreas(allAreas, result, selectCodes);
        wrapper.clear();
        String selectedCode = userSession.getSelectedAreaCode();
        wrapper.lambda().eq(Area::getParentCode, selectedCode);
        if (areaMapper.selectCount(wrapper) > 0) {
            selectedCode = null;
        }
        return SelectAreaVo.builder()
                .defaultValue(selectedCode)
                .treeSelectList(TreeUtils.listToTree(CollUtil.newArrayList(result), "000000"))
                .build();
    }

    @Override
    public void changeArea(String areaCode) {
        QueryWrapper<Area> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Area::getParentCode, areaCode);
        if (areaMapper.selectCount(wrapper) > 0) {
            throw new BizException("只能切换末级的行政区划！");
        }
        QueryWrapper<UserDataAuth> authWrapper = new QueryWrapper<>();
        authWrapper.lambda().eq(UserDataAuth::getUserCode, userSession.getUserCode())
                .eq(UserDataAuth::getAreaCode, areaCode);
        if (userDataAuthMapper.selectCount(authWrapper) <= 0) {
            throw new BizException("您没有权限切换到该行政区划！");
        }
        wrapper.clear();
        wrapper.lambda().eq(Area::getCode, areaCode)
                .select(Area::getName);
        Area area = areaMapper.selectOne(wrapper);
        userSession.setSelectArea(areaCode, area.getName());
    }

    @Override
    public TreeSelectedVo treeOrganizeSelected() {
        List<TreeSelectVo> list = vOrganizeMapper.treeOrganize();
        TreeSelectedVo vo = new TreeSelectedVo();
        List<TreeSelectVo> treeList = TreeUtils.listToTree(list, "000000");
        vo.setTreeSelects(treeList);
        String organizeCode = userSession.getOrganizeCode();
        if (StrUtil.equals("000000", organizeCode)) {
            List<String> selectedKeys = new ArrayList<>();
            List<String> selectedTitles = new ArrayList<>();
            list.forEach(treeSelectVo -> {
                selectedKeys.add(treeSelectVo.getKey());
                selectedTitles.add(treeSelectVo.getTitle());
            });
            vo.setSelectedKeys(selectedKeys);
            vo.setSelectedTitles(selectedTitles);
        } else {
            TreeSelectVo treeSelectVo = treeSearch(treeList, organizeCode);
            if (treeSelectVo == null || CollUtil.isEmpty(treeSelectVo.getChildren())) {
                vo.setSelectedKeys(CollUtil.newArrayList(organizeCode));
                vo.setSelectedTitles(CollUtil.newArrayList(userSession.getSessionLoginVo().getOrganize().getName()));
            } else {
                List<String> selectedKeys = new ArrayList<>();
                List<String> selectedTitles = new ArrayList<>();
                treeKeys(treeSelectVo, selectedKeys, selectedTitles);
                vo.setSelectedKeys(selectedKeys);
                vo.setSelectedTitles(selectedTitles);
            }
        }
        return vo;
    }

    @Override
    public Area getArea(String areaCode, SFunction<Area, ?>... properties) {
        QueryWrapper<Area> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Area::getCode, areaCode)
                .select(properties);
        return areaMapper.selectOne(wrapper);
    }

    @Override
    public TreeSelectVo listUnitSelect() {
        List<TreeSelectVo> list = unitMapper.listUnitSelect();
        TreeSelectVo vo = new TreeSelectVo();
        vo.setKey("root");
        vo.setValue("root");
        vo.setTitle("全选");
        vo.setChildren(TreeUtils.listToTree(list, "root"));
        return vo;
    }

    private void checkExistUser(List<String> organizeCodes) {
        QueryWrapper<OrganizeUser> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(OrganizeUser::getOrganizeCode, organizeCodes);
        if (organizeUserMapper.exists(wrapper)) {
            throw new BizException("该组织结构下存在用户，无法删除！");
        }
    }

    private TreeSelectVo treeSearch(List<TreeSelectVo> treeList, String key) {
        if (CollUtil.isEmpty(treeList)) {
            return null;
        }
        for (TreeSelectVo tree : treeList) {
            if (StrUtil.equals(key, tree.getKey())) {
                return tree;
            }
            return treeSearch(tree.getChildren(), key);
        }
        return null;
    }

    private void treeKeys(TreeSelectVo vo, List<String> selectedKeys, List<String> selectedTitles) {
        selectedKeys.add(vo.getKey());
        selectedTitles.add(vo.getTitle());
        if (CollUtil.isNotEmpty(vo.getChildren())) {
            vo.getChildren().forEach(child -> treeKeys(child, selectedKeys, selectedTitles));
        }
    }

    private void recursionAreas(List<Area> allAreas, Set<TreeSelectVo> result, Set<String> screenParentCodes) {
        Set<String> parentCodes = new HashSet<>();
        allAreas.forEach(area -> {
            if (screenParentCodes.contains(area.getCode())) {
                parentCodes.add(area.getParentCode());
                result.add(TreeSelectVo.builder()
                        .key(area.getCode())
                        .title(area.getName())
                        .parentKey(area.getParentCode())
                        .value(area.getCode())
                        .build());
            }
        });
        if (CollUtil.isNotEmpty(parentCodes)) {
            recursionAreas(allAreas, result, parentCodes);
        }
    }

    private Boolean checkOrganize(String code) {
        QueryWrapper<VOrganize> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VOrganize::getCode, code);
        return vOrganizeMapper.selectCount(wrapper) > 0;
    }
}

package com.zjhh.user.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.user.dao.entity.Component;
import com.zjhh.user.dao.entity.Menu;
import com.zjhh.user.dto.MenuDto;
import com.zjhh.user.request.AddMenuReq;
import com.zjhh.user.request.UpdateMenuReq;
import com.zjhh.user.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/2 16:45
 */
public interface MenuService {

    /**
     * 获取用户路由
     *
     * @return
     */
    Tree<String> getRouter();

    /**
     * 添加菜单
     *
     * @param req
     * @return
     */
    void addMenu(AddMenuReq req);

    /**
     * 获取所有的权限
     *
     * @return
     */
    List<Tree<String>> allMenus();

    /**
     * 获取所有的上级菜单
     *
     * @return
     */
    List<Tree<String>> listParentMenus();

    /**
     * 修改权限l
     *
     * @param req
     * @return
     */
    void updateMenu(UpdateMenuReq req);

    /**
     * 删除权限
     *
     * @param menuId
     * @return
     */
    void deleteMenu(String menuId);

    /**
     * 批量删除权限
     *
     * @param menuIds
     * @return
     */
    void deleteBatch(List<String> menuIds);

    /**
     * 获取菜单详情
     *
     * @param id
     * @return
     */
    MenuVo getMenu(String id);

    /**
     * 获取前端组件列表
     *
     * @return
     */
    List<ComponentVo> listComponents();

    /**
     * 菜单选择前端组件
     *
     * @return
     */
    List<ComponentVo> listMenuSelect();

    /**
     * 按钮选择前端组件
     *
     * @param menuId
     * @return
     */
    List<Component> listButtonSelect(String menuId);

    /**
     * 根据组件ID获取路径
     *
     * @param router
     * @return
     */
    FullPathVo getFullPath(String router);

    /**
     * 根据组件ID获取menuCode
     *
     * @param router
     * @return
     */
    MenuInfoVo getMenuInfo(String router);

    /**
     * 根据menuCode获取链接
     *
     * @param menuCode
     * @return
     */
    FullPathVo getFullPathByMenuCode(String menuCode);

    /**
     * 获取驾驶舱菜单
     *
     * @return
     */
    List<CockpitMenuVo> listCockpitMenu();

    /**
     * 获取菜单--选择字段
     *
     * @param menuCode
     * @param properties
     * @return
     */
    Menu getMenuProperties(String menuCode, SFunction<Menu, ?>... properties);

    /**
     * 获取菜单树形结构
     *
     * @return
     */
    TreeVo getMenuTree();

    /**
     * 获取菜单和下级菜单的列表
     *
     * @param menuCode
     * @return
     */
    List<MenuDto> listMenu(String menuCode);

    /**
     * 获取在首页显示的菜单
     *
     * @return
     */
    List<ShowHomeMenuVo> listShowHomeMenu();

    /**
     * 获取数据说明的树形结构
     *
     * @return
     */
    TreeVo getDescMenuTree();

    /**
     * 获取该组件的第一个菜单
     *
     * @param component
     * @return
     */
    String getOneMenuByComponent(String component);

    /**
     * 获取映射的菜单
     *
     * @return
     */
    List<TreeSelectVo> listSelectMapMenu();

    Menu getMenu(String code, SFunction<Menu, ?>... columns);

    /**
     * 获取系统显示配置列跳转菜单
     *
     * @return
     */
    TreeVo getJumpMenuTree();
}

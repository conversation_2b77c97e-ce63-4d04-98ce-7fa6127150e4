package com.zjhh.user.service;

import com.zjhh.db.comm.Page;
import com.zjhh.user.request.AddPointReq;
import com.zjhh.user.request.PageBuryingPointReq;
import com.zjhh.user.vo.BuryingPointVo;
import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2022/6/2 17:16
 */
public interface BuryingPointService {

    /**
     * 添加埋点
     *
     * @param req
     */
    void addPoint(AddPointReq req, HttpServletRequest request);

    /**
     * 埋点信息分页
     *
     * @param req
     * @return
     */
    Page<BuryingPointVo> page(PageBuryingPointReq req);
}

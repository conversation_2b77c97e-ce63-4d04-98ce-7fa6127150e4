package com.zjhh.user.utils;

import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.alibaba.xxpt.gateway.shared.client.http.GetClient;
import com.alibaba.xxpt.gateway.shared.client.http.PostClient;
import com.zjhh.comm.config.SystemProperties;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/10/21 14:09
 */
@Component
public class ZwddUtil {

    @Resource
    private SystemProperties systemProperties;

    @Resource
    private ExecutableClient executableClient;

    /**
     * 获取扫码的getClient
     *
     * @param api
     * @return
     */
    public GetClient getScanGetClient(String api) {
        GetClient getClient = executableClient.newGetClient(api);
        getClient.accessKey(systemProperties.getZwddAuth().getScanAppKey());
        getClient.secretKey(systemProperties.getZwddAuth().getScanAppSecret());
        return getClient;
    }

    /**
     * 获取扫码的postClient
     *
     * @param api
     * @return
     */
    public PostClient getScanPostClient(String api) {
        PostClient postClient = executableClient.newPostClient(api);
        postClient.accessKey(systemProperties.getZwddAuth().getScanAppKey());
        postClient.secretKey(systemProperties.getZwddAuth().getScanAppSecret());
        return postClient;
    }

    /**
     * 获取微应用的getClient
     *
     * @param api
     * @return
     */
    public GetClient getGetClient(String api) {
        GetClient getClient = executableClient.newGetClient(api);
        getClient.accessKey(systemProperties.getZwddAuth().getAppKey());
        getClient.secretKey(systemProperties.getZwddAuth().getAppSecret());
        return getClient;
    }

    /**
     * 获取微应用的postClient
     *
     * @param api
     * @return
     */
    public PostClient getPostClient(String api) {
        PostClient postClient = executableClient.newPostClient(api);
        postClient.accessKey(systemProperties.getZwddAuth().getAppKey());
        postClient.secretKey(systemProperties.getZwddAuth().getAppSecret());
        return postClient;
    }
}

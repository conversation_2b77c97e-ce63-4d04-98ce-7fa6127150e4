package com.zjhh.user.utils;

import com.zjhh.comm.exception.BizException;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @since 2022/7/4 15:39
 */
@Slf4j
public class Encrypt {

    private String Algorithm = "Blowfish";
    private SecretKey deskey;
    private Cipher cipher;

    public Encrypt() {
        this.init();
    }

    private void init() {
        this.deskey = new SecretKeySpec("hzg-soft".getBytes(), this.Algorithm);

        try {
            this.cipher = Cipher.getInstance(this.Algorithm);
        } catch (NoSuchAlgorithmException var2) {
            log.error("没有此加密算法，加密器初始化失败", var2);
            throw new BizException("没有此加密算法，加密器初始化失败");
        } catch (NoSuchPaddingException var3) {
            log.error("加密器初始化失败", var3);
            throw new BizException("加密器初始化失败");
        }
    }

    private byte[] createEncryptor(byte[] datasource) {
        byte[] encryptorData = (byte[]) null;

        try {
            this.cipher.init(1, this.deskey);
            encryptorData = this.cipher.doFinal(datasource);
            return encryptorData;
        } catch (InvalidKeyException var4) {
            log.error("非法的加密密匙，加密失败", var4);
            throw new BizException("非法的加密密匙，加密失败");
        } catch (BadPaddingException var5) {
            log.error("非法的加密数据，加密失败", var5);
            throw new BizException("非法的加密数据，加密失败");
        } catch (IllegalBlockSizeException var6) {
            log.error("加密字符串字节数不对，加密失败", var6);
            throw new BizException("加密字符串字节数不对，加密失败");
        }
    }

    public String createEncryptor(String datasource) {
        byte[] encryptorData = this.createEncryptor(datasource.getBytes(StandardCharsets.UTF_8));
        return URLEncoder.encode(Base64.getEncoder().encodeToString(encryptorData), StandardCharsets.UTF_8);
    }

    private byte[] createDecryptor(byte[] datasource) {
        byte[] decryptorData = (byte[]) null;

        try {
            this.cipher.init(2, this.deskey);
            decryptorData = this.cipher.doFinal(datasource);
            return decryptorData;
        } catch (InvalidKeyException var4) {
            log.error("非法的解密密匙，解密失败", var4);
            throw new BizException("非法的解密密匙，解密失败");
        } catch (BadPaddingException var5) {
            log.error("非法的解密数据，解密失败", var5);
            throw new BizException("非法的解密数据，解密失败");
        } catch (IllegalBlockSizeException var6) {
            log.error("解密字符串字节数不对，解密失败", var6);
            throw new BizException("解密字符串字节数不对，解密失败");
        }
    }

    public String createDecryptor(String datasource) {
        datasource = this.urlEncoder(datasource);
        byte[] decryptorData;

        try {
            decryptorData = Base64.getDecoder().decode(datasource);
        } catch (IllegalArgumentException var4) {
            log.error("字符串Base64解码失败", var4);
            throw new BizException("字符串Base64解码失败");
        }

        return new String(this.createDecryptor(decryptorData), StandardCharsets.UTF_8);
    }

    private String urlEncoder(String datasource) {
        return datasource.indexOf('%') < 0 ? datasource :
                this.urlEncoder(URLDecoder.decode(datasource, StandardCharsets.UTF_8));
    }

    public static void main(String[] args) {
        Encrypt encrypt = new Encrypt();
        String code = encrypt.createDecryptor("me0xb77CVmezSjiLLciMAg==");
        System.out.println(code);
    }
}

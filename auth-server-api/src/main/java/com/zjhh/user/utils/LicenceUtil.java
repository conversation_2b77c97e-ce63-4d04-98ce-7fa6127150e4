package com.zjhh.user.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.system.oshi.OshiUtil;
import com.alibaba.fastjson2.JSONObject;
import com.zjhh.comm.vo.LicenceDataVo;
import com.zjhh.comm.vo.LicenceNetworkVo;
import com.zjhh.comm.vo.LicenceValidVo;
import com.zjhh.comm.vo.LicenceXmlVo;
import lombok.extern.slf4j.Slf4j;
import oshi.hardware.NetworkIF;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/3/22 11:42
 */
@Slf4j
public class LicenceUtil {

    public static LicenceValidVo checkLicence(LicenceXmlVo param) {
        LicenceValidVo vo = new LicenceValidVo();
        try {
            String data = decryptData(param.getData(), param.getCipherNumber());
            String cipher = decryptCipher(param.getCipher(), param.getCipherNumber());
            String signStr = param.getCompany() +
                    cipher +
                    param.getCipherNumber() +
                    data +
                    param.getIssueDate() +
                    param.getSignSecret() +
                    param.getTimestamp();
            String sign = SecureUtil.hmacMd5(param.getSignSecret()).digestHex(signStr);
            if (!sign.equals(param.getSign())) {
                vo.setValid(false);
                return vo;
            }
            RSA rsa = new RSA(null, cipher);
            String dataRsa = rsa.decryptStr(data, KeyType.PublicKey);
            LicenceDataVo dataVo = JSONObject.parseObject(dataRsa, LicenceDataVo.class);
            log.info(JSONObject.toJSONString(dataVo));
            if (checkMacAddress(dataVo)) {
                vo.setValid(true);
                vo.setLicenceData(dataVo);
            } else {
                vo.setValid(false);
                return vo;
            }
            return vo;
        } catch (Exception e) {
            log.error("密钥验证失败", e);
            vo.setValid(false);
            return vo;
        }

    }

    private static boolean checkMacAddress(LicenceDataVo dataVo) {
        List<String> localMacAddressList = new ArrayList<>();
        List<NetworkIF> list = OshiUtil.getNetworkIFs();
        log.info("当前机器的MAC地址为：{}, 证书的许可MAC地址为：{}", JSONObject.toJSONString(list), JSONObject.toJSONString(dataVo.getNetworks()));
        list.forEach(networkIF -> {
            if (networkIF.getIPv4addr().length > 0 && !networkIF.getName().contains("docker")) {
                localMacAddressList.add(networkIF.getMacaddr());
            }
        });
        List<LicenceNetworkVo> networks = dataVo.getNetworks();
        for (LicenceNetworkVo networkVo : networks) {
            if (localMacAddressList.contains(networkVo.getMacAddress())) {
                return true;
            }
        }
        return false;
    }


    private static String decryptData(String str, int offset) {
        str = Base64.decodeStr(new StringBuilder(str).reverse());
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            sb.append((char) (((int) str.charAt(i)) - offset));
        }
        return sb.toString();
    }

    private static String decryptCipher(String str, int offset) {
        str = Base64.decodeStr(new StringBuilder(str).reverse());
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            sb.append((char) (((int) str.charAt(i)) + offset));
        }
        return sb.toString();
    }

    public static String encryptLicenceId(String str) {
        str = Base64.encode(new StringBuilder(str).reverse());
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            sb.append((char) (((int) str.charAt(i)) + 128));
        }
        return sb.toString();
    }

    public static void main(String[] args) throws Exception {
        File file = FileUtil.file("/Users/<USER>/Downloads/zjhh_licence.xml");
        LicenceValidVo validVo;
        try (InputStream inputStream = Files.newInputStream(file.toPath())) {
            String xml = IoUtil.readUtf8(inputStream);
            Map<String, Object> map = XmlUtil.xmlToMap(xml);
            LicenceXmlVo xmlVo = new LicenceXmlVo();
            BeanUtil.fillBeanWithMap(map, xmlVo, true);
            validVo = LicenceUtil.checkLicence(xmlVo);
            System.out.println(JSONObject.toJSONString(validVo));
        } catch (Exception e) {

        }
    }


}

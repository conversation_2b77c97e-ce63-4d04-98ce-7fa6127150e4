package com.zjhh.user.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.utils.PathUtil;
import com.zjhh.comm.utils.SnowflakeUtil;
import com.zjhh.system.service.DictService;
import com.zjhh.user.vo.FileVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/3/30 18:00
 */
@Slf4j
@Component
public class BaseFileUtil {

    @Resource
    private DictService dictService;

    private static final List<String> DOC_WHITE_LIST = CollUtil.newArrayList("pdf", "txt", "jpg", "jpeg", "png", "gif", "tiff", "bmp", "svg", "psd",
            "ai", "raw", "avi", "wmv", "mpg", "mpeg", "mov", "rm", "ram", "swf", "flv", "rmvb", "mp4", "zip", "7z", "rar", "tar", "gzip", "xls", "xlsx", "doc", "docx", "ppt", "pptx");

    public FileVo upload(MultipartFile file) {
        String fileType = FileUtil.getSuffix(file.getOriginalFilename());
        if (StrUtil.isBlank(fileType) || !DOC_WHITE_LIST.contains(fileType.toLowerCase())) {
            throw new BizException("上传文件格式不正确！");
        }
        String path = PathUtil.createPath() + SnowflakeUtil.createId() + "." + fileType;
        FileVo fileVo = new FileVo();
        fileVo.setFilepath(path);
        fileVo.setFilename(file.getOriginalFilename());
        System.out.println(path);
        try {
            System.out.println(dictService.getFileUploadPath() + path);
            file.transferTo(FileUtil.touch(dictService.getFileUploadPath() + path));
        } catch (IOException e) {
            log.error("上传文件失败", e);
            throw new BizException("上传文件失败！");
        }
        return fileVo;
    }


}

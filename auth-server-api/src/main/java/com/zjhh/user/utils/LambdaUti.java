package com.zjhh.user.utils;

import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2021/10/26 16:09
 */
public class LambdaUti {

    public static <T> Consumer<T> consumerWithIndex(BiConsumer<T, Integer> consumer) {
        class Obj {
            int i;
        }
        Obj obj = new Obj();
        return t -> {
            int index = obj.i++;
            consumer.accept(t, index);
        };
    }
}

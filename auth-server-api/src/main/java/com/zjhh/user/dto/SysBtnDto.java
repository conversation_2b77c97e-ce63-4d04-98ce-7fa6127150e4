package com.zjhh.user.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.user.enume.SysDisplayEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/28 17:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysBtnDto extends SysTagDto {

    @Serial
    private static final long serialVersionUID = 808375377717273642L;

    @Schema(hidden = true)
    @JsonIgnore
    private Integer displaySet;

    private List<String> show;

    public List<String> getShow() {
        List<String> shows = new ArrayList<>();
        if (SysDisplayEnum.ALL.value() == displaySet) {
            shows.add("chart");
            shows.add("table");
        } else if (SysDisplayEnum.CHART.value() == displaySet) {
            shows.add("chart");
        } else if (SysDisplayEnum.TABLE.value() == displaySet) {
            shows.add("table");
        }
        return shows;
    }
}

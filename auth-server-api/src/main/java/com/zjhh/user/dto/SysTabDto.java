package com.zjhh.user.dto;

import cn.hutool.core.lang.tree.Tree;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/27 16:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysTabDto extends SysTagDto {

    @Serial
    private static final long serialVersionUID = -7715226086594113381L;

    @JsonIgnore
    @Schema(hidden = true)
    private String uuid;

    @JsonIgnore
    @Schema(hidden = true)
    private String parentUuid;

    @JsonIgnore
    @Schema(hidden = true)
    private Integer displayArea;

    @JsonIgnore
    @Schema(hidden = true)
    private Boolean leaf;

    @Schema(description = "页面说明")
    private String tagExplain;

    private List<SysTabDto> children;

    private List<Tree<String>> columns;
}

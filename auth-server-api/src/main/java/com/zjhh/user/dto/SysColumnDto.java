package com.zjhh.user.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/27 16:10
 */
@Data
public class SysColumnDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -6026099770427007102L;

    private String uuid;

    private String columnId;

    private String columnName;

    private String parentUuid;

    private String linkMenuCode;

    private List<SysColumnDto> children;

    private String tabUuid;

    private Integer sort;

    private Boolean isLeaf;
}

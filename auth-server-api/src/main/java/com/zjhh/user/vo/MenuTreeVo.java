package com.zjhh.user.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zjhh.comm.vo.BaseVo;
import lombok.Data;

import java.io.Serial;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/11/23 15:23
 */
@Data
public class MenuTreeVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 1988419196588924034L;

    private String router;

    private String key;

    private String parentKey;

    private String title;

    private String value;

    private Map<String, String> scopedSlots;

    private List<MenuTreeVo> children;

    public List<MenuTreeVo> getChildren() {
        return CollUtil.isEmpty(children) ? null : children;
    }

    public Map<String, String> getScopedSlots() {
        Map<String, String> map = new HashMap<>(1);
        map.put("title", "title");
        return map;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        return ObjectUtil.equal(key, ((MenuTreeVo) o).key);
    }

    @Override
    public int hashCode() {
        return key.hashCode();
    }
}

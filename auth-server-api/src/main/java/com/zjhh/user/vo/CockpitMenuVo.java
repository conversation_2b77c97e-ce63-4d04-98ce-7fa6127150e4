package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 驾驶舱菜单
 *
 * <AUTHOR>
 * @since 2021/8/4 13:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CockpitMenuVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 5017219267128069249L;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "地址")
    private String path;

    @Schema(description = "排序")
    private String sort;
}

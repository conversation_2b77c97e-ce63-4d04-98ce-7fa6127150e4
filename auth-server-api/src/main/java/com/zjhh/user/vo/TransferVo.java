package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/20 12:42
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TransferVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -9016787504152955363L;

    @Schema(description = "老年度")
    private Integer oldYear;

    @Schema(description = "新年度")
    private Integer newYear;
}

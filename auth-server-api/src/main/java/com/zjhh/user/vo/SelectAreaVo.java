package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.comm.vo.TreeSelectVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/9 16:47
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SelectAreaVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 1159891462986219922L;

    @Schema(description = "默认值")
    private String defaultValue;

    @Schema(description = "树形结构")
    private List<TreeSelectVo> treeSelectList;
}

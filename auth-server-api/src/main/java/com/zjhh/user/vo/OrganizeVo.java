package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/9 17:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrganizeVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 7998727771411975201L;

    @Schema(description = "ID")
    private String id;

    @Schema(description = "编码")
    private String key;

    @Schema(description = "名称")
    private String title;

    @Schema(description = "类型")
    private Integer type;

    @Schema(description = "是否叶子节点")
    private Boolean isLeaf;

    @Schema(hidden = true)
    private String parentCode;

    @Schema(description = "slot")
    private SlotVo slots;

    @Schema(description = "scopedSlots")
    private ScopedSlotVo scopedSlots;

    @Schema(description = "子节点")
    private List<OrganizeVo> children;

}

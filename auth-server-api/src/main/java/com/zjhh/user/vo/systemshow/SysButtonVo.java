package com.zjhh.user.vo.systemshow;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/10/25 15:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysButtonVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -7730668362572478025L;

    @Schema(description = "buttonUuid")
    private String buttonUuid;

    @Schema(description = "按钮标识")
    private String buttonId;

    @Schema(description = "按钮名称标识")
    private String buttonName;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "0-全部显示 1-图形 2-表格 3-不显示")
    private Integer displaySet;

    @Schema(description = "配置显示")
    private Integer displayShow;

    @Schema(description = "描述")
    private String desc;
}

package com.zjhh.user.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * 政府钉钉响应bean
 *
 * @param <T>
 */
@Data
public class ResponseBean<T> {
    // 网关调用是否成功
    private boolean success;

    // 请求正常则返回，不正常的话没有
    private Content<T> content;

    // 以下是错误反馈字段
    // 网关请求ID
    @JSONField(name = "_RequestId")
    private String requestId;

    // 网关Code
    @JSONField(name = "Code")
    private String code;

    // 网关错误码
    private String errorCode;

    // 网关错误级别
    private String errorLevel;

    // 网关错误信息
    private String errorMsg;

    // 网关HostId
    @JSONField(name = "HostId")
    private String hostId;

    // 网关信息
    @JSONField(name = "Message")
    private String message;
}

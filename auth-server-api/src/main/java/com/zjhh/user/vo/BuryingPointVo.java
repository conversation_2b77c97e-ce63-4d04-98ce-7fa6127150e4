package com.zjhh.user.vo;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @since 2023/12/6 14:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuryingPointVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -3467133150186455339L;

    @Schema(description = "id")
    @ExcelIgnore
    private String id;

    @Schema(description = "用户编码")
    @ExcelIgnore
    private String userCode;

    @Schema(description = "用户名")
    @ExcelProperty(value = "用户名", index = 0)
    private String username;

    @Schema(description = "菜单编码")
    @ExcelIgnore
    private String menuCode;

    @Schema(description = "菜单名称")
    @ExcelProperty(value = "访问页面", index = 1)
    private String menuName;

    @Schema(description = "访问时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "访问时间", index = 2)
    private LocalDateTime openTime;

    @Schema(hidden = true)
    @ExcelIgnore
    private LocalDateTime closeTime;

    @Schema(description = "访问ip")
    @ExcelProperty(value = "访问ip", index = 4)
    private String ip;

    @Schema(description = "停留时长-秒")
    @ExcelProperty(value = "停留时长（秒）", index = 3)
    private Long stayTime;

    public Long getStayTime() {
        if (ObjectUtil.isNotNull(openTime) && ObjectUtil.isNotNull(closeTime)) {
            return Math.abs(closeTime.until(openTime, ChronoUnit.SECONDS));
        }
        return null;
    }
}

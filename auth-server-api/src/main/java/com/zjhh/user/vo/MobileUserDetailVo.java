package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/1/18 11:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MobileUserDetailVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -8522821160406701272L;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "移动端头像")
    private String mobileAvatar;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "地区")
    private String area;

}

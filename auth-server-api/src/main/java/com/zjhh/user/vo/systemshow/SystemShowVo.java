package com.zjhh.user.vo.systemshow;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;
import com.zjhh.user.dto.SysBtnDto;
import com.zjhh.user.dto.SysSearchDto;
import com.zjhh.user.dto.SysTabDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/27 14:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SystemShowVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 1278872020734438945L;

    @Schema(hidden = true)
    @JsonIgnore
    private String menuCode;

    @Schema(hidden = true)
    @JsonIgnore
    private String component;

    private List<SysTabDto> tableTabs;

    private List<SysTabDto> chartTabs;

    private List<SysBtnDto> buttons;

    private List<SysSearchDto> searches;

    @Schema(description = "移动端用，不区分图tab和表tab")
    private List<SysTabDto> tabs;
}

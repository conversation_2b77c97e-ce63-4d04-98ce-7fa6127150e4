package com.zjhh.user.vo;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/6/15 15:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -7572932039915710857L;

    @ExcelIgnore
    private String id;

    @ExcelIgnore
    private String code;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    @ExcelProperty(value = "用户姓名", index = 1)
    private String username;

    /**
     * 登录名
     */
    @Schema(description = "账号")
    @ExcelProperty(value = "用户账号", index = 0)
    private String loginName;

    /**
     * 是否锁定
     */
    @Schema(description = "是否锁定")
    @ExcelIgnore
    private Boolean locked;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    @ExcelProperty(value = "手机号码", index = 3)
    private String mobile;

    @Schema(description = "创建时间")
    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @Schema(description = "角色")
    @ExcelIgnore
    private List<RoleVo> roles;

    @Schema(description = "头像")
    @ExcelIgnore
    private String avatar;

    @Schema(description = "用户类型")
    @ExcelIgnore
    private Integer userType;

    @Schema(description = "是否新用户")
    @ExcelIgnore
    private Boolean newUser;

    @Schema(description = "创建用户")
    @ExcelIgnore
    private String createUser;

    @Schema(description = "是否同步政务钉钉")
    @ExcelIgnore
    private Boolean syncZwdd;

    @Schema(description = "当前组织结构")
    @ExcelIgnore
    private String orgName;

    @Schema(description = "父级组织结构")
    @ExcelIgnore
    private String orgParentName;

    @Schema(description = "组织结构")
    @ExcelProperty(value = "组织结构", index = 2)
    private String organizeName;

    @Schema(hidden = true)
    @ExcelProperty(value = "同步标志", index = 4)
    private String syncZwddName;

    @Schema(hidden = true)
    @ExcelProperty(value = "状态", index = 5)
    private String lockedName;

    public String getSyncZwddName() {
        return BooleanUtil.isTrue(this.syncZwdd) ? "已同步" : "未同步";
    }

    public String getLockedName() {
        return BooleanUtil.isTrue(this.locked) ? "锁定" : "正常";
    }
}

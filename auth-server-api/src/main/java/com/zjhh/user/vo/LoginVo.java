package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.user.dao.entity.VOrganize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/6/12 11:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LoginVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 3631861946150454603L;

    private String userId;

    private String token;

    private String code;

    private String loginName;

    private String username;

    private Integer userType;

    private Boolean newUser;

    private Boolean locked;

    private String themeConfig;

    private String avatar;

    private VOrganize organize;


    /**
     * 根据用户类型设置角色，跟分配的用户角色不同
     */
    private List<String> roles;

    /**
     * 数据权限
     */
    private List<String> authAreaCodes;

    @Schema(description = "是否开启复杂密码认证")
    private Boolean complexPassword;

    @Schema(description = "水印文本")
    private List<String> watermarks;

    /**
     * 选中行政区划编码
     */
    private String selectedAreaCode;

    /**
     * 选择行政区划名称
     */
    private String selectedAreaName;

    /**
     * 默认的行政区划编码
     */
    private String defaultAreaCode;

    @Schema(description = "是否开启代表监督录入")
    private Boolean repSupervision;

    @Schema(description = "是否开启我要建议")
    private Boolean suggest;

    @Schema(description = "跳转信息")
    private FullPathVo fullPath;

    @Schema(description = "第三方登录省厅所需参数")
    private ThirdLoginVo thirdLoginVo;

    @Schema(description = "登录类型： 1-本地登录 2-第三方登录")
    private Integer loginType;
}

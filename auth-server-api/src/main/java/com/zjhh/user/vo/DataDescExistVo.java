package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/8 14:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataDescExistVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 941540147947792079L;

    @Schema(description = "是否存在")
    private Boolean exist;

    @Schema(description = "文档guid")
    private List<DataDescExistFileVo> files;
}

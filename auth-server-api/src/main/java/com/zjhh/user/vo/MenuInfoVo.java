package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/30 10:07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MenuInfoVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -4504923374704525256L;

    @Schema(description = "菜单编码")
    private String menuCode;

    @Schema(description = "是否调转")
    private Boolean isRedirect;

    @Schema(description = "地址")
    private String fullPath;

    @Schema(description = "按钮列表")
    private List<String> buttons;
}

package com.zjhh.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 根据组织code查询详情响应Content
 *
 * <AUTHOR>
 * @date 2021-05-26 3:41 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetOrganizationByCode extends BaseVo {

    @Serial
    private static final long serialVersionUID = 4075123828914729155L;

    // 组织code
    private String organizationCode;

    // 组织名称
    private String organizationName;

    // 排序码
    private String displayOrder;

    // 父组织Code
    private String parentCode;

    // 父组织名称
    private String parentName;

    // 组织状态，A - 有效的数据, F - 无效的数据
    private String status;

    // 组织类型Code
    private String typeCode;

    // 组织类型名称
    private String typeName;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    // 修改时间 TODO 不确定有没有，接口文档上没有，示例里边有
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtModified;

    // 负责人code，|拼接多个
    private String responsibleEmployeeCodes;

    // 条线codes,多个用|拼接
    private String businessStripCodes;

    // 详细地址
    private String address;

    // 联系人code
    private String contactEmployeeCode;

    // 联系人号码
    private String contactNumber;

    // 行政区划code
    private String divisionCode;

    // 简称
    private String shortName;

    // 组织机构代码
    private String institutionCode;

    // 其它名称
    private String otherName;

    // 邮政编码
    private String postalCode;

    // 备注
    private String remarks;

    // 统一社会信用代码
    private String unifiedSocialCreditCode;

    // 机构/单位级别
    private String institutionLevelCode;

    // 机构/单位性质
    private String institutionNature;

    // 机构/单位类别
    private String institutionCategory;

    //是否叶子节点
    private boolean leaf;
}

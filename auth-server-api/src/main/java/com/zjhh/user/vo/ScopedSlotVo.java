package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/4/1 11:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class ScopedSlotVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 3459881592783879704L;

    @Schema(description = "名称")
    private String title;
}

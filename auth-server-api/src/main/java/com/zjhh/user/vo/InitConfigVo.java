package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/15 11:34
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InitConfigVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -3250668332339470663L;

    @Schema(description = "是否需要验证码")
    private Boolean needCaptcha;

    @Schema(description = "证书是否有效")
    private Boolean licenceValid;

    @Schema(description = "证书有效期")
    private String expireDate;

    @Schema(description = "证书提示")
    private String licenceTips;

    @Schema(description = "系统名称")
    private String systemName;

    @Schema(description = "后台名称")
    private String adminName;
}

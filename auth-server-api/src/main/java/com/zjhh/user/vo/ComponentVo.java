package com.zjhh.user.vo;

import cn.hutool.core.collection.CollUtil;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/29 15:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ComponentVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -8928430998290420009L;

    private String key;

    private String title;

    private String component;

    private Boolean disabled;

    @Schema(hidden = true)
    private String parentKey;

    private List<ComponentVo> children;

    public List<ComponentVo> getChildren() {
        return CollUtil.isEmpty(this.children) ? null : this.children;
    }
}

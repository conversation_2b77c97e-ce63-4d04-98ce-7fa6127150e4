package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import lombok.*;

import java.io.Serial;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/10/27 09:30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TreeVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 860523316130234449L;

    private String key;

    private String title;

    private String parentKey;

    private Object extra;

    private Map<String, String> scopedSlots;

    private List<TreeVo> children;

    public Map<String, String> getScopedSlots() {
        Map<String, String> map = new HashMap<>(1);
        map.put("title", "title");
        return map;
    }
}

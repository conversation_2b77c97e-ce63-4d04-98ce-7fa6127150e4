package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/20 12:23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserSessionVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -7679654741004885623L;

    @Schema(description = "登录用户信息")
    private LoginVo user;

    @Schema(description = "路由信息")
    private RouterVo router;
}

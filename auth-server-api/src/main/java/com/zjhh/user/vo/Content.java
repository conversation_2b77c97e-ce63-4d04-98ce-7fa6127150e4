package com.zjhh.user.vo;

import lombok.Data;

/**
 * @param <T>
 */
@Data
public class Content<T> {
    // 业务调用是否成功
    private boolean success;

    // 业务响应Code
    private String responseCode;

    // 业务响应信息
    private String responseMessage;

    // 业务请求ID
    private String requestId;

    // 当前页码
    private Integer currentPage;

    // 总页数
    private Integer pageSize;

    // 数据总数
    private Integer totalSize;

    // 业务返回值
    private T data;

    private String msgId;

}

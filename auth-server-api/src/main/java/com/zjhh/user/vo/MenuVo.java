package com.zjhh.user.vo;

import com.zjhh.user.dao.entity.Menu;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/15 17:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MenuVo extends Menu {

    @Serial
    private static final long serialVersionUID = 1097553912451678070L;

    @Schema(description = "父资源ID")
    private String parentId;
}

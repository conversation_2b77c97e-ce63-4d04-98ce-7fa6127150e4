package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/12/2 11:04
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InitSystemVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -816931627503184648L;

    @Schema(description = "系统的行政区划编码")
    private String systemArea;
}

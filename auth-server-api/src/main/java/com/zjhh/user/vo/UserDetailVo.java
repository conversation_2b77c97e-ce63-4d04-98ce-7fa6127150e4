package com.zjhh.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/2 13:53
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserDetailVo extends UserVo {

    @Serial
    private static final long serialVersionUID = -9215850824857412665L;

    @Schema(description = "用户描述")
    private String description;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "浙政钉ID")
    private String zzdAccountId;

    @Schema(description = "移动端头像")
    private String mobileAvatar;
}

package com.zjhh.user.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.localdatetime.LocalDateTimeDateConverter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2020/6/15 10:30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -1833817005974139929L;

    @ExcelIgnore
    private String id;

    @ExcelIgnore
    private String userCode;

    @ExcelIgnore
    private String roleCode;

    @Schema(description = "角色编码")
    @ExcelProperty(value = "角色标识", index = 0)
    private String roleKey;

    @Schema(description = "角色名称")
    @ExcelProperty(value = "角色名称", index = 1)
    private String name;

    @Schema(description = "角色描述")
    @ExcelProperty(value = "角色描述", index = 2)
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间", index = 3, converter = LocalDateTimeDateConverter.class)
    private LocalDateTime gmtCreate;

}

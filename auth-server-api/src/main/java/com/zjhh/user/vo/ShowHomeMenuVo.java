package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/1/17 17:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ShowHomeMenuVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 4787461028263224601L;

    @Schema(description = "menuCode")
    private String menuCode;

    @Schema(description = "是否末级菜单")
    private Boolean leaf;

    @Schema(description = "菜单名称")
    private String menuName;

    @Schema(description = "菜单图标")
    private String icon;

    @Schema(description = "组件")
    private String router;
}

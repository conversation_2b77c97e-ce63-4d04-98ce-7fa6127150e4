package com.zjhh.user.vo;

import com.zjhh.comm.dto.LicenceProduct;
import com.zjhh.comm.dto.LicenceServerInfo;
import com.zjhh.comm.dto.LicenceServiceInfo;
import com.zjhh.comm.dto.LicenceUser;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/24 15:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LicenceVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -7359135622847201710L;

    @Schema(description = "服务商信息")
    private LicenceServiceInfo service;

    @Schema(description = "服务器信息")
    private List<LicenceServerInfo> servers;

    @Schema(description = "产品信息")
    private LicenceProduct product;

    @Schema(description = "用户信息")
    private LicenceUser user;
}

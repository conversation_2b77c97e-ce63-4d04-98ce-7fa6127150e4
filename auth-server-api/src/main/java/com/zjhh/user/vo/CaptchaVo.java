package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/15 9:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CaptchaVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -7050059501216285443L;

    @Schema(description = "图形验证码")
    private String imageData;

    @Schema(description = "标识key")
    private String imageKey;
}

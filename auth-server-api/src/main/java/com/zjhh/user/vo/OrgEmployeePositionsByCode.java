package com.zjhh.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 根据组织code查询详情响应content
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgEmployeePositionsByCode {

    // 组织code
    private String organizationCode;

    // 人员Code
    private String employeeCode;

    // 是否员工主任职
    private Boolean mainJob;

    // 任职状态
    private String status;

    // 任职序号
    private String order;

    // 组织内排序
    private String orderInOrganization;

    // 办公地址
    private String govEmpPosAddress;

    // 任职
    private String govEmpPosJob;

    // 所在单位Code
    private String empPosUnitCode;

    // 所在内设机构Code
    private String empPosInnerInstitutionCode;

    // 所在部门Code
    private String empPosDepartmentCode;

    // 所在虚拟组织Code
    private String empPosVirtualOrganizationCode;

    // 传真
    private String empPosFaxNo;

    // 人员角色
    private String empPosEmployeeRole;

    // 任职属性Code
    private String jobAttributesCode;

    // 职务层次Code
    private String posJobRankCode;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String gmtCreate;
}

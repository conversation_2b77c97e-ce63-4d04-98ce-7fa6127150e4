package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/1/18 18:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -7296613239277515316L;

    @Schema(description = "上传后文件路径")
    private String filepath;

    @Schema(description = "文件名称-带后缀")
    private String filename;
}

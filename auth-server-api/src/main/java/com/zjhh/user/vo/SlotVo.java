package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/22 10:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class SlotVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -4113915688360405752L;

    @Schema(description = "图标")
    private String icon;
}

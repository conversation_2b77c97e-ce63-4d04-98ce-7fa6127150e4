package com.zjhh.user.vo;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/25 15:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysColumnVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 4664245398280375459L;

    @Schema(description = "menuCode", hidden = true)
    @JsonIgnore
    private String menuCode;

    @Schema(description = "columnUuid")
    private String columnUuid;

    @Schema(description = "页签uuid")
    private String tabUuid;

    @Schema(description = "页签名称")
    private String tabName;

    @Schema(description = "字段标识")
    private String columnId;

    @Schema(description = "字段名称")
    private String columnName;

    @Schema(description = "跳转的菜单编码")
    private String linkMenuCode;

    @Schema(description = "是否显示")
    private Boolean showed;

    @Schema(description = "父节点uuid")
    private String parentUuid;

    @Schema(description = "是否叶子节点")
    private Boolean isLeaf;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "描述")
    private String desc;

    private List<SysColumnVo> children;

    public List<SysColumnVo> getChildren() {
        return CollUtil.isEmpty(children) ? null : children;
    }
}

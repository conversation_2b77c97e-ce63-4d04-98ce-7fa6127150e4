package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.user.dao.entity.VOrganize;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/24 15:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdLoginVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -5640373586197023641L;

    private String appKey;

    private String sign;

    private Long timestamp;

    private String token;

    private List<String> userAuthAreaCodes;

    private VOrganize organize;
}

package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 路由
 *
 * <AUTHOR>
 * @since 2021/3/2 16:37
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RouterVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 5546322078094310553L;

    @Schema(description = "菜单ID")
    private String menuId;

    @Schema(description = "菜单编码")
    private String menuCode;

    @Schema(description = "菜单名称")
    private String name;

    @Schema(description = "菜单对应前端组件")
    private String router;

    @Schema(description = "菜单图标")
    private String icon;

    @Schema(description = "外部链接地址--在外部显示时候使用")
    private String link;

    @Schema(description = "外部链接地址")
    private String page;

    @Schema(description = "菜单类型 0：目录；1：菜单；2：按钮")
    private Integer menuType;

    @Schema(description = "地址")
    private String path;

    @Schema(description = "重定向")
    private String redirect;

    @Schema(description = "排序")
    private String sort;

    private List<RouterVo> children;

}

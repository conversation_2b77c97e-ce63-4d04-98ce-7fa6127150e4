package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/6/16 11:27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleButtonVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -3942459635895010135L;

    @Schema(description = "按钮列表")
    private List<CommButtonVo> buttons;

    @Schema(description = "选中的按钮")
    private List<String> checkedKeys;

}

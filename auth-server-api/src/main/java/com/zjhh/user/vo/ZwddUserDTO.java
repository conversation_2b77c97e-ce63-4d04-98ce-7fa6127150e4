package com.zjhh.user.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 政务钉钉用户
 *
 * <AUTHOR>
 * @date 2021-05-31 2:22 下午
 */
@Data
public class ZwddUserDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 3581522394049568214L;

    private Long id;

    private Long tenantId;

    private String accountName;

    private String userName;

    private String sex;

    private Long jurisdictionId;

    private String state;

    private String salt;

    private String password;

    private Long deptId;

    private Integer cractOrder;

    private String cractOuterUuid;

    private String accountId;

    private Integer sourceType;

    private LocalDateTime createTime;

    private Integer dataFlag;

    private Long deleteVersion;
}

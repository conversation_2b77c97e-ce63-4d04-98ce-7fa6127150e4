package com.zjhh.user.vo;

/**
 * <AUTHOR>
 * @date 2021-05-26 3:46 下午
 */
public enum ZwddCallBackEventEnum {
    ADD_UPDATE_ORGANIZATION("MOZI_VDS_TENANT_CHANGE|ORGANIZATION_ADD_UPDATE"),
    PARENT_CHANGE_ORGANIZATION("MOZI_VDS_TENANT_CHANGE|ORGANIZATION_PARENT_CHANGED"),
    REMOVE_ORGANIZATION("MOZI_VDS_TENANT_CHANGE|ORGANIZATION_REMOVE"),
    ADD_UPDATE_EMPLOYEE("MOZI_VDS_TENANT_CHANGE|EMPLOYEE_ADD_UPDATE"),
    LEAVE_EMPLOYEE("MOZI_VDS_TENANT_CHANGE|EMPLOYEE_LEAVE"),
    ATTACH_EMPLOYEE("MOZI_VDS_TENANT_CHANGE|ORGANIZATION_ATTACH_EMPLOYEE"),
    DE<PERSON>CH_EMPLOYEE("MOZI_VDS_TENANT_CHANGE|ORGANIZATION_DETACH_EMPLOYEE"),
    CHANGE_ACCOUNT_NAME("MOZI_BUC_ACCOUNT_MODIFY_REALM|change_account_name"),
    FROZEN("MOZI_BUC_ACCOUNT_MODIFY_REALM|frozen"),
    UNFROZEN("MOZI_BUC_ACCOUNT_MODIFY_REALM|unfrozen");

    private String index;

    private ZwddCallBackEventEnum(String index) {
        this.index = index;
    }

    public String getIndex() {
        return this.index;
    }

    public static ZwddCallBackEventEnum indexToEnum(String index) {
        ZwddCallBackEventEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            ZwddCallBackEventEnum eventTag = var1[var3];
            if (eventTag.getIndex().equals(index)) {
                return eventTag;
            }
        }

        return null;
    }

    public String toString() {
        return this.index;
    }
}

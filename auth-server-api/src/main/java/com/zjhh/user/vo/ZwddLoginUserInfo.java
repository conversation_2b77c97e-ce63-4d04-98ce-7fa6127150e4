package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 政务钉钉用户信息VO
 *
 * <AUTHOR>
 * @date 2021-05-26 3:30 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ZwddLoginUserInfo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -7076655255093859730L;

    private Long accountId;

    private String lastName;

    private String clientId;

    private Long realmId;

    private String tenantName;

    private String realmName;

    private String namespace;

    private Long tenantId;

    private String nickNameCn;

    private String tenantUserId;

    private String account;

    private String employeeCode;

}

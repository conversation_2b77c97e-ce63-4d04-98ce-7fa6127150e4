package com.zjhh.user.vo;

import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/6/2 15:30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataDescExistFileVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 1440964615671273623L;

    @Schema(description = "guid")
    private String guid;

    @Schema(hidden = true)
    @JsonIgnore
    private String fileDir;

    @Schema(description = "文件后缀")
    private String suffix;

    public String getSuffix() {
        return "." + FileUtil.getSuffix(fileDir);
    }
}

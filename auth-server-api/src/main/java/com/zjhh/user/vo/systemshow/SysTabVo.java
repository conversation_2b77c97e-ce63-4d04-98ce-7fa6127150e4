package com.zjhh.user.vo.systemshow;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.user.vo.SysColumnVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021/10/25 14:57
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysTabVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 890200505402914319L;

    @Schema(description = "tabUuid")
    private String tabUuid;

    @Schema(description = "tab标签标识")
    private String tabId;

    @Schema(description = "tab标签名称")
    private String tabName;

    @Schema(description = "显示区域 1-图形 2-表格")
    private Integer displayArea;

    @Schema(description = "是否显示")
    private Boolean showed;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "父节点")
    private String parentUuid;

    @Schema(description = "是否叶子节点")
    private Boolean leaf;

    @Schema(description = "描述")
    private String desc;

    @Schema(description = "页面说明")
    private String tagExplain;

    @Schema(description = "字段类型")
    private List<SysColumnVo> columns;

    @Schema(description = "选中的字段")
    private Set<String> selectedColumnUuids;
}

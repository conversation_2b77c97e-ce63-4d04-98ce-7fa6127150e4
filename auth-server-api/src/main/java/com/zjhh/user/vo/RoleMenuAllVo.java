package com.zjhh.user.vo;

import cn.hutool.core.lang.tree.Tree;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/5 17:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleMenuAllVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 6355777587246815619L;

    @Schema(description = "树形数据")
    private List<Tree<String>> treeData;

    @Schema(description = "选中的菜单")
    private List<String> checkedKeys;

    @Schema(description = "所有的菜单key")
    private List<String> allTreeKeys;

}

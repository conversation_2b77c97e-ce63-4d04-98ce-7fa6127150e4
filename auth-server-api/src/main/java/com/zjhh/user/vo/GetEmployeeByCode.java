package com.zjhh.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 员工详情VO
 *
 * <AUTHOR>
 * @date 2021-05-26 3:38 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetEmployeeByCode extends BaseVo {

    @Serial
    private static final long serialVersionUID = 6948396994086281266L;

    // 人员Code
    @Schema(description = "人员Code")
    private String employeeCode;

    // 人员姓名
    @Schema(description = "人员姓名")
    private String employeeName;

    // 人员状态
    @Schema(description = "人员状态")
    private String status;

    // 人员性别 Code
    @Schema(description = "人员性别")
    private String empGender;

    // 创建人
    @Schema(description = "创建人")
    private String creator;

    // 修改人
    @Schema(description = "修改人")
    private String modifier;

    // 创建时间
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    // 修改时间
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtModified;

    // 备注
    @Schema(description = "备注")
    private String govEmpRemarks;

    // 头像
    @Schema(description = "头像")
    private String govEmpAvatar;

    // 职级
    @Schema(description = "职级")
    private String empJobLevelCode;

    // 人员编制Code
    @Schema(description = "人员编制Code")
    private String empBudgetedPostCode;

    // 政治面貌
    @Schema(description = "政治面貌")
    private String empPoliticalStatusCode;

    //钉钉id
    @Schema(description = "钉钉id")
    private String dingdingId;

    @Schema(description = "手机号")
    private String mobile;

}

package com.zjhh.user.vo.systemshow;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/10/25 15:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysSearchVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 7961419433237778102L;

    @Schema(description = "searchUuid")
    private String searchUuid;

    @Schema(description = "搜索标识")
    private String searchId;

    @Schema(description = "搜索名称")
    private String searchName;

    @Schema(description = "默认值")
    private String defaultValue;

    @Schema(description = "0-全部显示 1-图形 2-表格 3-不显示")
    private Integer displaySet;

    @Schema(description = "配置显示")
    private Integer displayShow;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "描述")
    private String desc;

    @Schema(description = "是否可编辑")
    private Boolean editable;
}

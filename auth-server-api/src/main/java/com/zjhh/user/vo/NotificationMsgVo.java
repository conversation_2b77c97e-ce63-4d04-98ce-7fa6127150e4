package com.zjhh.user.vo;

import cn.hutool.core.util.StrUtil;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationMsgVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -748178826163943738L;

    @Schema(description = "消息类型-不填默认")
    private String msgType;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "图片链接")
    private String imageUrl;

    @Schema(description = "描述")
    private String desc;

    @Schema(description = "跳转url")
    private String url;

    private List<String> receiverIds;


    public String getMsgType() {
        return StrUtil.isBlank(msgType) ? "action_card" : msgType;
    }
}

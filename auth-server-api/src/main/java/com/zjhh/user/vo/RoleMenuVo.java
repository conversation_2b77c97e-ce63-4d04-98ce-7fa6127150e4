package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/5 16:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleMenuVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 2638194697424501937L;

    @Schema(description = "菜单编码")
    private String key;

    @Schema(description = "菜单名称")
    private String title;

    private List<RoleMenuVo> children;
}

package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/6/16 11:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CommButtonVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -3537162755018272722L;

    private String key;

    private String title;
}

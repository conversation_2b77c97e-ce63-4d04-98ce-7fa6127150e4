package com.zjhh.user.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/6/18 15:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FullPathVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 2238524289039386627L;

    @Schema(description = "是否调转")
    private Boolean isRedirect;

    @Schema(description = "地址")
    private String fullPath;
}

package com.zjhh.user.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2020/6/17 15:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateRoleReq extends AddRoleReq {

    @Serial
    private static final long serialVersionUID = 7034215116870045397L;

    @Schema
    private String roleId;
}

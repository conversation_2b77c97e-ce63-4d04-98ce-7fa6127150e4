package com.zjhh.user.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/6/17 10:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageUserReq extends PageReq {

    @Serial
    private static final long serialVersionUID = 1838293877893507094L;

    @Schema(description = "账号")
    private String loginName;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "是否锁定")
    private Boolean locked;

    @Schema(description = "组织结构编码")
    @NotBlank(message = "组织结构编码不能为空！")
    private String organizeCode;

    @Schema(hidden = true)
    private List<String> organizeCodes;

    @Schema(hidden = true)
    private Boolean isSuperManager;
}

package com.zjhh.user.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/16 14:27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RolePostReq extends RoleIdReq {

    @Serial
    private static final long serialVersionUID = 4154568160853510316L;

    @Schema(description = "岗位ID")
    @NotNull(message = "分配的岗位不能为空！")
    @Size(min = 1, message = "分配的岗位不能为空！")
    private List<Long> postIds;
}

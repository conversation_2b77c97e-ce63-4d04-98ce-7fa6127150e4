package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/6/15 14:06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JxrdThirdLoginReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -8458192481132439652L;

    @NotBlank(message = "appKey不能为空！")
    private String appKey;

    @NotBlank(message = "timestamp不能为空！")
    private String timestamp;

    @NotBlank(message = "sign不能为空！")
    private String sign;
}

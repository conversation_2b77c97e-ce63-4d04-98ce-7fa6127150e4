package com.zjhh.user.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2023/12/6 14:24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageBuryingPointReq extends PageReq {

    @Serial
    private static final long serialVersionUID = -3114359177594110391L;

    @Schema(description = "搜索关键字")
    private String searchKey;

    @Schema(description = "访问开始时间 yyyy-MM-dd")
    private String startTime;

    @Schema(description = "访问结束时间 yyyy-MM-dd")
    private String endTime;
}

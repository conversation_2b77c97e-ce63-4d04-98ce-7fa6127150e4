package com.zjhh.user.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/4 10:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateMenuReq extends AddMenuReq {

    @Serial
    private static final long serialVersionUID = 3413434614650802571L;

    @Schema
    private String menuId;
}

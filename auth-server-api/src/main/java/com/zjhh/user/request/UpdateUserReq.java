package com.zjhh.user.request;

import com.zjhh.comm.validator.EmailOrNull;
import com.zjhh.comm.validator.PhoneOrNull;
import com.zjhh.comm.validator.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/6/17 15:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateUserReq extends UserIdReq {

    @Serial
    private static final long serialVersionUID = 3870474732808603961L;

    @Xss
    @Schema(description = "姓名")
    @NotBlank(message = "姓名不能为空！")
    private String username;

    @Schema(description = "手机号")
    @PhoneOrNull
    private String mobile;

    @Xss
    @Schema(description = "用户描述")
    private String description;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "邮箱")
    @EmailOrNull
    private String email;

    @Schema(description = "岗位编码")
    private List<String> roleCodes;

    @Schema(description = "用户类型：1-系统管理员 2-普通用户")
    private Integer userType;

    @Schema(description = "浙政钉ID")
    private String zzdAccountId;
}

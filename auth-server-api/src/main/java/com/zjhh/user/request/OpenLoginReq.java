package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/8/11 10:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OpenLoginReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 295800600587651397L;

    @Schema(description = "唯一标识")
    @NotBlank(message = "appKey不能为空！")
    private String appKey;

    @Schema(description = "时间戳")
    @NotNull(message = "时间戳不能为空！")
    private String timestamp;

    @Schema(description = "签名")
    @NotBlank(message = "签名不能为空！")
    private String sign;

    @Schema(description = "组件编码")
    private String router;
}

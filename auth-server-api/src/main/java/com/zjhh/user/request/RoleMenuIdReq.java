package com.zjhh.user.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/6/16 11:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleMenuIdReq extends RoleIdReq {

    @Serial
    private static final long serialVersionUID = -2097660666588411611L;

    @Schema
    @NotNull(message = "menuCode不能为空！")
    private String menuCode;
}

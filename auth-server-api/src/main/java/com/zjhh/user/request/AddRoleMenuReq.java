package com.zjhh.user.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/3 15:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddRoleMenuReq extends RoleIdReq {

    @Serial
    private static final long serialVersionUID = 3852430494290270803L;

    @Schema
    @NotNull(message = "menuCodes不能为空！")
    private List<String> menuCodes;

}

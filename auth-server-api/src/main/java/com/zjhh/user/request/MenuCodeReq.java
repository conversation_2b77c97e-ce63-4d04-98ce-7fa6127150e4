package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/10/26 17:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MenuCodeReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -1182627937934923015L;

    @Schema(description = "menuCode")
    @NotBlank(message = "menuCode不能为空！")
    private String menuCode;
}

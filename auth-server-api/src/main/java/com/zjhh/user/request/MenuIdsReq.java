package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/4 10:30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MenuIdsReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -649454283471923218L;

    @Schema
    private List<String> menuIds;
}

package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/5 10:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ListUserIdReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -6509294381226073078L;

    @Schema
    @NotNull(message = "用户id不能为空")
    private List<String> userIds;
}

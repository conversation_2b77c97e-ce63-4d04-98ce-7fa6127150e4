package com.zjhh.user.request;

import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.validator.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/4/8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateUnitReq extends IdReq {

    @Serial
    private static final long serialVersionUID = 7220013837600711301L;

    @Xss
    @Schema(description = "预算单位名称")
    @NotBlank(message = "预算单位名称不能为空！")
    private String name;
}

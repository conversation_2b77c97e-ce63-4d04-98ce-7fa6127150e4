package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2020/6/12 10:59
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LoginNameReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 9015348340585512382L;

    @Schema(description = "登录账号")
    @NotBlank(message = "用户账号不能为空！")
    private String loginName;
}

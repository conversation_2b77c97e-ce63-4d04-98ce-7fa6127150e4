package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/6/2 17:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddPointReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -3106009538819112202L;

    @Schema(description = "from菜单")
    private String fromMenuCode;

    @Schema(description = "to菜单")
    @NotBlank(message = "to菜单不能为空！")
    private String toMenuCode;

}

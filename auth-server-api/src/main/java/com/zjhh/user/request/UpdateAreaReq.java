package com.zjhh.user.request;

import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.validator.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/4/8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateAreaReq extends IdReq {

    @Serial
    private static final long serialVersionUID = 1521635658748896315L;

    @Xss
    @Schema(description = "机构名称")
    @NotBlank(message = "机构名称不能为空！")
    private String name;

    @Schema(description = "机构级别")
    @NotNull(message = "机构级别不能为空！")
    private Integer organLevel;
}

package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import com.zjhh.comm.validator.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/4/7
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddUnitReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -9106400569451691888L;

    @Xss
    @Schema(description = "预算单位编码")
    @NotBlank(message = "预算单位编码不能为空！")
    private String code;

    @Schema(description = "上级编码")
    @NotBlank(message = "上级编码不能为空！")
    private String parentCode;

    @Xss
    @Schema(description = "预算单位名称")
    @NotBlank(message = "预算单位名称不能为空！")
    private String name;
}

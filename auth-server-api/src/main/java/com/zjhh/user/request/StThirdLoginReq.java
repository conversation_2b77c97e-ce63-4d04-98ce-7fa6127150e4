package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import com.zjhh.user.dao.entity.VOrganize;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/20 16:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StThirdLoginReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -1788790903880158086L;

    @NotNull(message = "appKey不能为空！")
    private String appKey;

    @NotNull(message = "sign不能为空！")
    private String sign;

    @NotNull(message = "token不能为空！")
    private String token;

    @NotNull(message = "timestamp不能为空！")
    private long timestamp;

    private List<String> userAuthAreaCodes;

    private VOrganize organize;
}

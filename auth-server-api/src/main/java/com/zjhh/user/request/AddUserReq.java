package com.zjhh.user.request;

import com.zjhh.comm.validator.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/6/12 09:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddUserReq extends LoginNameReq {

    @Serial
    private static final long serialVersionUID = -8705334072643072939L;

    @Xss
    @Schema(description = "姓名")
    @NotBlank(message = "用户名字不能为空！")
    @Length(max = 100, message = "用户名字过长！")
    private String username;

    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空！")
    private String password;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;

    @Xss
    @Schema(description = "用户描述")
    private String description;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "用户类型：1-系统管理员 2-普通用户")
    @NotNull(message = "是否管理员不能为空")
    private Integer userType;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "组织结构编码")
    @NotBlank(message = "组织结构编码不能为空！")
    private String organizeCode;

    /**
     * 角色列表
     */
    @Schema(description = "角色编码")
    private List<String> roleCodes;

    @Schema(description = "浙政钉ID")
    private String zzdAccountId;
}

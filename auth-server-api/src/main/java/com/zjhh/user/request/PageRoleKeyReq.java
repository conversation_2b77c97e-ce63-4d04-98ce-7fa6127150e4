package com.zjhh.user.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/4 19:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageRoleKeyReq extends PageRoleIdReq {

    @Serial
    private static final long serialVersionUID = 791739973339730323L;

    @Schema(description = "关键字检索")
    private String searchKey;

    @Schema(hidden = true)
    private String roleCode;

    @Schema(hidden = true)
    private List<String> organizeCodes;
}

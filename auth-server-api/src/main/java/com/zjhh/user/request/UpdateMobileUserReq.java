package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import com.zjhh.comm.validator.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/1/18 10:45
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateMobileUserReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -4345148295261450371L;

    @Schema(description = "移动端头像")
    private String avatarImg;

    @Xss
    @Schema(description = "姓名")
    private String username;
}

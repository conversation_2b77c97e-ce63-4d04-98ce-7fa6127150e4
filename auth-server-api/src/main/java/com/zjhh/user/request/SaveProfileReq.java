package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import com.zjhh.comm.validator.EmailOrNull;
import com.zjhh.comm.validator.PhoneOrNull;
import com.zjhh.comm.validator.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/18 10:30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SaveProfileReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 4083404929545302015L;

    @Xss
    @Schema(description = "姓名")
    @NotBlank(message = "用户名字不能为空！")
    @Length(max = 100, message = "用户名字过长！")
    private String username;

    @Schema(description = "手机号")
    @PhoneOrNull
    private String mobile;

    @Schema(description = "邮箱")
    @EmailOrNull
    private String email;

    @Xss
    @Schema(description = "描述")
    private String description;

    @Schema(description = "头像")
    private String avatar;
}

package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/4/7
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddAreaReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -8691335664084059381L;

    @Schema(description = "机构代码")
    @NotBlank(message = "机构代码不能为空！")
    @Length(max = 36, message = "机构编码太长！")
    private String code;

    @Schema(description = "上级机构代码")
    @NotBlank(message = "上级机构不能为空！")
    private String parentCode;

    @Schema(description = "机构名称")
    @NotBlank(message = "机构名称不能为空！")
    private String name;

    @Schema(description = "机构级别")
    @NotNull(message = "机构级别不能为空！")
    private Integer organLevel;
}

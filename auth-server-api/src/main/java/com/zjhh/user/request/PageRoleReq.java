package com.zjhh.user.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2020/6/17 15:30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageRoleReq extends PageReq {

    @Serial
    private static final long serialVersionUID = 8433444194706957652L;

    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "角色关键字")
    private String roleKey;
}

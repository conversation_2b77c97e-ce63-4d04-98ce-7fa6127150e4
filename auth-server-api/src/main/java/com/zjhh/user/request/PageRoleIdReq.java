package com.zjhh.user.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/11 10:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageRoleIdReq extends PageReq {

    @Serial
    private static final long serialVersionUID = 1124898066631299214L;

    @Schema
    @NotNull(message = "角色ID不能为空！")
    private String roleId;
}

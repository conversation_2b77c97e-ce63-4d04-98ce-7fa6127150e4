package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/1/20 10:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CodeReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 8064804025231591516L;

    @Schema(description = "code")
    @NotBlank(message = "code不能为空！")
    private String code;
}

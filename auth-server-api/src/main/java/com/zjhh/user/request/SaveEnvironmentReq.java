package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/15 18:44
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SaveEnvironmentReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1716652322087792875L;

    @Schema(name = "年度")
    @NotNull(message = "年度不能为空！")
    private Integer year;

    @Schema(name = "单位编码")
    @NotBlank(message = "单位编码不能为空！")
    private String unitCode;

    @Schema(name = "部门编码")
    @NotBlank(message = "部门编码不能为空！")
    private String deptCode;

    @Schema(name = "岗位编码")
    @NotBlank(message = "岗位编码不能为空！")
    private String postCode;
}

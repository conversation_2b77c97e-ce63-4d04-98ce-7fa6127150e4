package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import com.zjhh.comm.validator.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/11 14:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SaveUserThemeReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1482167610382900177L;

    @Xss
    @Schema(description = "主题配置")
    @NotBlank(message = "主题配置不能为空！")
    private String themeConfig;
}

package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2020/6/17 15:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleIdReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -7153481623089083442L;

    @Schema
    @NotNull(message = "roleId不能为空！")
    private String roleId;
}

package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/7/4 15:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FOALoginReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 5729261158687357372L;

    @Schema(description = "appCode")
    private String appCode;

    @Schema(description = "passport")
    private String passport;
}

package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 扫码登陆Req
 *
 * <AUTHOR>
 * @date 2021-05-26 3:55 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QrCodeLoginReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -3774695562916300909L;

    @Schema(description = "员工Code")
    private String code;
}

package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/12/14 11:23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SmartOpenLoginReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -4301130009914356558L;

    @Schema(description = "临时token")
    @NotBlank(message = "tempToken不能为空！")
    private String tempToken;
}

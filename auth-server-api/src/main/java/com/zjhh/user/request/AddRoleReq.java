package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import com.zjhh.comm.validator.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2020/6/15 10:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddRoleReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1397962786701327276L;

    @Xss
    @Schema(description = "角色名称")
    @NotBlank(message = "角色名称不能为空！")
    private String name;

    @Xss
    @Schema(description = "角色描述")
    private String description;
}

package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2020/6/12 11:55
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdatePasswordReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -4467806959987292327L;

    @NotBlank(message = "旧密码不能为空")
    private String oldPassword;

    @NotBlank(message = "新密码不能为空")
    private String newPassword;
}

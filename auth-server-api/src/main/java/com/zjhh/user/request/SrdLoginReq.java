package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/10/8 17:13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SrdLoginReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -7523410995056079775L;

    @NotBlank(message = "token不能为空！")
    private String accessToken;
}

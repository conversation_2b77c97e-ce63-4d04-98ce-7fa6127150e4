package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2020/6/17 15:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserIdReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -593763390304819197L;

    @Schema(description = "用户编码")
    @NotNull(message = "用户编码不能为空")
    private String userId;
}

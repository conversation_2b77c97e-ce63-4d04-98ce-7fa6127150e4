package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/4 10:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MenuIdReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 7240160547744381085L;

    @Schema
    private String menuId;
}

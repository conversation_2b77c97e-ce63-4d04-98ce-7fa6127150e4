package com.zjhh.user.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <AUTHOR>
 * @since 2020/6/11 17:13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LoginReq extends LoginNameReq {

    @Serial
    private static final long serialVersionUID = 8627483957596090847L;

    @Schema(description = "密码（MD5）")
    @NotBlank(message = "密码不能为空")
    private String password;

    private String captchaCode;

    private String imageKey;
}

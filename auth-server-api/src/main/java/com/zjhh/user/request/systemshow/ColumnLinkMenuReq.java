package com.zjhh.user.request.systemshow;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @since 2021/11/23 18:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ColumnLinkMenuReq extends BaseReq {

    private static final long serialVersionUID = 1448166375719568284L;

    @Schema(description = "菜单编码")
    @NotBlank(message = "菜单编码不能为空")
    private String menuCode;

    @Schema(description = "字段ID")
    @NotBlank(message = "字段ID不能为空")
    private String columnId;
}

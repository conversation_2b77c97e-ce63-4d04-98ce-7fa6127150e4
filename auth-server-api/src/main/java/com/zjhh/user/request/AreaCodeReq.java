package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/11/10 09:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AreaCodeReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -1161340520084214969L;

    @Schema(description = "行政区划编码")
    private String areaCode;
}

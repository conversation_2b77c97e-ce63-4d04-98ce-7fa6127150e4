package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/6/22 10:02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ComponentReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 8213348241959113323L;

    @Schema(description = "组件")
    private String component;
}

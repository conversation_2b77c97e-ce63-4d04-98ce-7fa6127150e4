package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import com.zjhh.comm.validator.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/2 17:37
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddMenuReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 5859363265018927155L;

    /**
     * 资源名称
     */
    @Xss
    @Schema(description = "资源名称")
    @NotBlank(message = "资源名称不能为空")
    private String name;

    /**
     * 权限类型：0：目录；1：菜单；2：按钮
     */
    @Schema(description = "菜单类型 0：目录；1：菜单；2：按钮")
    @NotNull(message = "权限类型不能为空")
    private Integer menuType;

    /**
     * 权限标识符，与前端的路由配置对应
     */
    @Xss
    @Schema(description = "权限标识符")
    private String router;

    /**
     * 父资源uuid
     */
    @Schema(description = "父菜单ID")
    private String parentId;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "跳转地址")
    private String redirect;

    @Schema(description = "排序")
    private String sort;

    @Xss
    @Schema(description = "权限")
    private String perm;

    @Schema(description = "是否外链")
    private Boolean externalLink;

    @Schema(description = "外链是否在内部显示")
    private Boolean externalInner;

    @Xss
    @Schema(description = "外部链接地址")
    private String page;

    @Schema(description = "是否在首页显示-移动端")
    private Boolean showHome;

    @Schema(description = "首页菜单排序-移动端")
    private Integer homeSort;

    @Schema(description = "menu图片url")
    private String menuImg;

    @Schema(description = "对应菜单")
    private String mappingMenu;
}

package com.zjhh.user.request.systemshow;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2023/12/8 15:06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetSystemShowReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 4438981878138533699L;

    @Schema(description = "菜单编码")
    private String menuCode;

}

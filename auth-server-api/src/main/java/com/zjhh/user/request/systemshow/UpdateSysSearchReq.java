package com.zjhh.user.request.systemshow;

import com.zjhh.user.request.MenuCodeReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/26 15:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateSysSearchReq extends MenuCodeReq {

    @Serial
    private static final long serialVersionUID = 7471193750960700834L;

    @Schema(description = "search列表")
    @NotNull(message = "search列表不能为空！")
    @Size(min = 1, message = "search列表不能为空！")
    private List<ChildSysSearch> searchList;

    @Data
    public static class ChildSysSearch implements Serializable {

        @Serial
        private static final long serialVersionUID = 8507314715408137859L;

        @Schema(description = "searchUuid")
        @NotBlank(message = "searchUuid不能为空！")
        private String searchUuid;

        @Schema(description = "搜索标识")
        @NotBlank(message = "搜索标识不能为空！")
        private String searchId;

        @Schema(description = "搜索名称")
        @NotBlank(message = "搜索名称不能为空！")
        private String searchName;

        @Schema(description = "默认值")
        private String defaultValue;

        @Schema(description = "1-全部显示 2-图形 3-表格 4-不显示")
        @NotBlank(message = "显示设置不能为空！")
        private Integer displaySet;

        @Schema(description = "排序")
        @NotNull(message = "排序不能为空！")
        private Integer sort;
    }
}

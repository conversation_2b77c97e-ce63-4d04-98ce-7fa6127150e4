package com.zjhh.user.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/2 14:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleUserReq extends ListUserIdReq {

    @Serial
    private static final long serialVersionUID = 6989390112635372453L;

    @Schema
    @NotNull(message = "角色ID不能为空！")
    private String roleId;
}

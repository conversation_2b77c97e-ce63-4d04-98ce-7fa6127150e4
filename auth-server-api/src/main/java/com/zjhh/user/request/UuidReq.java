package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/1/12 14:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UuidReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 304660245330265810L;

    @Schema(description = "uuid")
    @NotBlank(message = "uuid不能为空")
    private String uuid;

}

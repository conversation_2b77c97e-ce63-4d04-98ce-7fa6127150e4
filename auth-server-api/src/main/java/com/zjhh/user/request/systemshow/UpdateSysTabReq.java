package com.zjhh.user.request.systemshow;

import com.zjhh.user.request.MenuCodeReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/26 15:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateSysTabReq extends MenuCodeReq {

    @Serial
    private static final long serialVersionUID = 7471193750960700834L;

    @Schema(description = "tab列表")
    @NotNull(message = "tab列表不能为空！")
    @Size(min = 1, message = "tab列表不能为空！")
    private List<ChildSysTab> tabList;

    @Data
    public static class ChildSysTab implements Serializable {

        @Serial
        private static final long serialVersionUID = 8507314715408137859L;

        @Schema(description = "tabUuid")
        @NotBlank(message = "tabUuid不能为空！")
        private String tabUuid;

        @Schema(description = "tab标签标识")
        @NotBlank(message = "tab标签标识不能为空！")
        private String tabId;

        @Schema(description = "tab标签名称")
        @NotBlank(message = "tab标签名称不能为空！")
        private String tabName;

        @Schema(description = "页面说明")
        private String tagExplain;

        @Schema(description = "显示区域 1-图形 2-表格")
        @NotNull(message = "显示区域不能为空！")
        private Integer displayArea;

        @Schema(description = "是否显示")
        @NotNull(message = "是否显示不能为空！")
        private Boolean showed;

        @Schema(description = "排序")
        @NotNull(message = "排序不能为空！")
        private Integer sort;

        @Schema(description = "column列表")
        private List<ChildSysColumn> columns;

        @Schema(description = "选中的字段")
        private List<String> selectedColumnUuids;
    }

    @Data
    public static class ChildSysColumn implements Serializable {

        @Serial
        private static final long serialVersionUID = 8507314715408137859L;

        @Schema(description = "columnUuid")
        @NotBlank(message = "columnUuid不能为空！")
        private String columnUuid;

        @Schema(description = "字段标识")
        @NotBlank(message = "字段标识不能为空！")
        private String columnId;

        @Schema(description = "字段名称")
        @NotBlank(message = "字段名称不能为空！")
        private String columnName;

        @Schema(description = "链接的菜单编码")
        private String linkMenuCode;

        @Schema(description = "排序")
        @NotNull(message = "排序不能为空！")
        private Integer sort;

        @Schema(description = "子节点")
        private List<ChildSysColumn> children;

    }
}

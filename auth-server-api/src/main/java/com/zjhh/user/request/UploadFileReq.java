package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/1/18 18:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UploadFileReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1973491690440980183L;

    @Schema(description = "上传的文件")
    @NotNull(message = "上传的文件不能为空！")
    private MultipartFile file;

}

package com.zjhh.user.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/5/12 09:23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RouterReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1752364017803819350L;

    @Schema(description = "router")
    @NotBlank(message = "router不能为空！")
    private String router;
}

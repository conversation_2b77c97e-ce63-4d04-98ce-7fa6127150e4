package com.zjhh.user.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/3/15 14:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ResetPasswordReq extends UserIdReq {

    @Serial
    private static final long serialVersionUID = -5079299852489901671L;

    @Schema(description = "新密码")
    @NotNull(message = "新密码不能为空！")
    private String newPassword;
}

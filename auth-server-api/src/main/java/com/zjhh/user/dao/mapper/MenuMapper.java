package com.zjhh.user.dao.mapper;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.user.dao.entity.Menu;
import com.zjhh.user.dto.MenuDto;
import com.zjhh.user.vo.TreeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface MenuMapper extends CustomerBaseMapper<Menu> {

    /**
     * 获取最大的子菜单
     *
     * @return
     */
    int findMaxChildMenu();

    List<TreeVo> findMenuTree();

    List<String> listButton(@Param("menuCode") String menuCode, @Param("userCode") String userCode);

    List<TreeVo> findDescMenuTree();

    /**
     * 获取该组件的第一个菜单
     *
     * @param component
     * @return
     */
    String findOneMenuByComponent(@Param("component") String component);

    /**
     * 获取映射的菜单
     *
     * @return
     */
    List<TreeSelectVo> listSelectMapMenu();

    List<MenuDto> listAllChildMenu(@Param("menuCode") String menuCode);

    /**
     * 获取系统显示配置列跳转菜单
     *
     * @return
     */
    List<TreeVo> findJumpMenuTree();
}

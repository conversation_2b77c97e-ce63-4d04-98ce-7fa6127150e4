package com.zjhh.user.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.user.dao.entity.SetButton;
import com.zjhh.user.vo.systemshow.SysButtonVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
public interface SetButtonMapper extends CustomerBaseMapper<SetButton> {

    List<SysButtonVo> listSysButton(@Param("menuCode") String menuCode, @Param("component") String component);
}

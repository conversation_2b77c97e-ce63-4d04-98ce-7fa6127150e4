package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户数据权限
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_user_data_auth")
public class UserDataAuth implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 行政区划编码
     */
    private String areaCode;

    /**
     * 用户编码
     */
    private String userCode;

    private Integer version;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtUpdate;


}

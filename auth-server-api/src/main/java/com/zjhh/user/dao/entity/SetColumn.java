package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zjhh.db.dao.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_set_column")
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SetColumn extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    private String uuid;

    private String columnUuid;

    private String tabUuid;

    private String columnName;

    private String menuCode;

    private Boolean showed;

    private Integer sort;

    private String parentUuid;

    private String linkMenuCode;
}

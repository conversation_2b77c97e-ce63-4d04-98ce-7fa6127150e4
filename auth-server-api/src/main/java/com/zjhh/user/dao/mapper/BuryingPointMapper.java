package com.zjhh.user.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.user.dao.entity.BuryingPoint;
import com.zjhh.user.request.PageBuryingPointReq;
import com.zjhh.user.vo.BuryingPointVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * <p>
 * 埋点 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-02
 */
public interface BuryingPointMapper extends CustomerBaseMapper<BuryingPoint> {

    /**
     * 获取该用户最后打开的界面
     *
     * @param userCode
     * @param menuCode
     * @param curToken
     * @param ip
     * @param time
     * @return
     */
    BuryingPoint findLastOpen(@Param("userCode") String userCode, @Param("menuCode") String menuCode,
                              @Param("curToken") String curToken, @Param("ip") String ip, @Param("time") LocalDateTime time);

    /**
     * 埋点信息分页
     *
     * @param req
     * @return
     */
    Page<BuryingPointVo> page(Page<BuryingPointVo> page, @Param("req") PageBuryingPointReq req);
}

package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zjhh.db.dao.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <p>
 * 用户个性化表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user_config")
public class UserConfig extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    private String userCode;

    /**
     * 关键字
     */
    private String code;

    /**
     * 对应的值
     */
    private String value;

    public UserConfig() {

    }

    public UserConfig(String userCode, String code, String value) {
        this.userCode = userCode;
        this.code = code;
        this.value = value;
    }
}

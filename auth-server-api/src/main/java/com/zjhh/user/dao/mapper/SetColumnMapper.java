package com.zjhh.user.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.user.dao.entity.SetColumn;
import com.zjhh.user.dto.SelectedColumnDto;
import com.zjhh.user.dto.SysColumnDto;
import com.zjhh.user.request.systemshow.ColumnLinkMenuReq;
import com.zjhh.user.vo.SysColumnVo;
import com.zjhh.user.vo.systemshow.GetSystemColumnVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
public interface SetColumnMapper extends CustomerBaseMapper<SetColumn> {

    List<SysColumnVo> listSysColumn(@Param("menuCode") String menuCode, @Param("component") String component, @Param("tabUuids") List<String> tabUuids);

    List<SysColumnDto> listSysShowColumn(@Param("menuCode") String menuCode, @Param("component") String component);

    List<SelectedColumnDto> listSelectedLeftColumn(@Param("menuCode") String menuCode, @Param("component") String component, @Param("tabUuids") List<String> tabUuids);

    String findLinkMenuCode(@Param("req") ColumnLinkMenuReq req);

    /**
     * 获取字段信息
     *
     * @param component
     * @param menuCode
     * @param tabUuid
     * @return
     */
    List<GetSystemColumnVo> getSystemColumn(@Param("component") String component, @Param("menuCode") String menuCode, @Param("tabId") String tabId);
}

package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zjhh.db.dao.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <p>
 * 组织结构用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_organize_user")
public class OrganizeUser extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 单位编码或者行政区划编码
     */
    private String organizeCode;

    /**
     * 用户编码
     */
    private String userCode;


}

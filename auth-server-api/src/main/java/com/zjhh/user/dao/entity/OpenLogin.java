package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 通用对外登录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_open_login")
public class OpenLogin implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String appKey;

    private String appSecret;

    /**
     * 用户编码
     */
    private String userCode;

    private Integer version;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtUpdate;


}

package com.zjhh.user.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.user.dao.entity.Role;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-17
 */
public interface RoleMapper extends CustomerBaseMapper<Role> {

    /**
     * 获取没有分配该角色的用户列表
     *
     * @param roleCode
     * @param userIds
     * @return
     */
    List<String> listUser(@Param("roleCode") String roleCode, @Param("userIds") List<String> userIds);

    /**
     * 获取用户角色
     *
     * @param userCode
     * @return
     */
    List<Role> listUserRole(@Param("userCode") String userCode);
}

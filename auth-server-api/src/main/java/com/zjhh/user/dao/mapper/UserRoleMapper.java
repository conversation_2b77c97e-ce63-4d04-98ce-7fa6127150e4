package com.zjhh.user.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.user.dao.entity.UserRole;
import com.zjhh.user.request.PageRoleKeyReq;
import com.zjhh.user.vo.RoleVo;
import com.zjhh.user.vo.UserVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-17
 */
public interface UserRoleMapper extends CustomerBaseMapper<UserRole> {

    /**
     * 获取用户角色
     *
     * @param userCode
     * @return
     */
    List<RoleVo> listRole(@Param("userCode") String userCode);

    /**
     * 获取角色对应的用户
     *
     * @param req
     * @return
     */
    Page<UserVo> pageRoleUser(Page<UserVo> page, @Param("req") PageRoleKeyReq req);

    /**
     * 获取角色编码
     *
     * @param userCode
     * @return
     */
    List<String> listRoleKeys(@Param("userCode") String userCode);

}

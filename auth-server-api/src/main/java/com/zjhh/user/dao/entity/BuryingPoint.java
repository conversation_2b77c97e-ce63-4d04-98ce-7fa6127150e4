package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 埋点
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_burying_point")
public class BuryingPoint implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 打开时间
     */
    private LocalDateTime openTime;

    /**
     * 关闭时间
     */
    private LocalDateTime closeTime;

    private Integer version;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtUpdate;

    private String curToken;

    private String ip;
}

package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.db.dao.entity.BaseEntity;
import com.zjhh.user.enume.UserTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class User extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "编码")
    private String code;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String username;

    /**
     * 登录名
     */
    @Schema(description = "登录名")
    private String loginName;

    /**
     * 登录密码
     */
    @Schema(hidden = true)
    @JsonIgnore
    private String password;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;

    /**
     * 是否锁定
     */
    @Schema(description = "是否冻结")
    private Boolean locked;

    /**
     * 加密盐
     */
    @Schema(hidden = true)
    @JsonIgnore
    private String salt;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 用户类型：0-超级管理员 1-系统管理员 2-普通用户
     */
    @Schema(description = "用户类型：0-超级管理员 1-系统管理员 2-普通用户")
    private Integer userType;

    @Schema(description = "是否新用户")
    private Boolean newUser;

    @Schema(description = "创建用户")
    private String createUser;

    /**
     * 头像
     */
    @Schema(description = "头像")
    private String avatar;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "是否同步政务钉钉")
    private Boolean syncZwdd;

    private String zzdAccountId;

    @Schema(description = "移动端头像")
    private String mobileAvatar;

    /**
     * 是否超级管理员
     *
     * @return
     */
    public Boolean isSuperUser() {
        return UserTypeEnum.isSuperUser(this.userType);
    }
}

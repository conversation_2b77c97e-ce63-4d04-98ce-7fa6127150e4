package com.zjhh.user.dao.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjhh.db.dao.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <p>
 * 单位表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_unit")
public class Unit extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 单位编码
     */
    @Schema(description = "单位编码")
    @ExcelProperty(value = "预算单位编码", index = 0)
    private String code;

    /**
     * 单位名称
     */
    @Schema(description = "单位名称")
    @ExcelProperty(value = "预算单位名称", index = 1)
    private String name;

    /**
     * 父编码
     */
    @Schema(description = "上级预算单位编码")
    @ExcelIgnore
    private String parentCode;

    /**
     * 上级预算单位名称
     */
    @Schema(description = "上级预算单位名称")
    @ExcelProperty(value = "上级机构名称", index = 2)
    private String parentName;

    /**
     * 行政区划编码
     */
    @Schema(description = "行政区划编码")
    @ExcelIgnore
    private String areaCode;

    /**
     * 行政区划名称
     */
    @Schema(description = "行政区划名称")
    @ExcelProperty(value = "所属行政区划", index = 3)
    private String areaName;

    @Schema(description = "创建用户")
    @ExcelIgnore
    private String createUser;

}

package com.zjhh.user.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.user.dao.entity.SetTab;
import com.zjhh.user.dto.SysBtnDto;
import com.zjhh.user.dto.SysSearchDto;
import com.zjhh.user.dto.SysTabDto;
import com.zjhh.user.vo.systemshow.SysTabVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 页签设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
public interface SetTabMapper extends CustomerBaseMapper<SetTab> {

    List<SysTabVo> listSysTab(@Param("menuCode") String menuCode, @Param("component") String component);

    List<SysTabDto> findCharsTab(@Param("menuCode") String menuCode, @Param("component") String component);

    List<SysTabDto> findTableTab(@Param("menuCode") String menuCode, @Param("component") String component);

    List<SysBtnDto> findButtons(@Param("menuCode") String menuCode, @Param("component") String component);

    List<SysSearchDto> findSearches(@Param("menuCode") String menuCode, @Param("component") String component);

    List<SysTabDto> findAllTab(@Param("menuCode") String menuCode, @Param("component") String component);
}

package com.zjhh.user.dao.mapper;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.user.dao.entity.VOrganize;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * <p>
 * VIEW Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-08
 */
public interface VOrganizeMapper extends CustomerBaseMapper<VOrganize> {

    List<TreeSelectVo> treeOrganize();

    /**
     * 数据权限-组织结构和用户树形结构
     *
     * @return
     */
    List<TreeSelectVo> treeOrganizeUser();

    List<String> listAllChild(@Param("organizeCode") String organizeCode);
}

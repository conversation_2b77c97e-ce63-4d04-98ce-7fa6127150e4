package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zjhh.db.dao.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * <p>
 * 页签设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_set_tab")
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SetTab extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    private String uuid;

    private String tabUuid;

    private String tabName;

    /**
     * 菜单code
     */
    private String menuCode;

    /**
     * 页面说明
     */
    private String tagExplain;

    /**
     * 是否显示
     */
    private Boolean showed;

    private Integer sort;

}

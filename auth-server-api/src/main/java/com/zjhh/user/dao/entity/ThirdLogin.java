package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zjhh.db.dao.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_third_login")
public class ThirdLogin extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 行政区划编码
     */
    private String areaCode;

    private String appKey;

    private String appSecret;

    private String url;

}

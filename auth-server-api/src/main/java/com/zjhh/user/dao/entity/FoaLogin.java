package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 省厅OA单点登录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_foa_login")
public class FoaLogin implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String oaUserName;

    private String userUuid;

    private Integer version;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtUpdate;


}

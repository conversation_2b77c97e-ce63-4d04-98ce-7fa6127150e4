package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zjhh.db.dao.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_set_button")
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SetButton extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    private String uuid;

    private String buttonUuid;

    private String buttonName;

    private String menuCode;

    /**
     * 0-全部显示 1-图形 2-表格 3-不显示
     */
    private Integer displaySet;

    private Integer sort;

}

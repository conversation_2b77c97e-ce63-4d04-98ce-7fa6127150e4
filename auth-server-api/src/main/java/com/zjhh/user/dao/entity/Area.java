package com.zjhh.user.dao.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjhh.db.dao.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <p>
 * 行政区划表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_area")
public class Area extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 编码
     */
    @Schema(description = "编码")
    @ExcelProperty(value = "机构代码", index = 0)
    private String code;

    /**
     * 名称
     */
    @Schema(description = "名称")
    @ExcelProperty(value = "机构名称", index = 1)
    private String name;

    /**
     * 父编码
     */
    @Schema(description = "上级机构编码")
    @ExcelIgnore
    private String parentCode;

    /**
     * 上级机构名称
     */
    @Schema(description = "上级机构名称")
    @ExcelProperty(value = "上级机构", index = 2)
    private String parentName;

    /**
     * 机构级别 1-中央级、2-省级、3-地市级、4-县级、5-乡(镇)
     */
    @Schema(description = "1-中央级；2-省级；3-计划单列市；4-市级；5-县（区）级；51-县级；52-区级；6-乡级；61-乡镇级；62-街道级（其中51、52为5的下级，61、62为6的下级，只可选择末级）")
    @ExcelIgnore
    private Integer organLevel;

    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    @ExcelIgnore
    private String createUser;

    @TableField(exist = false)
    @ExcelProperty(value = "机构级别", index = 3)
    private String organLevelName;

    public String getOrganLevelName() {
        switch (organLevel) {
            case 1:
                return "中央级";
            case 2:
                return "省级";
            case 3:
                return "计划单列市";
            case 4:
                return "市级";
            case 51:
                return "县级";
            case 52:
                return "区级";
            case 61:
                return "乡镇级";
            case 62:
                return "街道级";
            default:
                return null;
        }
    }
}

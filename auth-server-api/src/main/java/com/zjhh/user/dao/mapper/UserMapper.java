package com.zjhh.user.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.user.dao.entity.User;
import com.zjhh.user.request.PageRoleKeyReq;
import com.zjhh.user.request.PageUserReq;
import com.zjhh.user.vo.RoleVo;
import com.zjhh.user.vo.UserVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-19
 */
public interface UserMapper extends CustomerBaseMapper<User> {

    /**
     * 获取用户列表
     *
     * @param page
     * @param req
     * @return
     */
    Page<UserVo> selectPageUserVo(@Param("page") Page<UserVo> page, @Param("req") PageUserReq req);

    /**
     * 获取角色列表
     *
     * @param userCodes
     * @return
     */
    List<RoleVo> listRoleVo(@Param("userCodes") List<String> userCodes);

    /**
     * 获取用户列表
     *
     * @param page
     * @param req
     * @return
     */
    Page<User> selectPageRoleUserVo(@Param("page") Page<User> page, @Param("req") PageRoleKeyReq req);

    /**
     * 查询某个组织结构下系统管理员数量
     *
     * @param organizeCode
     * @return
     */
    int countOrganizeSystemManager(@Param("organizeCode") String organizeCode);

    /**
     * 获取所有用户权限
     *
     * @param userCode
     * @return
     */
    List<String> listAllPermission(@Param("userCode") String userCode);
}

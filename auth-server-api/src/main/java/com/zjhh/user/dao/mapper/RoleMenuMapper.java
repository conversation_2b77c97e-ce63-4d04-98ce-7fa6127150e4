package com.zjhh.user.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.user.dao.entity.Menu;
import com.zjhh.user.dao.entity.RoleMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 角色菜单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
public interface RoleMenuMapper extends CustomerBaseMapper<RoleMenu> {

    /**
     * 获取选中的菜单
     *
     * @param roleCode
     * @return
     */
    List<Menu> listCheckedMenu(@Param("roleCode") String roleCode);

    /**
     * 获取用户是否有该菜单权限
     *
     * @param userCode
     * @param menuCode
     * @return
     */
    int countUserMenu(@Param("userCode") String userCode, @Param("menuCode") String menuCode);
}

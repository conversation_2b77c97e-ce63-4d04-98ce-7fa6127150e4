package com.zjhh.user.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.user.dao.entity.Area;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 行政区划表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-08
 */
public interface AreaMapper extends CustomerBaseMapper<Area> {

    /**
     * 修改父级名称
     *
     * @param parentCode
     * @return
     */
    int updateParentName(@Param("parentName") String parentName, @Param("parentCode") String parentCode);

    List<Area> listLeafAreas(@Param("areaCodes") Collection<String> areaCodes);

    /**
     * 获取组织结构所有子节点
     *
     * @param areaCode
     * @return
     */
    List<String> getAreaChild(@Param("areaCode") String areaCode);

    /**
     * 获取组织结构的父节点
     *
     * @param areaCodes
     * @return
     */
    Set<String> getAreaParent(@Param("areaCodes") List<String> areaCodes);
}

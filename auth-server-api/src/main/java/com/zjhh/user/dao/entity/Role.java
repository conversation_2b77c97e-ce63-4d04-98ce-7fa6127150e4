package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zjhh.db.dao.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <p>
 * 角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_role")
public class Role extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    private String code;

    private String roleKey;

    private String name;

    private String description;

    private String createUser;
}

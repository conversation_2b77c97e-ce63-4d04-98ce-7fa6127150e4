package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 政务钉钉关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-31
 */
@Data
@TableName("sys_user_relation")
public class ZwddUserRelation implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String id;

    private String userCode;

    private String username;

    private String mobile;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtUpdate;

    private String employeeCode;

    private Long accountId;

}

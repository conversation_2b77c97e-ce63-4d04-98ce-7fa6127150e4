package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 移动端默认页面设置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_mobile_default_set")
public class MobileDefaultSet implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String code;

    /**
     * 配置
     */
    private String value;

    private String description;


}

package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zjhh.db.dao.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <p>
 * 前端组件表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_component")
public class Component extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    private String code;

    private String title;

    private String component;

    private String parentCode;

    private String perm;

    private Integer sort;
}

package com.zjhh.user.dao.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjhh.db.dao.entity.BaseEntity;
import lombok.Data;

import java.io.Serial;

/**
 * <p>
 * 菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Data
@TableName("sys_menu")
public class Menu extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    private String code;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单对用前端路由
     */
    private String router;

    /**
     * 菜单类型 0-目录 1-菜单 2-按钮
     */
    private Integer menuType;

    /**
     * 父节点菜单code
     */
    private String parentCode;

    /**
     * 菜单分类 0：非菜单 1：一级菜单 2：二级菜单 3：三级菜单 以此类推
     */
    private Integer childMenu;

    /**
     * 访问l路径
     */
    private String path;

    /**
     * 默认的跳转地址
     */
    private String redirect;

    /**
     * 图表
     */
    private String icon;

    /**
     * 排序
     */
    private String sort;

    private String perm;

    private Boolean externalLink;

    private Boolean externalInner;

    private Boolean showHome;

    private Integer homeSort;

    private String menuImg;

    /**
     * 外部路由链接地址
     */
    private String page;

    /**
     * 对应菜单
     */
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private String mappingMenu;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        Menu menu = (Menu) o;
        return menu.getId().equals(this.getId());
    }

    @Override
    public int hashCode() {
        return this.getId().hashCode();
    }
}

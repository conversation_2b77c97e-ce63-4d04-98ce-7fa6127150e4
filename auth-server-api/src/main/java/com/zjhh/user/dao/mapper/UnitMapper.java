package com.zjhh.user.dao.mapper;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.user.dao.entity.Unit;
import com.zjhh.user.vo.MenuTreeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 单位表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-08
 */
public interface UnitMapper extends CustomerBaseMapper<Unit> {

    /**
     * 修改父级名称
     *
     * @param parentName
     * @param parentCode
     * @return
     */
    int updateParentName(@Param("parentName") String parentName, @Param("parentCode") String parentCode);

    /**
     * 修改行政区划名称
     *
     * @param areaName
     * @param areaCode
     * @return
     */
    int updateAreaName(@Param("areaName") String areaName, @Param("areaCode") String areaCode);


    List<MenuTreeVo> listUnit(@Param("admDivCode") String admDivCode);

    /**
     * 获取预算单位选择
     *
     * @return
     */
    List<TreeSelectVo> listUnitSelect();
}

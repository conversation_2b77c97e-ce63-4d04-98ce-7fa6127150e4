package com.zjhh.user.enume;

/**
 * <AUTHOR>
 * @since 2023/11/23 11:27
 */
public enum DefaultMenuEnum {

    PERSONAL("personal", "个人页"),

    PROFILE("profile", "个人中心"),

    LICENCE("licence", "密钥管理"),

    UPLOAD_LICENSE("uploadlicense", "上传licence"),

    SECRET_KEY("secretkey", "密钥生成");

    private final String menuCode;

    private final String menuName;

    DefaultMenuEnum(String menuCode, String menuName) {
        this.menuCode = menuCode;
        this.menuName = menuName;
    }

    public static String getNameByCode(String menuCode) {
        switch (menuCode) {
            case "personal":
                return PERSONAL.menuName;
            case "profile":
                return PROFILE.menuName;
            case "licence":
                return LICENCE.menuName;
            case "uploadlicense":
                return UPLOAD_LICENSE.menuName;
            case "secretkey":
                return SECRET_KEY.menuName;
            default:
                return null;
        }
    }
}

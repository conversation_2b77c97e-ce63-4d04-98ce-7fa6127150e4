package com.zjhh.user.enume;

/**
 * <AUTHOR>
 * @since 2021/4/8
 */
public enum UserTypeEnum {

    /**
     * 超级管理员
     */
    SUPER_MANAGER(0),

    /**
     * 系统管理员
     */
    SYSTEM_MANAGER(1),

    /**
     * 普通用户
     */
    COMMON_USER(2);

    private final int value;

    UserTypeEnum(int value) {
        this.value = value;
    }

    public static boolean isSuperUser(Integer userType) {
        return userType == SUPER_MANAGER.value;
    }

    public int value() {
        return this.value;
    }
}

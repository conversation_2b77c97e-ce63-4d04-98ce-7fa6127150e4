package com.zjhh.user.enume;

/**
 * <AUTHOR>
 * @since 2023/10/19 09:33
 */
public enum AreaGradeEnum {

    //1-中央级；2-省级；3-计划单列市；4-市级；5-县（区）级；51-县级；52-区级；6-乡级；61-乡镇级；62-街道级（其中51、52为5的下级，61、62为6的下级，只可选择末级）
    CENTRAL(1),

    PROVINCE(2),

    PLAN_CITY(3),

    CITY(4),

    DISTRICT(5),

    VILLAGE(6);


    private final int value;

    AreaGradeEnum(int value) {
        this.value = value;
    }

    public int value() {
        return this.value;
    }

    public static AreaGradeEnum getByValue(int value) {
        if (value > 10) {
            value = value / 10;
        }
        switch (value) {
            case 1:
                return CENTRAL;
            case 2:
                return PROVINCE;
            case 3:
                return PLAN_CITY;
            case 4:
                return CITY;
            case 5:
                return DISTRICT;
            case 6:
                return VILLAGE;
            default:
                return null;
        }
    }

}

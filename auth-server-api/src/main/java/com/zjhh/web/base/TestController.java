package com.zjhh.web.base;

import com.zjhh.comm.response.ReData;
import com.zjhh.user.service.ZwddApiService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import lombok.Data;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-05-31 9:58 上午
 */
@RestController
public class TestController {

    @Resource
    private ZwddApiService zwddApiService;


   @Operation(summary = "Test")
    @GetMapping("test")
    public ReData login() throws Exception {
        return ReData.success(zwddApiService.fullSyncDeptAndUser());
    }

   @Operation(summary = "Test1")
    @PostMapping("sync/mobile")
    public ReData syncMobile(@RequestBody MobileBody mobileBody) throws Exception {
        return ReData.success(zwddApiService.fullSyncUserByMobileList(mobileBody.getMobileList()));
    }


}

@Data
class MobileBody {
    List<String> mobileList;

}

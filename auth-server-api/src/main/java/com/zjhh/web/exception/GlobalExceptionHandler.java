package com.zjhh.web.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import com.zjhh.comm.constant.CommConstants;
import com.zjhh.comm.exception.BaseException;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.exception.NeedLoginException;
import com.zjhh.comm.response.ReData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 统一异常处理
 *
 * <AUTHOR>
 * @since 2020/6/11 17:33
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(BizException.class)
    public ReData<String> handleBizException(BizException e) {
        return ReData.error(e);
    }

    @ExceptionHandler(NeedLoginException.class)
    public ReData<String> handleNeedLoginException(NeedLoginException e) {
        return ReData.error(e);
    }

    @ExceptionHandler(NotLoginException.class)
    public ReData<String> handleNotLoginException() {
        return ReData.error(new NeedLoginException());
    }

    @ExceptionHandler(NotRoleException.class)
    public ReData<String> handleNotRoleException() {
        return ReData.error("没有操作权限！");
    }

    @ExceptionHandler(NotPermissionException.class)
    public ReData<String> handleNotPermissionException() {
        return ReData.error("没有操作权限！");
    }

    @ExceptionHandler(BaseException.class)
    public ReData<String> handleBaseException(BaseException e) {
        return ReData.error(e);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ReData<String> handleParamException(MethodArgumentNotValidException e) {
        String message = "参数校验错误";
        if (e.getBindingResult().getFieldError() != null) {
            message = e.getBindingResult().getFieldError().getDefaultMessage();
        }
        return ReData.error(CommConstants.CODE_ARGUMENT_NOT_VALID, message);
    }

    @ExceptionHandler(RuntimeException.class)
    public ReData<String> handleRuntimeException(RuntimeException e) {
        log.error("系统错误:", e);
        return ReData.error(CommConstants.CODE_SYSTEM_ERROR, "出错了，请联系管理员");
    }

}

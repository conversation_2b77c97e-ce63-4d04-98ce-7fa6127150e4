package com.zjhh.web.config;

import cn.hutool.core.util.StrUtil;
import com.zjhh.comm.utils.ThreadLocalUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * <AUTHOR>
 * @since 2022/12/20 17:18
 */
@Slf4j
public class HeaderInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String menuCode = request.getHeader("menu-code");
        if (StrUtil.isNotBlank(menuCode)) {
            ThreadLocalUtil.MENU_CODE.set(menuCode);
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ThreadLocalUtil.MENU_CODE.remove();
        ThreadLocalUtil.TASK_AUTH.remove();
    }
}

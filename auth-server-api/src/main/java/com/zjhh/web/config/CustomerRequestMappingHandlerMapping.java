package com.zjhh.web.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @since 2020/6/15 16:08
 */
@Slf4j
public class CustomerRequestMappingHandlerMapping extends RequestMappingHandlerMapping {

    private final boolean logMappingPath;

    public CustomerRequestMappingHandlerMapping(boolean logMappingPath) {
        this.logMappingPath = logMappingPath;
    }


    @Override
    protected RequestMappingInfo getMappingForMethod(@NonNull Method method, @NonNull Class<?> handlerType) {
        RequestMappingInfo info = super.getMappingForMethod(method, handlerType);
        if (info == null) {
            return null;
        }
        RequestMapping requestMapping = handlerType.getAnnotation(RequestMapping.class);
        if (requestMapping != null) {
            Class<?> superClass = handlerType.getSuperclass();
            info = appendParentRequestMapping(superClass, info);
        }
        logMapping(info);
        return info;
    }

    protected RequestMappingInfo appendParentRequestMapping(Class<?> handlerType, RequestMappingInfo info) {
        if (handlerType == null) {
            return info;
        }
        RequestMapping parentRequestMapping = handlerType.getAnnotation(RequestMapping.class);
        if (parentRequestMapping != null && parentRequestMapping.value().length > 0) {
            //使用path工具向前追加父类的path
            info = RequestMappingInfo.paths(parentRequestMapping.value()).build().combine(info);
        }
        return appendParentRequestMapping(handlerType.getSuperclass(), info);
    }

    private void logMapping(RequestMappingInfo info) {
        if (!logMappingPath || info == null) {
            return;
        }
        log.info("mapping path:" + info.toString());
    }
}

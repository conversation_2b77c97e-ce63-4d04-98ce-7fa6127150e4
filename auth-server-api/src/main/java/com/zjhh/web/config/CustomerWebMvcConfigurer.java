package com.zjhh.web.config;

import com.zjhh.comm.constant.CommConstants;
import com.zjhh.system.service.DictService;
import jakarta.annotation.Resource;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @since 2021/4/25 14:55
 */
@Configuration
public class CustomerWebMvcConfigurer implements WebMvcConfigurer {

    @Resource
    private DictService dictService;

    @Resource
    private ApplicationContext applicationContext;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        String path = dictService.getFileUploadPath();
        registry.addResourceHandler("/" + CommConstants.FILE_URL + "/**").addResourceLocations("file:" + path);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HeaderInterceptor()).addPathPatterns("/**");
    }

//    @Override
//    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
//        Jackson2ObjectMapperBuilder json = Jackson2ObjectMapperBuilder.json();
//        json.applicationContext(this.applicationContext);
//        json.serializationInclusion(JsonInclude.Include.NON_NULL);
//        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter(json.build());
//        ObjectMapper objectMapper = converter.getObjectMapper();
//        objectMapper.registerModule(new CustomerSerializerModule());
//        converters.add(0, converter);
//    }
}

package com.zjhh.web.aop;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.hutool.core.util.IdUtil;
import com.zjhh.comm.exception.BaseException;
import com.zjhh.comm.exception.NeedLoginException;
import com.zjhh.comm.utils.IpUtil;
import com.zjhh.system.annotation.ApiLog;
import com.zjhh.system.dao.entity.LogApi;
import com.zjhh.system.enume.LogApiTypeEnum;
import com.zjhh.system.service.LogService;
import com.zjhh.user.service.impl.UserSession;
import com.zjhh.user.vo.LoginVo;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @since 2020/6/19 11:18
 */
@Aspect
@Slf4j
@Component
public class ApiLogAspect {

    @Resource
    private LogService logService;

    @Resource
    private UserSession userSession;

    @Pointcut("@annotation(com.zjhh.system.annotation.ApiLog)")
    public void logPointCut() {

    }

    @Around("logPointCut()")
    public Object saveApiLog(ProceedingJoinPoint point) {
        LogApi logApi = new LogApi();
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        ApiLog apiLog = method.getAnnotation(ApiLog.class);
        Object result;
        try {
            if (apiLog == null) {
                return point.proceed();
            }
            LoginVo loginVo;
            long time;
            if (apiLog.type() == LogApiTypeEnum.LOGIN) {
                long start = System.currentTimeMillis();
                result = point.proceed();
                time = System.currentTimeMillis() - start;
                loginVo = userSession.getSessionLoginVo();
            } else {
                loginVo = userSession.getSessionLoginVo();
                long start = System.currentTimeMillis();
                result = point.proceed();
                time = System.currentTimeMillis() - start;
            }
            logApi.setCode(IdUtil.simpleUUID());
            logApi.setMethod(method.getName());
            logApi.setLogType(apiLog.type().value());
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            logApi.setUrl(request.getRequestURI());
            logApi.setIp(IpUtil.getIp(request));
            logApi.setTotalTime(time);
            logApi.setUserCode(loginVo.getCode());
            logApi.setLoginName(loginVo.getLoginName());
            logApi.setUserName(loginVo.getUsername());
            logApi.setOperator(apiLog.value());
            logService.log(logApi);
        } catch (Throwable e) {
            if (e instanceof BaseException) {
                throw (BaseException) e;
            } else if (e instanceof NotLoginException) {
                throw new NeedLoginException();
            } else if (e instanceof NotRoleException) {
                throw new NotRoleException("无操作权限");
            } else {
                log.error("记录api日志失败", e);
                throw new RuntimeException(e);
            }
        }
        return result;
    }


}

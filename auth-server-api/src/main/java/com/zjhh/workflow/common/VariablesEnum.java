package com.zjhh.workflow.common;

/**
 * <AUTHOR>
 */

public enum VariablesEnum {

    /**
     * 提交人
     */
    submitter,

    /**
     * 审批节点1
     */
    auditNode1,

    /**
     * 审批节点2
     */
    auditNode2,

    /**
     * 审批节点3
     */
    auditNode3,

    /**
     * 审批节点4
     */
    auditNode4,

    /**
     * 审批节点5
     */
    auditNode5,

    /**
     * 审批节点6
     */
    auditNode6,

    /**
     * 审批节点7
     */
    auditNode7,

    /**
     * 审批节点8
     */
    auditNode8,

    /**
     * 审批节点9
     */
    auditNode9,

    /**
     * 审批节点10
     */
    auditNode10,

    /**
     * 审批节点11
     */
    auditNode11,

    /**
     * 审批操作
     */
    approveOpr,

    /**
     * 员工编号
     */
    userId,

    /**
     * 活动主题
     */
    activityName,

    /**
     * 活动类型
     */
    activityType,

    /**
     * 组织机构
     */
    orgCode,

    /**
     * 组织机构名称
     */
    orgName,

    /**
     * 业务主键ID
     */
    businessKey,

    /**
     * 提报类型  费用明细   费用核销
     */
    reportType,

    /**
     * 提报类型  活动立项  活动冲抵
     */
    reportType1,

    /**
     * 渠道申请类型
     */
    ChannelApplyType,

    /**
     * 渠道名称
     */
    channelName,

    /**
     * 渠道编码
     */
    channelCode,

    /**
     * 渠道城市
     */
    city,

    /**
     * 审批状态
     */
    auditStatus,

    start,

    limit,

    /**
     * 退回标识  YES
     */
    toBack;

}

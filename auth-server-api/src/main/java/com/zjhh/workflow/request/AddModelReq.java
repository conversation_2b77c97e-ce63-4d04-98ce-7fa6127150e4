package com.zjhh.workflow.request;

import com.zjhh.comm.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.multipart.MultipartFile;

/**
 * 添加模板Req
 *
 * <AUTHOR>
 * @date 2022-01-18 3:01 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddModelReq extends BaseReq {
    private static final long serialVersionUID = 6496544404651604031L;

    private String processId;

    private String processName;

    private MultipartFile file;

}

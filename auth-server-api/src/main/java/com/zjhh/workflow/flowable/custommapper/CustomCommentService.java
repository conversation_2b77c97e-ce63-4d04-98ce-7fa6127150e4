package com.zjhh.workflow.flowable.custommapper;

import org.flowable.common.engine.impl.cmd.CustomSqlExecution;
import org.flowable.engine.ManagementService;
import org.flowable.engine.impl.cmd.AbstractCustomSqlExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> bruce.liu
 * @title: : CustomCommentService
 * @projectName : flowable
 * @description: 自定义sql
 * @date : 2019/12/239:54
 */
@Service
public class CustomCommentService {

    @Autowired
    private ManagementService managementService;

    public List<Map<String, Object>> getCommentsByProcInstId(String procInstId) {
        CustomSqlExecution<CustomCommentMapper, List<Map<String, Object>>> customSqlExecution = new AbstractCustomSqlExecution<CustomCommentMapper, List<Map<String, Object>>>(CustomCommentMapper.class) {
            @Override
            public List<Map<String, Object>> execute(CustomCommentMapper customMapper) {
                return customMapper.selectCommentsByProcInstId(procInstId);
            }
        };
        return managementService.executeCustomSql(customSqlExecution);
    }
}

package com.zjhh.workflow;

import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.workflow.request.TaskReq;
import com.zjhh.workflow.vo.*;
import org.flowable.idm.api.User;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-11-01 10:37 上午
 */
public interface TaskHandler {

    /**
     * 加签
     */

    public String addSignTask(AddSignTaskVo addSignTaskVo, Boolean flag);

    /**
     * 执行任务
     *
     * @param params 参数
     */
    public String complete(CompleteTaskVo params);

    /**
     * 查询待办任务列表
     *
     * @param params 参数
     * @return
     */
    public Page<TaskVo> getApplyingTasks(TaskReq params);


    /**
     * 查询已办任务列表
     *
     * @param params 参数
     * @return
     */
    public Page<TaskVo> getApplyedTasks(TaskReq params);

    /**
     * 通过流程实例id获取流程实例的待办任务审批人列表
     *
     * @param processInstanceId 流程实例id
     * @return
     */
    public List<User> getApprovers(String processInstanceId);


    /**
     * 驳回任意节点 暂时没有考虑子流程
     *
     * @param backTaskVo 参数
     * @return
     */
    public ReData<String> backToStepTask(BackTaskVo backTaskVo);


    /**
     * 获取可驳回节点列表
     *
     * @param taskId            任务id
     * @param processInstanceId 流程实例id
     * @return
     */
    public List<FlowNodeVo> getBackNodesByProcessInstanceId(String processInstanceId, String taskId);

}

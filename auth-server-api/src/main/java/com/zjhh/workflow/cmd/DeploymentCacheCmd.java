package com.zjhh.workflow.cmd;

import cn.hutool.core.util.StrUtil;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.common.engine.impl.persistence.deploy.DeploymentCache;
import org.flowable.engine.impl.persistence.deploy.DeploymentManager;
import org.flowable.engine.impl.persistence.deploy.ProcessDefinitionCacheEntry;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.flowable.engine.repository.Deployment;

import java.io.Serializable;

public class DeploymentCacheCmd implements Command<Deployment>, Serializable {
    private static final long serialVersionUID = 1L;
    private final String definitionId;

    public DeploymentCacheCmd(String definitionId) {
        this.definitionId = definitionId;
    }

    @Override
    public Deployment execute(CommandContext commandContext) {
        DeploymentManager deploymentManager = CommandContextUtil.getProcessEngineConfiguration().getDeploymentManager();
        DeploymentCache<ProcessDefinitionCacheEntry> deploymentCache = deploymentManager.getProcessDefinitionCache();

        if (StrUtil.isNotBlank(definitionId)) {

            // 清除指定缓存
            deploymentCache.remove(definitionId);

        } else {

            // 清除全部缓存
            deploymentCache.clear();

        }

        return null;
    }
}

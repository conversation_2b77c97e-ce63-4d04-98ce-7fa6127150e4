package com.zjhh.workflow.config;

import com.zjhh.workflow.flowable.custommapper.CustomCommentMapper;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.common.engine.impl.de.odysseus.el.misc.TypeConverter;
import org.flowable.common.engine.impl.de.odysseus.el.misc.TypeConverterImpl;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ManagementService;
import org.flowable.engine.IdentityService;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.flowable.spring.ProcessEngineFactoryBean;
import org.flowable.spring.job.service.SpringAsyncExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.core.task.TaskExecutor;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.HashSet;
import java.util.Set;

/**
 * @Description: flowable配置
 * @Author: Bruce.liu
 * @Since:18:44 2018/9/7
 */
@Configuration
public class FlowableConfig implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {

    @Value("${flowable.activityFontName}")
    private String activityFontName;
    @Value("${flowable.labelFontName}")
    private String labelFontName;
    @Value("${flowable.annotationFontName}")
    private String annotationFontName;
    @Value("${flowable.xml.encoding}")
    private String xmlEncoding;
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private PlatformTransactionManager transactionManager;

    @Override
    public void configure(SpringProcessEngineConfiguration configure) {
        //配置中文
        configure.setActivityFontName(activityFontName);
        configure.setLabelFontName(labelFontName);
        configure.setAnnotationFontName(annotationFontName);
        //设置是否升级 false不升级  true升级
        configure.setDatabaseSchemaUpdate("false");
        //设置自定义的uuid生成策略
        configure.setIdGenerator(uuidGenerator());
        configure.setXmlEncoding(xmlEncoding);
        //启用任务关系计数
        configure.setEnableTaskRelationshipCounts(false);
        configure.setAsyncExecutorActivate(false);

        //启动同步功能 一定要启动否则报错
        configure.setAsyncExecutor(springAsyncExecutor());
        //自定义sql
        Set<Class<?>> customMybatisMappers = new HashSet<>();
        customMybatisMappers.add(CustomCommentMapper.class);
        configure.setCustomMybatisMappers(customMybatisMappers);
        //集成DMN
//        List<EngineConfigurator> configurators = new ArrayList<>();
//        configurators.add(dmnEngineConfiguration);
//        configure.setConfigurators(configurators);
    }
    
    /**
     * 提供 SpringProcessEngineConfiguration Bean
     *
     * @return SpringProcessEngineConfiguration 实例
     */
    @Bean
    public SpringProcessEngineConfiguration springProcessEngineConfiguration() {
        SpringProcessEngineConfiguration configuration = new SpringProcessEngineConfiguration();
        configuration.setDataSource(dataSource);
        configuration.setTransactionManager(transactionManager);
        configuration.setDatabaseSchemaUpdate("false");
        configuration.setActivityFontName(activityFontName);
        configuration.setLabelFontName(labelFontName);
        configuration.setAnnotationFontName(annotationFontName);
        configuration.setXmlEncoding(xmlEncoding);
        configuration.setEnableTaskRelationshipCounts(false);
        configuration.setAsyncExecutorActivate(false);
        configuration.setAsyncExecutor(springAsyncExecutor());
        
        // 设置自定义的uuid生成策略
        configuration.setIdGenerator(uuidGenerator());
        
        // 自定义SQL映射
        Set<Class<?>> customMybatisMappers = new HashSet<>();
        customMybatisMappers.add(CustomCommentMapper.class);
        configuration.setCustomMybatisMappers(customMybatisMappers);
        
        return configuration;
    }

    /**
     * 提供 ProcessEngine Bean
     *
     * @param processEngineConfiguration 流程引擎配置
     * @return ProcessEngine 实例
     * @throws Exception 可能的异常
     */
    @Bean
    public ProcessEngine processEngine(SpringProcessEngineConfiguration processEngineConfiguration) throws Exception {
        ProcessEngineFactoryBean factoryBean = new ProcessEngineFactoryBean();
        factoryBean.setProcessEngineConfiguration(processEngineConfiguration);
        return factoryBean.getObject();
    }

//    @Bean
//    public ObjectMapper objectMapper() {
//        return new ObjectMapper();
//    }

    @Bean
    public TypeConverter typeConverter() {
        return new TypeConverterImpl();
    }

    @Bean
    public UuidGenerator uuidGenerator() {
        return new UuidGenerator();
    }

    @Bean
    public SpringAsyncExecutor springAsyncExecutor() {
        SpringAsyncExecutor springAsyncExecutor = new SpringAsyncExecutor();
        springAsyncExecutor.setTaskExecutor(processTaskExecutor());
        springAsyncExecutor.setDefaultAsyncJobAcquireWaitTimeInMillis(1000);
        springAsyncExecutor.setDefaultTimerJobAcquireWaitTimeInMillis(1000);
        return springAsyncExecutor;
    }

    @Bean
    public TaskExecutor processTaskExecutor() {
        return new SimpleAsyncTaskExecutor();
    }

    /**
     * BpmnXMLConverter
     *
     * @return BpmnXMLConverter
     */
    @Bean
    public BpmnXMLConverter createBpmnXMLConverter() {
        return new BpmnXMLConverter();
    }

    /**
     * 提供 RepositoryService Bean
     *
     * @param processEngine Flowable 流程引擎
     * @return RepositoryService 实例
     */
    @Bean
    public RepositoryService repositoryService(ProcessEngine processEngine) {
        return processEngine.getRepositoryService();
    }

    /**
     * 提供 RuntimeService Bean
     *
     * @param processEngine Flowable 流程引擎
     * @return RuntimeService 实例
     */
    @Bean
    public RuntimeService runtimeService(ProcessEngine processEngine) {
        return processEngine.getRuntimeService();
    }

    /**
     * 提供 TaskService Bean
     *
     * @param processEngine Flowable 流程引擎
     * @return TaskService 实例
     */
    @Bean
    public TaskService taskService(ProcessEngine processEngine) {
        return processEngine.getTaskService();
    }

    /**
     * 提供 HistoryService Bean
     *
     * @param processEngine Flowable 流程引擎
     * @return HistoryService 实例
     */
    @Bean
    public HistoryService historyService(ProcessEngine processEngine) {
        return processEngine.getHistoryService();
    }

    /**
     * 提供 ManagementService Bean
     *
     * @param processEngine Flowable 流程引擎
     * @return ManagementService 实例
     */
    @Bean
    public ManagementService managementService(ProcessEngine processEngine) {
        return processEngine.getManagementService();
    }
    
    /**
     * 提供 IdentityService Bean
     *
     * @param processEngine Flowable 流程引擎
     * @return IdentityService 实例
     */
    @Bean
    public IdentityService identityService(ProcessEngine processEngine) {
        return processEngine.getIdentityService();
    }

//    /**
//     * BpmnJsonConverter
//     *
//     * @return BpmnJsonConverter
//     */
//    @Bean
//    public BpmnJsonConverter createBpmnJsonConverter() {
//        return new BpmnJsonConverter();
//    }
//
//
//    /**
//     * 兼容V5
//     *
//     * @return
//     */
//    @Bean
//    public SpringFlowable5CompatibilityHandlerFactory createSpringFlowable5CompatibilityHandlerFactory() {
//        return new SpringFlowable5CompatibilityHandlerFactory();
//    }
//
//    /**
//     * 在配置文件中如果没有字段，使用@Value的时候就会忽略掉，不会报错
//     *
//     * @return
//     */
//    @Bean
//    public static PropertySourcesPlaceholderConfigurer placeholderConfigurer() {
//        PropertySourcesPlaceholderConfigurer configurer = new PropertySourcesPlaceholderConfigurer();
//        configurer.setIgnoreUnresolvablePlaceholders(true);
//        return configurer;
//    }
//
//    @Bean
//    public SpringContextHolder creatSpringContextHolder() {
//        SpringContextHolder springContextHolder = new SpringContextHolder();
//        return springContextHolder;
//    }
}

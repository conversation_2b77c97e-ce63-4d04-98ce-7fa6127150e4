package com.zjhh.workflow.config;

import org.aspectj.lang.annotation.Aspect;
import org.springframework.context.annotation.Configuration;

/**
 * 事务配置
 *
 * <AUTHOR>
 * @date 2018/6/25 11:07
 */
@Aspect
@Configuration
public class TransactionConfig {

//    @Resource
//    private DataSourceTransactionManager transactionManager;
//
//    @Bean(name = "txAdvice")
//    public TransactionInterceptor txAdvice() {
//        Properties properties = new Properties();
//        properties.setProperty("save*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("insert*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("create*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("add*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("update*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("edit*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("del*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("drop*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("remove*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("import*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("active*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("stop*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        /*************************   flowable   **************************/
//        properties.setProperty("start*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("stop*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("revoke*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("complete*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("turn*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("claim*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("unClaim*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("back*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("deploy*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("set*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("before*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("after*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("activate*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("suspend*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("sync*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("review*", "PROPAGATION_REQUIRES_NEW,-Exception");
//        properties.setProperty("copy*", "PROPAGATION_REQUIRES_NEW,-Exception");
//
//        /*************************   flowable   **************************/
//        properties.setProperty("query*", "PROPAGATION_REQUIRES_NEW,-Exception,readOnly");
//        properties.setProperty("find*", "PROPAGATION_REQUIRES_NEW,-Exception,readOnly");
//        properties.setProperty("select*", "PROPAGATION_REQUIRES_NEW,-Exception,readOnly");
//        properties.setProperty("get*", "PROPAGATION_REQUIRES_NEW,-Exception,readOnly");
//        properties.setProperty("*", "PROPAGATION_REQUIRES_NEW,-Exception,readOnly");
//        return new TransactionInterceptor(transactionManager, properties);
//    }
//
//    @Bean
//    public BeanNameAutoProxyCreator txProxy() {
//        BeanNameAutoProxyCreator creator = new BeanNameAutoProxyCreator();
//        creator.setInterceptorNames("txAdvice");
//        creator.setBeanNames("*Service", "*ServiceImpl","*generator");
//        creator.setProxyTargetClass(true);
//        return creator;
//    }

}

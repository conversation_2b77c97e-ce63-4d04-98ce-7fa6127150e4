package com.zjhh.workflow.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zjhh.comm.response.ReData;
import com.zjhh.workflow.cmd.DeploymentCacheCmd;
import com.zjhh.workflow.dao.mapper.DeploymentMapper;
import com.zjhh.workflow.request.AddModelReq;
import com.zjhh.workflow.service.IFlowableDeployService;
import jakarta.annotation.Resource;
import org.flowable.engine.ManagementService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.Deployment;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022-01-18 3:07 下午
 */
@DS("master")
@Service
public class IFlowableModelServiceImpl implements IFlowableDeployService {

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private DeploymentMapper deploymentMapper;

    @Resource
    private ManagementService managementService;


    @Override
    public ReData<String> deploy(AddModelReq req) {

        try {
            Deployment deployment = repositoryService.createDeployment().addInputStream(req.getFile().getOriginalFilename(), req.getFile().getInputStream())
                    .name(req.getProcessName()).key(req.getProcessId()).deploy();
            String key = deploymentMapper.getFlowKey(deployment.getId());
            String oldId = deploymentMapper.getOldId(deployment.getId(), key);
            String removeID = deploymentMapper.getDefId(oldId);
            deploymentMapper.updateResource(deployment.getId(), oldId);
            managementService.executeCommand(new DeploymentCacheCmd(removeID));

            return ReData.success("流程发布成功");
        } catch (IOException e) {
            e.printStackTrace();
            return ReData.success("流程发布失败");
        }

    }
}

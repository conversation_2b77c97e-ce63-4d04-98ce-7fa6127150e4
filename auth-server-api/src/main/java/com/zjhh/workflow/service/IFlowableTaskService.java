package com.zjhh.workflow.service;

import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.db.comm.PageReq;
import com.zjhh.workflow.request.TaskReq;
import com.zjhh.workflow.vo.*;
import org.flowable.idm.api.User;
import org.flowable.task.api.Task;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> bruce.liu
 * @projectName : flowable
 * @description: 运行时的任务service
 * @date : 2019/11/1315:05
 */
public interface IFlowableTaskService {


    String processDefIdByKey(String processDefKey);

    /**
     * 驳回任意节点 暂时没有考虑子流程
     *
     * @param backTaskVo 参数
     * @return
     */
    public ReData<String> backToStepTask(BackTaskVo backTaskVo);

    /**
     * 获取可驳回节点列表
     *
     * @param taskId            任务id
     * @param processInstanceId 流程实例id
     * @return
     */
    public List<FlowNodeVo> getBackNodesByProcessInstanceId(String processInstanceId, String taskId);

    /**
     * 任务前加签 （如果多次加签只能显示第一次前加签的处理人来处理任务）
     * 多个加签人处理完毕任务之后又流到自己这里
     *
     * @param addSignTaskVo 参数
     * @return
     */
    public ReData<String> beforeAddSignTask(AddSignTaskVo addSignTaskVo);

    /**
     * 任务后加签（加签人自己自动审批完毕加签多个人处理任务）
     *
     * @param addSignTaskVo 参数
     * @return
     */
    public ReData<String> afterAddSignTask(AddSignTaskVo addSignTaskVo);

    /**
     * 任务加签
     *
     * @param addSignTaskVo 参数
     * @param flag          true向后加签  false向前加签
     * @return
     */
    public ReData<String> addSignTask(AddSignTaskVo addSignTaskVo, Boolean flag);

    /**
     * 反签收任务
     *
     * @param claimTaskVo 参数
     * @return
     */
    public ReData<String> unClaimTask(ClaimTaskVo claimTaskVo);

    /**
     * 签收任务
     *
     * @param claimTaskVo 参数
     * @return
     */
    public ReData<String> claimTask(ClaimTaskVo claimTaskVo);

    /**
     * 委派任务
     *
     * @param delegateTaskVo 参数
     * @return
     */
    public ReData<String> delegateTask(DelegateTaskVo delegateTaskVo);

    /**
     * 转办
     *
     * @param turnTaskVo 转办任务VO
     * @return 返回信息
     */
    public ReData<String> turnTask(TurnTaskVo turnTaskVo);

    public ReData<String> updateAssignee(TurnTaskVo turnTaskVo);

    /**
     * 执行任务
     *
     * @param params 参数
     */
    public ReData<String> complete(CompleteTaskVo params);

    /**
     * 通过任务id获取任务对象
     *
     * @param taskId 任务id
     * @return
     */
    public ReData<Task> findTaskById(String taskId);

    /**
     * 查询待办任务列表
     *
     * @param params 参数
     * @return
     */
    public Page<TaskVo> getApplyingTasks(TaskReq params);


    public Page<String> listAdviceGuid(String userCode,String unitCode, PageReq req);


    /**
     * 查询已办任务列表
     *
     * @param params 参数
     * @return
     */
    public Page<TaskVo> getApplyedTasks(TaskReq params);

    /**
     * 通过流程实例id获取流程实例的待办任务审批人列表
     *
     * @param processInstanceId 流程实例id
     * @return
     */
    public List<User> getApprovers(String processInstanceId);

    /**
     * 通过任务id判断当前节点是不是并行网关的节点
     *
     * @param taskId 任务id
     * @return
     */
    public boolean checkParallelgatewayNode(String taskId);

    public String getTaskId(String processId);

    public Task getCurrentTask(String processId);

    public void setTaskLocalVars(String taskId, Map<String, String> vars);

    Map<String, Object> getTaskLocalVars(String taskId);

    List<String> taskUsers(String taskId);

    List<String> currentTaskUsers(String taskId);

    List<String> instanceUsers(String processId);

    Task getTaskByProcessIdAndAssignee(String processId, String assignee);

    Boolean checkMultiTaskCompleted(String processId);

    void deleteCandidateUser(String processId, String userCode);

    String getNextTaskDefinitionKey(String taskId);

}

package com.zjhh.workflow.service;

import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.workflow.request.ProcessDefinitionReq;
import com.zjhh.workflow.vo.ProcessDefinitionVo;

/**
 * <AUTHOR> bruce.liu
 * @title: : IFlowProcessDi
 * @projectName : flowable
 * @description: 流程定义
 * @date : 2019/11/1314:11
 */
public interface IFlowableProcessDefinitionService {

    /**
     * 通过条件查询流程定义
     *
     * @param params
     * @return
     */
    public Page<ProcessDefinitionVo> getPagerModel(ProcessDefinitionReq params);

    /**
     * 通过流程定义id获取流程定义的信息
     *
     * @param processDefinitionId 流程定义id
     * @return
     */
    public ProcessDefinitionVo getById(String processDefinitionId);

    /**
     * 挂起流程定义
     *
     * @param processDefinitionId 流程定义id
     * @param suspensionState     状态1挂起 2激活
     */
    public ReData suspendOrActivateProcessDefinitionById(String processDefinitionId, int suspensionState);

}

package com.zjhh.workflow.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.workflow.dao.mapper.IFlowableProcessDefinitionDao;
import com.zjhh.workflow.request.ProcessDefinitionReq;
import com.zjhh.workflow.service.IFlowableProcessDefinitionService;
import com.zjhh.workflow.vo.ProcessDefinitionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> bruce.liu
 * @title: : FlowableProcessDefinitionServiceImpl
 * @projectName : flowable
 * @description: 流程定义service
 * @date : 2019/11/1314:18
 */
@DS("master")
@Service
public class FlowableProcessDefinitionServiceImpl extends BaseProcessService implements IFlowableProcessDefinitionService {

    @Autowired
    private IFlowableProcessDefinitionDao flowableProcessDefinitionDao;

    @Override
    public Page<ProcessDefinitionVo> getPagerModel(ProcessDefinitionReq params) {

        Page<ProcessDefinitionVo> page = flowableProcessDefinitionDao.getPagerModel(params.getPage(ProcessDefinitionVo.class), params);
        return page;
    }

    @Override
    public ProcessDefinitionVo getById(String processDefinitionId) {
        return flowableProcessDefinitionDao.getById(processDefinitionId);
    }

    @Override
    public ReData suspendOrActivateProcessDefinitionById(String processDefinitionId, int suspensionState) {
        ReData returnVo = null;
        if (suspensionState == 1) {
            repositoryService.suspendProcessDefinitionById(processDefinitionId, true, null);
            returnVo = ReData.success("挂起成功！");
        } else {
            repositoryService.activateProcessDefinitionById(processDefinitionId, true, null);
            returnVo = ReData.success("激活成功！");
        }
        return returnVo;
    }

    public void getLine() {

    }

}

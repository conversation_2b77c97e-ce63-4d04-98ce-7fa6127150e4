package com.zjhh.workflow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.zjhh.workflow.service.IFlowableBpmnModelService;
import com.zjhh.workflow.vo.TaskNodeVo;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR> bruce.liu
 * @title: : FlowableBpmnModelServiceImpl
 * @projectName : flowable
 * @description: BpmnModel service
 * @date : 2019/11/2519:42
 */
@DS("master")
@Service
public class FlowableBpmnModelServiceImpl extends BaseProcessService implements IFlowableBpmnModelService {

    @Override
    public BpmnModel getBpmnModelByProcessDefId(String processDefId) {
        return repositoryService.getBpmnModel(processDefId);
    }

    public List<FlowNode> findFlowNodes(String processDefId) {
        List<FlowNode> flowNodes = new ArrayList<>();
        BpmnModel bpmnModel = this.getBpmnModelByProcessDefId(processDefId);
        Process process = bpmnModel.getMainProcess();
        Collection<FlowElement> list = process.getFlowElements();
        list.forEach(flowElement -> {
            if (flowElement instanceof FlowNode && flowElement instanceof UserTask) {

                flowNodes.add((FlowNode) flowElement);
            }
        });
        return flowNodes;
    }

    @Override
    public List<TaskNodeVo> findFlowElements(String processDefId) {
        List<TaskNodeVo> flowNodes = new ArrayList<>();
        BpmnModel bpmnModel = this.getBpmnModelByProcessDefId(processDefId);
        Process process = bpmnModel.getMainProcess();
        Collection<FlowElement> list = process.getFlowElements();
        list.forEach(flowElement -> {
            if (flowElement instanceof FlowNode && flowElement instanceof UserTask) {
                if (!"skip".equals(flowElement.getDocumentation())) {
                    TaskNodeVo taskNodeVo = new TaskNodeVo();
                    taskNodeVo.setNodeId(flowElement.getId());
                    taskNodeVo.setNodeName(flowElement.getName());
                    taskNodeVo.setSelectable(true);
                    taskNodeVo.setNodeCode(flowElement.getDocumentation());
                    taskNodeVo.setNodeAssignee(((UserTask) flowElement).getAssignee());
                    flowNodes.add(taskNodeVo);
                }

            }
        });
        return flowNodes;
    }

    @Override
    public List<TaskNodeVo> findFlowAllElements(String processDefId) {
        List<TaskNodeVo> flowNodes = new ArrayList<>();
        BpmnModel bpmnModel = this.getBpmnModelByProcessDefId(processDefId);
        Process process = bpmnModel.getMainProcess();
        Collection<FlowElement> list = process.getFlowElements();
        list.forEach(flowElement -> {
            if (flowElement instanceof StartEvent || flowElement instanceof UserTask || flowElement instanceof EndEvent) {
                TaskNodeVo taskNodeVo = new TaskNodeVo();
                taskNodeVo.setNodeId(flowElement.getId());
                taskNodeVo.setNodeName(flowElement.getName());
                if (flowElement instanceof StartEvent) {
                    taskNodeVo.setNodeCode("start");
                } else if (flowElement instanceof EndEvent) {
                    taskNodeVo.setNodeCode("end");
                } else {
                    taskNodeVo.setNodeCode(flowElement.getDocumentation());
                }

                flowNodes.add(taskNodeVo);


            }
        });
        flowNodes.sort(Comparator.comparing(TaskNodeVo::getNodeId));
        return flowNodes;
    }

    @Override
    public List<EndEvent> findEndFlowElement(String processDefId) {
        BpmnModel bpmnModel = this.getBpmnModelByProcessDefId(processDefId);
        if (bpmnModel != null) {
            Process process = bpmnModel.getMainProcess();
            return process.findFlowElementsOfType(EndEvent.class);
        } else {
            return null;
        }
    }

    @Override
    public FlowNode findMainProcessActivityByActivityId(String processDefId, String activityId) {
        FlowNode activity = null;
        BpmnModel bpmnModel = this.getBpmnModelByProcessDefId(processDefId);
        Process process = bpmnModel.getMainProcess();
        FlowElement flowElement = process.getFlowElement(activityId);
        if (flowElement != null) {
            activity = (FlowNode) flowElement;
        }
        return activity;
    }

    @Override
    public FlowNode findFlowNodeByActivityId(String processDefId, String activityId) {
        FlowNode activity = null;
        BpmnModel bpmnModel = this.getBpmnModelByProcessDefId(processDefId);
        List<Process> processes = bpmnModel.getProcesses();
        for (Process process : processes) {
            FlowElement flowElement = process.getFlowElementMap().get(activityId);
            if (flowElement != null) {
                activity = (FlowNode) flowElement;
                break;
            }
        }
        return activity;
    }

    @Override
    public TaskNodeVo findTaskNodeByActivityId(String processDefId, String activityId) {
        TaskNodeVo activity = new TaskNodeVo();
        BpmnModel bpmnModel = this.getBpmnModelByProcessDefId(processDefId);
        List<Process> processes = bpmnModel.getProcesses();
        for (Process process : processes) {
            FlowElement flowElement = process.getFlowElementMap().get(activityId);
            if (flowElement != null) {
                activity.setNodeId(flowElement.getId());
                activity.setNodeName(flowElement.getName());
                activity.setNodeAssignee(((UserTask) flowElement).getAssignee());
                activity.setCandidateUsercodes(((UserTask) flowElement).getCandidateUsers());
                activity.setNodeCode(flowElement.getDocumentation());
                break;
            }
        }
        return activity;
    }

    @Override
    public boolean checkActivitySubprocessByActivityId(String processDefId, String activityId) {
        boolean flag = true;
        List<FlowNode> activities = this.findFlowNodesByActivityId(processDefId, activityId);
        if (CollUtil.isNotEmpty(activities)) {
            flag = false;
        }
        return flag;
    }

    public List<FlowNode> findFlowNodesByActivityId(String processDefId, String activityId) {
        List<FlowNode> activities = new ArrayList<>();
        BpmnModel bpmnModel = this.getBpmnModelByProcessDefId(processDefId);
        List<Process> processes = bpmnModel.getProcesses();
        for (Process process : processes) {
            FlowElement flowElement = process.getFlowElement(activityId);
            if (flowElement != null) {
                FlowNode flowNode = (FlowNode) flowElement;
                activities.add(flowNode);
            }
        }
        return activities;
    }

    @Override
    public List<Activity> findActivityByActivityId(String processDefId, String activityId) {
        List<Activity> activities = new ArrayList<>();
        BpmnModel bpmnModel = this.getBpmnModelByProcessDefId(processDefId);
        List<Process> processes = bpmnModel.getProcesses();
        for (Process process : processes) {
            FlowElement flowElement = process.getFlowElement(activityId);
            if (flowElement != null) {
                Activity activity = (Activity) flowElement;
                activities.add(activity);
            }
        }
        return activities;
    }

    @Override
    public Activity findActivityByName(String processDefId, String name) {
        Activity activity = null;
        BpmnModel bpmnModel = this.getBpmnModelByProcessDefId(processDefId);
        Process process = bpmnModel.getMainProcess();
        Collection<FlowElement> list = process.getFlowElements();
        for (FlowElement f : list) {
            if (StrUtil.isNotBlank(name)) {
                if (name.equals(f.getName())) {
                    activity = (Activity) f;
                    break;
                }
            }
        }
        return activity;
    }
}

package com.zjhh.workflow.service;

import com.zjhh.db.comm.Page;
import com.zjhh.db.comm.PageReq;
import com.zjhh.workflow.vo.TaskNodeVo;
import com.zjhh.workflow.vo.TurnTaskVo;
import org.flowable.task.api.Task;

import java.util.List;
import java.util.Map;

public interface FlowableHandler {


    String processDefIdByKey(String processDefKey);

    /**
     * 开启流程
     *
     * @param processId
     * @return
     */
    String startProcess(String processId, Map<String, Object> vars, String userCode);

    Boolean completeTask(String processId, Map<String, Object> vars, String message, String type, String userCode);

    Boolean completeMultiTask(String processId, String taskId, Map<String, Object> vars, String message, String type, String userCode);

    Page<String> listAdviceGuidByUserCode(String userCode,String unitCode, PageReq req);

    Integer checkShowType(String processId, String userCode, Boolean isSuperManager);


    Boolean checkTaskUser(String processId, String userCode);

    Integer checkShowTypeNotSuper(String processId, String userCode);

    Task checkCurrentTask(String processId);

    Integer turnTask(TurnTaskVo turnTaskVo);

    List<TaskNodeVo> listTaskNode(String processDefId);

    List<TaskNodeVo> listAllTaskNode(String processDefId);


    TaskNodeVo taskNodeById(String processDefId, String nodeId);

    Task getCurrentTaskByProcessId(String processId);

    void setTaskLocalVars(String taskId, Map<String, String> vars);

    Map<String, Object> getTaskLocalVars(String taskId);

    List<String> taskUsers(String taskId);

    void claimTask(String taskId, String userCode);

    TaskNodeVo taskNodeByName(String processDefId, String name);

    Task getTaskByProcessIdAndAssignee(String processId, String assignee);

    Boolean checkMultiTaskCompleted(String processId);

    void deleteCandidateUser(String processId, String userCode);

    void updateAssignee(TurnTaskVo turnTaskVo);

    void stopProcess(String processId);

    String getNextTaskDefinitionKey(String taskId);



}

package com.zjhh.workflow.service;

import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.workflow.request.ProcessInstanceReq;
import com.zjhh.workflow.vo.EndProcessVo;
import com.zjhh.workflow.vo.ProcessInstanceVo;
import com.zjhh.workflow.vo.RevokeProcessVo;
import com.zjhh.workflow.vo.StartProcessInstanceVo;

/**
 * <AUTHOR> bruce.liu
 * @projectName : flowable
 * @description: 流程实例service
 * @date : 2019/10/2511:40
 */
public interface IFlowableProcessInstanceService {


    /**
     * 启动流程
     *
     * @param startProcessInstanceVo 参数
     * @return
     */
    String startProcessInstanceByKey(StartProcessInstanceVo startProcessInstanceVo, String userCode);

    /**
     * 查询流程实例列表
     *
     * @param params 参数
     * @param query  分页参数
     * @return
     */
    Page<ProcessInstanceVo> getPagerModel(ProcessInstanceReq params);

    /**
     * 查询我发起的流程实例
     *
     * @param params 参数
     * @param query  分页参数
     * @return
     */
    Page<ProcessInstanceVo> getMyProcessInstances(ProcessInstanceReq params);

    /**
     * 获取流程图图片
     *
     * @param processInstanceId 流程实例id
     * @return
     */
    byte[] createImage(String processInstanceId);

    /**
     * 删除流程实例
     *
     * @param processInstanceId 流程实例id
     * @return
     */
    ReData deleteProcessInstanceById(String processInstanceId);

    /**
     * 激活流程定义
     *
     * @param processInstanceId 流程实例id
     * @param suspensionState   2激活 1挂起
     */
    ReData suspendOrActivateProcessInstanceById(String processInstanceId, Integer suspensionState);

    /**
     * 终止流程
     *
     * @param endVo 参数
     * @return
     */
    ReData stopProcessInstanceById(EndProcessVo endVo);

    /**
     * 撤回流程
     *
     * @param revokeVo 参数
     * @return
     */
    ReData revokeProcess(RevokeProcessVo revokeVo);
}

package com.zjhh.workflow.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zjhh.workflow.service.IFlowableIdentityService;
import org.flowable.idm.api.Group;
import org.flowable.idm.api.User;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> bruce.liu
 * @title: : FlowableIdentityServiceImpl
 * @projectName : flowable
 * @description: 用户组service
 * @date : 2019/11/1411:46
 */
@DS("master")
@Service
public class FlowableIdentityServiceImpl extends BaseProcessService implements IFlowableIdentityService {

    @Override
    public void saveUser(User user) {
        identityService.saveUser(user);
    }

    @Override
    public void saveGroup(Group group) {
        identityService.saveGroup(group);
    }

}

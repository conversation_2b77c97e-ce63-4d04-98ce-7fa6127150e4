package com.zjhh.workflow.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.zjhh.db.comm.Page;
import com.zjhh.db.comm.PageReq;
import com.zjhh.workflow.service.FlowableHandler;
import com.zjhh.workflow.service.IFlowableBpmnModelService;
import com.zjhh.workflow.service.IFlowableProcessInstanceService;
import com.zjhh.workflow.service.IFlowableTaskService;
import com.zjhh.workflow.vo.*;
import jakarta.annotation.Resource;
import org.flowable.bpmn.model.Activity;
import org.flowable.task.api.Task;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-01-18 7:36 下午
 */
@DS("master")
@Component
public class FlowableHandlerImpl implements FlowableHandler {

    @Resource
    private IFlowableProcessInstanceService iFlowableProcessInstanceService;

    @Resource
    private IFlowableBpmnModelService iFlowableBpmnModelService;

    @Resource
    private IFlowableTaskService iFlowableTaskService;

    @Override
    public String processDefIdByKey(String processDefKey) {
        return iFlowableTaskService.processDefIdByKey(processDefKey);
    }

    @Override
    public String startProcess(String processId, Map<String, Object> vars, String userCode) {
        StartProcessInstanceVo req = new StartProcessInstanceVo();
        req.setProcessDefinitionKey(processId);
        req.setBusinessKey("advice");
        req.setFormName("监督建议");
        req.setVariables(vars);
        return iFlowableProcessInstanceService.startProcessInstanceByKey(req, userCode);
    }

    @Override
    public Boolean completeTask(String processId, Map<String, Object> vars, String message, String type, String userCode) {
        String taskId = iFlowableTaskService.getTaskId(processId);
        CompleteTaskVo vo = new CompleteTaskVo();
        vo.setVariables(vars);
        vo.setTaskId(taskId);
        vo.setProcessInstanceId(processId);
        vo.setMessage(message);
        if (StrUtil.isNotBlank(type)) {
            vo.setType(type);
        }
        vo.setUserCode(userCode);
        return iFlowableTaskService.complete(vo).getIsSuccess();
    }

    @Override
    public Boolean completeMultiTask(String processId, String taskId, Map<String, Object> vars, String message, String type, String userCode) {

        CompleteTaskVo vo = new CompleteTaskVo();
        vo.setVariables(vars);
        vo.setTaskId(taskId);
        vo.setProcessInstanceId(processId);
        vo.setMessage(message);
        if (StrUtil.isNotBlank(type)) {
            vo.setType(type);
        }
        vo.setUserCode(userCode);
        return iFlowableTaskService.complete(vo).getIsSuccess();
    }

    @Override
    public Page<String> listAdviceGuidByUserCode(String userCode, String unitCode, PageReq req) {
        return iFlowableTaskService.listAdviceGuid(userCode, unitCode, req);
    }

    @Override
    public Integer checkShowType(String processId, String userCode, Boolean isSuperManager) {
        String taskId = iFlowableTaskService.getTaskId(processId);
        if (taskId == null) {
            return 2;
        }
        Task task = iFlowableTaskService.findTaskById(taskId).getData();
        Integer result = 0;
        if (StrUtil.isBlank(task.getAssignee())) {
            List<String> userCodes = taskUsers(taskId);
            return (!userCodes.isEmpty() && userCodes.contains(userCode)) || isSuperManager ? 1 : 0;
        }
        return task.getAssignee().equals(userCode) || isSuperManager ? 1 : 0;
    }

    @Override
    public Boolean checkTaskUser(String processId, String userCode) {
        Task currentTask = iFlowableTaskService.getCurrentTask(processId);
        if (ObjectUtil.isEmpty(currentTask)) {
            return false;
        }
        if (StrUtil.isBlank(currentTask.getAssignee())) {
            List<String> userCodes = iFlowableTaskService.currentTaskUsers(currentTask.getId());
            return (!userCodes.isEmpty() && userCodes.contains(userCode));
        }
        return currentTask.getAssignee().equals(userCode);
    }

    @Override
    public Integer checkShowTypeNotSuper(String processId, String userCode) {
        String taskId = iFlowableTaskService.getTaskId(processId);
        if (taskId == null) {
            return 2;
        }
        Task task = iFlowableTaskService.findTaskById(taskId).getData();
        Integer result = 0;
        if (StrUtil.isBlank(task.getAssignee())) {
            List<String> userCodes = taskUsers(taskId);
            return (!userCodes.isEmpty() && userCodes.contains(userCode)) ? 1 : 0;
        }
        return task.getAssignee().equals(userCode) ? 1 : 0;
    }

    @Override
    public Task checkCurrentTask(String processId) {
        return iFlowableTaskService.getCurrentTask(processId);
    }

    @Override
    public Integer turnTask(TurnTaskVo turnTaskVo) {
        if (StrUtil.isBlank(turnTaskVo.getTaskId())) {
            String taskId = iFlowableTaskService.getTaskId(turnTaskVo.getProcessInstanceId());
            turnTaskVo.setTaskId(taskId);
        }
        iFlowableTaskService.turnTask(turnTaskVo);
        return 1;
    }

    @Override
    public List<TaskNodeVo> listTaskNode(String processDefId) {
        return iFlowableBpmnModelService.findFlowElements(processDefId);
    }

    @Override
    public List<TaskNodeVo> listAllTaskNode(String processDefId) {
        return iFlowableBpmnModelService.findFlowAllElements(processDefId);
    }

    @Override
    public TaskNodeVo taskNodeById(String processDefId, String nodeId) {
        return iFlowableBpmnModelService.findTaskNodeByActivityId(processDefId, nodeId);
    }

    @Override
    public Task getCurrentTaskByProcessId(String processId) {
        return iFlowableTaskService.getCurrentTask(processId);
    }

    @Override
    public void setTaskLocalVars(String taskId, Map<String, String> vars) {
        iFlowableTaskService.setTaskLocalVars(taskId, vars);
    }

    @Override
    public Map<String, Object> getTaskLocalVars(String taskId) {
        return ObjectUtil.isEmpty(iFlowableTaskService.getTaskLocalVars(taskId)) ?
                new HashMap<>() :
                iFlowableTaskService.getTaskLocalVars(taskId);
    }

    @Override
    public List<String> taskUsers(String taskId) {
        return iFlowableTaskService.taskUsers(taskId);
    }

    @Override
    public void claimTask(String taskId, String userCode) {
        ClaimTaskVo claimTaskVo = new ClaimTaskVo();
        claimTaskVo.setTaskId(taskId);
        claimTaskVo.setUserCode(userCode);
        iFlowableTaskService.claimTask(claimTaskVo);
    }

    @Override
    public TaskNodeVo taskNodeByName(String processDefId, String name) {


        Activity activity = iFlowableBpmnModelService.findActivityByName(processDefId, name);

        return iFlowableBpmnModelService.findTaskNodeByActivityId(processDefId, activity.getId());

    }

    @Override
    public Task getTaskByProcessIdAndAssignee(String processId, String assignee) {

        return iFlowableTaskService.getTaskByProcessIdAndAssignee(processId, assignee);
    }

    @Override
    public Boolean checkMultiTaskCompleted(String processId) {
        return iFlowableTaskService.checkMultiTaskCompleted(processId);
    }

    @Override
    public void deleteCandidateUser(String processId, String userCode) {

    }

    @Override
    public void updateAssignee(TurnTaskVo turnTaskVo) {
        if (StrUtil.isBlank(turnTaskVo.getTaskId())) {
            String taskId = iFlowableTaskService.getTaskId(turnTaskVo.getProcessInstanceId());
            turnTaskVo.setTaskId(taskId);
        }
        iFlowableTaskService.updateAssignee(turnTaskVo);
    }

    @Override
    public void stopProcess(String processId) {
        EndProcessVo endProcessVo = new EndProcessVo();
        endProcessVo.setProcessInstanceId(processId);
        iFlowableProcessInstanceService.stopProcessInstanceById(endProcessVo);
    }

    @Override
    public String getNextTaskDefinitionKey(String taskId) {
        return iFlowableTaskService.getNextTaskDefinitionKey(taskId);
    }

}

package com.zjhh.workflow.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> bruce.liu
 * @title: : TurnTaskVo
 * @projectName : flowable
 * @description: 转办Vo
 * @date : 2019/11/1315:34
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TurnTaskVo extends BaseProcessVo {

    private static final long serialVersionUID = 5775056306149637437L;

    /**
     * 被转办人工号 必填
     */
    private String turnToUserId;

    private List<String> candidateUserCodes;

}

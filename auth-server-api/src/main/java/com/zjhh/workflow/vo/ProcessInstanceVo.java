package com.zjhh.workflow.vo;

import com.zjhh.comm.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-10-27 10:50 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProcessInstanceVo extends BaseVo {
    private static final long serialVersionUID = 8154040055047881616L;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 流程定义id
     */
    private String processDefinitionId;

    /**
     * 激活状态 1激活 2挂起
     */
    private int suspensionState;

    /**
     * 表单名称
     */
    private String formName;

    /**
     * 表单主键
     */
    private String businessKey;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 审批人
     */
    private String approver;

    /**
     * 发起人
     */
    private String starter;

    /**
     * 发起人id
     */
    private String starterId;

    /**
     * 系统标识
     */
    private String systemSn;

}

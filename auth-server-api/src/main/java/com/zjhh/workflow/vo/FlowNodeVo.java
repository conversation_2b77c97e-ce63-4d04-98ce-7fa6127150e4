package com.zjhh.workflow.vo;

import com.zjhh.comm.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-10-27 11:29 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlowNodeVo extends BaseVo {
    private static final long serialVersionUID = -7692495928382035940L;

    /**
     * 节点id
     */
    private String nodeId;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 执行人的code
     */
    private String userCode;
    /**
     * 执行人姓名
     */
    private String userName;

    /**
     * 任务节点结束时间
     */
    private Date endTime;
}

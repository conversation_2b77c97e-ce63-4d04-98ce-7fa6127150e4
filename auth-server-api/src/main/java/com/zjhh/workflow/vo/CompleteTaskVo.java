package com.zjhh.workflow.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * <AUTHOR> bruce.liu
 * @title: : CompleteTaskVo
 * @projectName : flowable
 * @description: 执行任务Vo
 * @date : 2019/11/1315:27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CompleteTaskVo extends BaseProcessVo {
    private static final long serialVersionUID = -8241511001523184513L;

    /**
     * 任务参数 选填
     */
    private Map<String, Object> variables;

}

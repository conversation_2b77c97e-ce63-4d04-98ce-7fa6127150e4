package com.zjhh.workflow.vo;

import com.zjhh.comm.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2021-10-27 10:47 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProcessDefinitionVo extends BaseVo {

    private static final long serialVersionUID = -6949800231241819934L;

    protected String id;

    protected String modelKey;

    protected String name;

    protected int version;

    protected String category;

    protected String deploymentId;

    protected String resourceName;

    protected String dgrmResourceName;

    protected int suspensionState;

    protected String tenantId;
}

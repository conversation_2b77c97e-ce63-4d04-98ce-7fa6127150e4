package com.zjhh.workflow.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务节点Node
 *
 * <AUTHOR>
 * @date 2022-01-20 2:54 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskNodeVo extends BaseVo {
    private static final long serialVersionUID = 2876385018834545658L;

    private String key;

    private String title;

    @JsonIgnore
    private String nodeId;
    @JsonIgnore
    private String nodeName;
    @JsonIgnore
    private String nodeAssignee;
    @JsonIgnore
    private String nodeCode;

    private List<String> candidateUsercodes;

    private String status;

    private Integer finished;

    private Boolean selectable;

    private String desc;

    private Map<String, String> scopedSlots;

    public Map<String, String> getScopedSlots() {
        Map<String, String> map = new HashMap<>(1);
        map.put("title", "title");
        return map;
    }

    public Boolean getSelectable() {
        return selectable;
    }

    public String getKey() {
        return nodeId;
    }

    public String getTitle() {
        return nodeName;
    }
}

package com.zjhh.workflow.vo;

import com.zjhh.comm.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 启动流程Req
 *
 * <AUTHOR>
 * @date 2021-10-27 11:12 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StartProcessInstanceVo extends BaseReq {
    private static final long serialVersionUID = 5808074156478721415L;

    /**
     * 流程定义key 必填
     */
    private String processDefinitionKey;
    /**
     * 业务系统id 必填
     */
    private String businessKey;
    /**
     * 启动流程变量 选填
     */
    private Map<String, Object> variables;
    /**
     * 申请人工号 必填
     */
    private String currentUserCode;
    /**
     * 系统标识 必填
     */
    private String systemSn;
    /**
     * 表单显示名称 必填
     */
    private String formName;
    /**
     * 流程提交人工号 必填
     */
    private String creator;
}

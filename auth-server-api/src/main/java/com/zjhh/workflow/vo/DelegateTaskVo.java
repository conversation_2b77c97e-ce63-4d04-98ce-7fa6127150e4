package com.zjhh.workflow.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> bruce.liu
 * @title: : DelegateTaskVo
 * @projectName : flowable
 * @description: 委派
 * @date : 2019/11/1315:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DelegateTaskVo extends BaseProcessVo {
    private static final long serialVersionUID = -8860631568873019419L;

    /**
     * 委派人
     */
    private String delegateUserCode;

}

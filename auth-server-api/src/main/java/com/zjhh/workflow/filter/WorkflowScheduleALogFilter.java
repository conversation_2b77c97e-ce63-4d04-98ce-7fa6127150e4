package com.zjhh.workflow.filter;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.p6spy.engine.spy.appender.MessageFormattingStrategy;

/**
 * <AUTHOR>
 * @date 2021-10-28 9:21 上午
 */
public class WorkflowScheduleALogFilter implements MessageFormattingStrategy {
    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category,
                                String prepared, String sql, String url) {
        if (sql.contains("RES.*")) {
            return "";
        } else {
            return StringUtils.isNotBlank(sql) ? " Consume Time：" + elapsed + " ms " + now +
                    "\n Execute SQL：" + sql.replaceAll("[\\s]+", " ") + "\n" : "";
        }
    }
}

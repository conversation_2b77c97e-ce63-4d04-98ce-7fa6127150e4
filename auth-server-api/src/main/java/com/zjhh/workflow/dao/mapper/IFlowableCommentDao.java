package com.zjhh.workflow.dao.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zjhh.workflow.vo.CommentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> bruce.liu
 * @projectName : flowable
 * @description: 流程备注Dao
 * @date : 2019/11/2413:00
 */
@DS("master")
@Mapper
public interface IFlowableCommentDao {

    /**
     * 通过流程实例id获取审批意见列表
     *
     * @param ProcessInstanceId 流程实例id
     * @return
     */
    public List<CommentVo> getFlowCommentVosByProcessInstanceId(@Param("processInstanceId") String ProcessInstanceId);

}

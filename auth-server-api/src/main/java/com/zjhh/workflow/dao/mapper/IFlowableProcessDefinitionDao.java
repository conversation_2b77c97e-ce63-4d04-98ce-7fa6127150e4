package com.zjhh.workflow.dao.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zjhh.db.comm.Page;
import com.zjhh.workflow.request.ProcessDefinitionReq;
import com.zjhh.workflow.vo.ProcessDefinitionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> bruce.liu
 * @title: : IFlowableProcessInstentDao
 * @projectName : flowable
 * @description: 流程定义Dao
 * @date : 2019/11/2317:09
 */
@DS("master")
@Mapper
public interface IFlowableProcessDefinitionDao {

    /**
     * 通过条件查询流程定义列表
     *
     * @param params 参数
     * @return
     */
    public Page<ProcessDefinitionVo> getPagerModel(@Param("page") Page<ProcessDefinitionVo> page, @Param("params") ProcessDefinitionReq params);

    /**
     * 通过流程定义id获取流程定义的信息
     *
     * @param processDefinitionId 流程定义id
     * @return
     */
    public ProcessDefinitionVo getById(@Param("processDefinitionId") String processDefinitionId);

    void updateResource(@Param("id") String id,@Param("oldId") String oldId );
    String getOldId(@Param("id") String id,@Param("key") String key);
}

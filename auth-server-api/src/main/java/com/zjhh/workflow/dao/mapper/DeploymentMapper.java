package com.zjhh.workflow.dao.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@DS("master")
@Mapper
public interface DeploymentMapper {


    void updateResource(@Param("id") String id, @Param("oldId") String oldId );
    String getOldId(@Param("id") String id,@Param("key") String key);
    String getDefId(@Param("id") String id);

    String getFlowKey(@Param("id") String id);

}

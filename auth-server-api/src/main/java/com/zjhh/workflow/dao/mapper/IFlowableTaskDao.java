package com.zjhh.workflow.dao.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zjhh.db.comm.Page;
import com.zjhh.workflow.request.TaskReq;
import com.zjhh.workflow.vo.TaskVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> bruce.liu
 * @title: : IFlowableTaskDao
 * @projectName : flowable
 * @description: flowabletask的查询
 * @date : 2019/11/2316:34
 */
@DS("master")
@Mapper
public interface IFlowableTaskDao {
    /**
     * 查询待办任务
     *
     * @param params 参数
     * @return
     */
    public Page<TaskVo> getApplyingTasks(@Param("page") Page<TaskVo> page, @Param("params") TaskReq params);

    /**
     * 查询已办任务列表
     *
     * @param params 参数
     * @return
     */
    public Page<TaskVo> getApplyedTasks(@Param("page") Page<TaskVo> page, @Param("params") TaskReq params);

    public Page<String> getAdviceGuid(@Param("page") Page<String> page, @Param("userCode") String userCode);


}

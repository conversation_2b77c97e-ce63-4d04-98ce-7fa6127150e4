package com.zjhh.workflow.dao.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zjhh.db.comm.Page;
import com.zjhh.workflow.request.ProcessInstanceReq;
import com.zjhh.workflow.vo.ProcessInstanceVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> bruce.liu
 * @title: : IFlowableProcessInstentDao
 * @projectName : flowable
 * @description: TODO
 * @date : 2019/11/2317:09
 */
@DS("master")
@Mapper
public interface IFlowableProcessInstanceDao {

    /**
     * 通过条件查询流程实例VO对象列表
     *
     * @param params 参数
     * @return
     */
    public Page<ProcessInstanceVo> getPagerModel(@Param("page") Page<ProcessInstanceVo> page, @Param("params") ProcessInstanceReq params);
}

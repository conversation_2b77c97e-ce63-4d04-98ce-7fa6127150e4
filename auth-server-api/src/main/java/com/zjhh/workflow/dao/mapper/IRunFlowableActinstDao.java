package com.zjhh.workflow.dao.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR> bruce.liu
 * @projectName : flowable
 * @description: 运行时的节点Dao
 * @date : 2019/12/417:55
 */
@DS("master")
@Mapper
public interface IRunFlowableActinstDao {

    /**
     * 删除节点信息
     *
     * @param ids ids
     */
    public void deleteRunActinstsByIds(List<String> ids);
}

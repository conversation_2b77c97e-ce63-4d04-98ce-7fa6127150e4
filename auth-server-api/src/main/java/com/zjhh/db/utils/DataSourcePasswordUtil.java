package com.zjhh.db.utils;

import cn.hutool.core.util.IdUtil;
import com.alibaba.druid.filter.config.ConfigTools;

/**
 * 数据库加密工具
 *
 * <AUTHOR>
 * @since 2020/6/15 16:54
 */
public class DataSourcePasswordUtil {

    private static final String PUBLIC_KEY = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIRL1ZPBXUQvuscppZdvxJekLva8lvYhCJtoicJZZzv0wenBwR7/bopSmzztkkgKQn9YMAp+FMv2ZYaQ6f8FAgUCAwEAAQ==";

    private static final String PRIVATE_KEY = "MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAhEvVk8FdRC+6xymll2/El6Qu9ryW9iEIm2iJwllnO/TB6cHBHv9uilKbPO2SSApCf1gwCn4Uy/ZlhpDp/wUCBQIDAQABAkBWMTojG9Xx+M7Nt1qFgXIqAfP/GPuZvAfpiIBIOQtDSdDfaj1SyQtiJkz4VdGJ+8i+r+KIIeYlmXbOTsbZHm35AiEAyMB3OJhlwLCquytFJaw70llLV9y9ShY1izmuMBH2aqsCIQCotHpvBr98f9HTY/XytRNuWN11vi83kayVztlFMT5GDwIgRmBqqzWJ3l5QzCLkL8vMgPZ62kdxOIrGpJNJBGMXrtcCIDYt19AGM8LQ7drclGUAwB/FDEI+9Lz83Jcz2dT9zokDAiAVhVmnAc+n8T5vktnyI0tVW3M907aWnzE2XBTxuUbG1A==";


    /**
     * 加密
     *
     * @param cipherText
     * @return
     * @throws Exception
     */
    public static String encrypt(String cipherText) throws Exception {
        return ConfigTools.encrypt(PRIVATE_KEY, cipherText);
    }

    /**
     * 解密
     *
     * @param cipherText
     * @return
     * @throws Exception
     */
    public static String decrypt(String cipherText) throws Exception {
        return ConfigTools.decrypt(PUBLIC_KEY, cipherText);
    }

    public static void main(String[] args) throws Exception {
        System.out.println(decrypt("S01Ltv8moa7ZLwxQ6JHAbd9oiqronZHYcHfcGZELt+QRw1zSp1dBr6LBwDynZicSeU2dBPjgvIheSf5VEqOqLQ=="));
        System.out.println(encrypt("zjwn@123"));
    }
}

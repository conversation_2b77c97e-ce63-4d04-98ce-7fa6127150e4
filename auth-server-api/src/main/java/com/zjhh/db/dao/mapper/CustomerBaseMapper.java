package com.zjhh.db.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/6/15 11:19
 */
public interface CustomerBaseMapper<T> extends BaseMapper<T> {

    /**
     * mybatis-plus 中的【选装件】 ， 自定义批量插入
     *
     * @param list
     * @return
     */
    int insertBatchSomeColumn(List<T> list);

    /**
     * 根据 Wrapper 条件，查询全部记录
     * <p>注意： 只返回第一个字段的值</p>
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
    List<String> selectStrings(@Param(Constants.WRAPPER) Wrapper<T> queryWrapper);

    T selectByCode(@Param("code") String code);

    /**
     * 根据 Wrapper 条件，查询全部记录
     * <p>注意： 只返回第一个字段的值</p>
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
    List<Long> selectLongs(@Param(Constants.WRAPPER) Wrapper<T> queryWrapper);
}

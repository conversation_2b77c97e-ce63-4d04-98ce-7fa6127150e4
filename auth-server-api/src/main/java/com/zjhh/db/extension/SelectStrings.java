package com.zjhh.db.extension;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.zjhh.db.enume.CustomerSqlMethod;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2021/4/7
 */
public class SelectStrings extends AbstractMethod {

    @Serial
    private static final long serialVersionUID = -8071993788930385023L;

    public SelectStrings() {
        this("selectStrings");
    }

    public SelectStrings(String name) {
        super(name);
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        CustomerSqlMethod sqlMethod = CustomerSqlMethod.SELECT_STRINGS;
        String sql = String.format(sqlMethod.getSql(), sqlFirst(), sqlSelectObjsColumns(tableInfo),
                tableInfo.getTableName(), sqlWhereEntityWrapper(true, tableInfo), sqlComment());
        SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, modelClass);
        return this.addSelectMappedStatementForOther(mapperClass, sqlMethod.getMethod(), sqlSource, Object.class);
    }
}

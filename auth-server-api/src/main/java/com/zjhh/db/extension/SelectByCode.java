package com.zjhh.db.extension;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.zjhh.db.enume.CustomerSqlMethod;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

import java.io.Serial;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/4/7
 */
public class SelectByCode extends AbstractMethod {

    @Serial
    private static final long serialVersionUID = -5851740732416565745L;

    public SelectByCode() {
        this("selectByCode");
    }

    public SelectByCode(String name) {
        super(name);
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        CustomerSqlMethod sqlMethod = CustomerSqlMethod.SELECT_BY_CODE;
        String sql = String.format(sqlMethod.getSql(), sqlSelectColumns(tableInfo, false),
                tableInfo.getTableName());
        SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, Map.class);
        return this.addSelectMappedStatementForTable(mapperClass, sqlMethod.getMethod(), sqlSource, tableInfo);
    }
}

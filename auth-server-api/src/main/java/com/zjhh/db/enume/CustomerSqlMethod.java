package com.zjhh.db.enume;

/**
 * 自定义 支持 SQL 方法
 *
 * <AUTHOR>
 * @since 2020/6/15 14:12
 */
public enum CustomerSqlMethod {

    /**
     * 根据code查询
     */
    SELECT_BY_CODE("selectByCode", "根据code查询一条数据", "<script>SELECT %s FROM %s WHERE code=#{code}</script>"),

    /**
     * 根据code修改
     */
    UPDATE_BY_CODE("updateByCode", "根据code修改一条数据", "<script>\nUPDATE %s %s WHERE code=#{code}\n</script>"),

    /**
     * 根据code删除
     */
    DELETE_BY_CODE("deleteByCode", "根据code删除一条数据", "<script>\nDELETE FROM %s WHERE code=#{code}\n</script>"),

    /**
     * 查询第一列
     */
    SELECT_STRINGS("selectStrings", "查询满足条件所有数据", "<script>%s SELECT %s FROM %s %s %s\n</script>"),

    /**
     * 查询第一列
     */
    SELECT_LONGS("selectLongs", "查询满足条件所有数据", "<script>%s SELECT %s FROM %s %s %s\n</script>");


    private final String method;

    private final String desc;

    private final String sql;

    CustomerSqlMethod(String method, String desc, String sql) {
        this.method = method;
        this.desc = desc;
        this.sql = sql;
    }

    public String getMethod() {
        return method;
    }

    public String getDesc() {
        return desc;
    }

    public String getSql() {
        return sql;
    }
}

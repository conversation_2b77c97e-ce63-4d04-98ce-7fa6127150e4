package com.zjhh.db.comm;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.zjhh.comm.constant.ValidateConstant;
import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * OraclePageReq
 *
 * <AUTHOR>
 * @date 2021-04-30 2:33 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OraclePageReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 8126022253758156459L;

    @Schema(description = "当前页")
    @NotNull(message = ValidateConstant.CURRENT_NOT_NULL)
    @Min(value = 1, message = ValidateConstant.CURRENT_MIN)
    private Integer current;

    @Schema(description = "每页显示数量")
    @NotNull(message = ValidateConstant.SIZE_NOT_NULL)
    @Min(value = 1, message = ValidateConstant.SIZE_MIN)
    private Integer size;

    @Schema(description = "排序")
    private List<OrderItem> orderItems;

    public Integer getStartNum() {
        return size * (current - 1) + 1;
    }

    public Integer getEndNum() {
        return size * current;
    }

    public <T> Page<T> getPage(Class<T> tClass) {
        Page<T> page = new Page<>();
        page.setCurrent(this.getCurrent());
        page.setSize(this.getSize());
        page.setOrders(this.getOrderItems());
        return page;
    }

}

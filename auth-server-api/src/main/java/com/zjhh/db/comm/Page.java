package com.zjhh.db.comm;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-04-26 22:15
 */
public class Page<T> extends com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> {

    @Serial
    private static final long serialVersionUID = 8080764907607142433L;

    private Boolean hasPrevious;

    private Boolean hasNext;

    public Page() {
        super();
    }

    public Page(long current, long size) {
        super(current, size);
    }

    public Page(long current, long size, long total) {
        super(current, size, total, true);
    }

    public Page(long current, long size, boolean isSearchCount) {
        super(current, size, 0L, isSearchCount);
    }

    public Page(long current, long size, long total, boolean isSearchCount) {
        super(current, size, total, isSearchCount);
    }

    public Boolean getHasPrevious() {
        return super.hasPrevious();
    }

    public Boolean getHasNext() {
        return super.hasNext();
    }


    @JsonIgnore
    @Override
    public List<OrderItem> orders() {
        return super.orders();
    }

    @JsonIgnore
    @Override
    public boolean searchCount() {
        return super.searchCount();
    }

    @Override
    public long getTotal() {
        if (this.getSize() < 0 && !this.getRecords().isEmpty()) {
            return this.getRecords().size();
        }
        return super.getTotal();
    }

    @Override
    public long getPages() {
        if (this.getSize() < 0) {
            return 1;
        }
        return super.getPages();
    }
}

package com.zjhh.db.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.zjhh.db.constant.CommFieldsConstant;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2020/6/12 10:00
 */
@Component
public class MetaObjectHandlerConfig implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime now = LocalDateTime.now();
        if (getFieldValByName(CommFieldsConstant.GMT_CREATE, metaObject) == null) {
            setFieldValByName(CommFieldsConstant.GMT_CREATE, now, metaObject);
        }
        if (getFieldValByName(CommFieldsConstant.GMT_UPDATE, metaObject) == null) {
            setFieldValByName(CommFieldsConstant.GMT_UPDATE, now, metaObject);
        }
        if (getFieldValByName(CommFieldsConstant.VERSION, metaObject) == null) {
            setFieldValByName(CommFieldsConstant.VERSION, 1, metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        setFieldValByName(CommFieldsConstant.GMT_UPDATE, LocalDateTime.now(), metaObject);
    }
}

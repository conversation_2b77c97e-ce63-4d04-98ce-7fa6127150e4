package com.zjhh.db.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.zjhh.db.extension.CustomerSqlInjector;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2020/6/11 12:00
 */
@Slf4j
@Configuration
@MapperScan({"com.zjhh.*.dao.mapper", "com.zjhh.**.dao.mapper", "com.zjhh.***.dao.mapper"})
public class MyBatisPlusConfiguration {

    public static final Map<String, String> TABLE_NAME_MAP = new ConcurrentHashMap<>();

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
        dynamicTableNameInnerInterceptor.setTableNameHandler(((sql, tableName) -> {
            if (TABLE_NAME_MAP.containsKey(tableName)) {
                return TABLE_NAME_MAP.get(tableName);
            }
            return tableName;
        }));
        interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);
//        interceptor.addInnerInterceptor(new DataPermissionInterceptor(new DataPermissionHandlerImpl()));
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        return interceptor;
    }

    @Bean
    public CustomerSqlInjector customerSqlInjector() {
        return new CustomerSqlInjector();
    }

}

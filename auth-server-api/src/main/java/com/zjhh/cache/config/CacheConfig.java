package com.zjhh.cache.config;

import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.alicp.jetcache.autoconfigure.LettuceFactory;
import com.alicp.jetcache.autoconfigure.RedisLettuceAutoConfiguration;
import io.lettuce.core.RedisClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

/**
 * 缓存配置
 *
 * <AUTHOR>
 * @since 2020/6/8 15:03
 */
@EnableMethodCache(basePackages = "com.zjhh")
@EnableCreateCacheAnnotation
@Configuration
public class CacheConfig {

    @Bean(name = "redisClient")
    @DependsOn(RedisLettuceAutoConfiguration.AUTO_INIT_BEAN_NAME)
    public LettuceFactory redisClient() {
        return new LettuceFactory("remote.default", RedisClient.class);
    }
}

package com.zjhh.cache.config;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2021/8/25 15:34
 */
@Data
@Component
public class CacheStore {

    @Resource
    private CacheManager cacheManager;

    private Cache<String, Object> sqlCache;

    @PostConstruct
    public void init() {
        QuickConfig sql = QuickConfig.newBuilder("sql:")
                .expire(Duration.ofSeconds(3600))
                .cacheType(CacheType.REMOTE)
                .syncLocal(true)
                .build();
        sqlCache = cacheManager.getOrCreateCache(sql);
    }
}

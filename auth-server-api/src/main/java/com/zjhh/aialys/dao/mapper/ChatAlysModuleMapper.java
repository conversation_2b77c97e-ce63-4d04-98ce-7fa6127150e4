package com.zjhh.aialys.dao.mapper;

import com.zjhh.aialys.dao.entity.ChatAlysModule;
import com.zjhh.aialys.vo.AlysConfigLeftTreeVo;
import com.zjhh.aialys.vo.AlysConfigVo;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface ChatAlysModuleMapper extends CustomerBaseMapper<ChatAlysModule> {

    /**
     * 左侧树形结构
     *
     * @return
     */
    List<AlysConfigLeftTreeVo> listAlysConfigLeftTree();

    /**
     * 获取配置信息
     *
     * @param id
     * @return
     */
    AlysConfigVo getAlysConfig(@Param("id") String id);
}

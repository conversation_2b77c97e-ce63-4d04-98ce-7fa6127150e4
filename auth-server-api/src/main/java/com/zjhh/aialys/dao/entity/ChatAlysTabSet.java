package com.zjhh.aialys.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("chat_alys_tab_set")
public class ChatAlysTabSet implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String tabId;

    /**
     * 是否传入大模型
     */
    private Boolean inputLlm;

    /**
     * 图表描述
     */
    private String description;

    private String createUser;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}

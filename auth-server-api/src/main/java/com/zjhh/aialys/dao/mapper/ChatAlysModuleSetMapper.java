package com.zjhh.aialys.dao.mapper;

import com.zjhh.aialys.dao.entity.ChatAlysModuleSet;
import com.zjhh.aialys.vo.AlysModuleVo;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 模块设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface ChatAlysModuleSetMapper extends CustomerBaseMapper<ChatAlysModuleSet> {

    /**
     * 获取AI解读模块信息
     *
     * @param component
     * @return
     */
    List<AlysModuleVo> listAlysModule(@Param("component") String component);
}

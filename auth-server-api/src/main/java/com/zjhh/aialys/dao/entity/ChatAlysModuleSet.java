package com.zjhh.aialys.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 模块设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("chat_alys_module_set")
public class ChatAlysModuleSet implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 模块id
     */
    private String moduleId;

    /**
     * 是否开启
     */
    private Boolean opened;

    /**
     * 提示词模板 id
     */
    private String promptTemplateId;

    /**
     * 提示词
     */
    private String prompt;

    /**
     * 大模型 id
     */
    private String llmId;

    private String createUser;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}

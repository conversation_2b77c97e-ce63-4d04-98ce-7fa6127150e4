package com.zjhh.aialys.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("chat_alys_tab")
public class ChatAlysTab implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String moduleId;

    /**
     * 对应页签
     */
    private String tab;

    /**
     * 页签名称
     */
    private String name;

    /**
     * 图表类型
     */
    private String type;

    /**
     * 描述
     */
    private String description;

    private Integer sort;


}

package com.zjhh.aialys.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * ai智能解读-字段
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("chat_alys_column")
public class ChatAlysColumn implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String tabId;

    /**
     * 对应字段
     */
    private String field;

    /**
     * 字段名称
     */
    private String columnName;

    /**
     * 类型：METRIC-指标 DIMENSION-维度  
     */
    private String type;

    /**
     * 单位
     */
    private String unit;

    /**
     * 描述
     */
    private String description;

    private Integer sort;


}

package com.zjhh.aialys.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 字段设置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("chat_alys_column_set")
public class ChatAlysColumnSet implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String columnId;

    /**
     * 字段名称
     */
    private String columnName;

    private String unit;

    /**
     * 描述
     */
    private String description;

    private String createUser;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}

package com.zjhh.aialys.vo;

import cn.hutool.core.util.StrUtil;
import com.zjhh.aialys.enume.TabChartType;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/3 10:14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AlysConfigTabVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 5647187456457374495L;

    private String tabId;

    @Schema(description = "页签编码")
    private String tab;

    @Schema(description = "页签名称")
    private String tabName;

    @Schema(description = "是否传入大模型")
    private Boolean inputLlm;

    @Schema(description = "图表类型")
    private String tabType;

    @Schema(description = "图表类型名称")
    private String tabTypeName;

    @Schema(description = "图表类型名称")
    private String tabDesc;

    @Schema(description = "字段信息")
    private List<AlysConfigColumnVo> columns;

    public String getTabTypeName() {
        if (StrUtil.isBlank(this.tabType)) {
            return null;
        }
        return Enum.valueOf(TabChartType.class, this.tabType).getTitle();
    }
}

package com.zjhh.aialys.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.w3c.dom.stylesheets.LinkStyle;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/3 10:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AlysConfigVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -7411899883939135263L;

    private String id;

    @Schema(description = "所属页面")
    private String page;

    @Schema(description = "图表名称")
    private String moduleName;

    @Schema(description = "是否开启")
    private Boolean opened;

    @Schema(description = "页签")
    private List<AlysConfigTabVo> tabs;

    @Schema(description = "提示词模板id")
    private String promptTemplateId;

    @Schema(description = "提示器")
    private String prompt;

    @Schema(description = "模块编码")
    private String module;
}

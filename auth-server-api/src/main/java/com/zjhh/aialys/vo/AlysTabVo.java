package com.zjhh.aialys.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2025/6/5 14:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AlysTabVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -7909090493632155431L;

    @Schema(description = "页签id")
    private String tabId;

    @Schema(description = "页签标识")
    private String tab;
}

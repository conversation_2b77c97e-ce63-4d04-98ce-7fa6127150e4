package com.zjhh.aialys.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/6/24 17:11
 */
@Data
public class ChatResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -8290837344206756437L;

    private String type;

    private String data;

    private Long timestamp;

    public ChatResponse(String type, String data) {
        this.type = type;
        this.data = data;
        this.timestamp = System.currentTimeMillis();
    }
}

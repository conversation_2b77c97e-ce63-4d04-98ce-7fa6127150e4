package com.zjhh.aialys.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/5 14:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AlysModuleVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -8677696632084529785L;

    @Schema(description = "模块id")
    private String moduleId;

    @Schema(description = "模块标识")
    private String module;

    @Schema(description = "页签")
    private List<AlysTabVo> tabs;
}

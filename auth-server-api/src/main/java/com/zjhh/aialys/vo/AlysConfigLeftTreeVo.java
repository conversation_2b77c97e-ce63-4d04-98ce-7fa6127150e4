package com.zjhh.aialys.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/3 10:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AlysConfigLeftTreeVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -6874701300842408028L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "parentId")
    private String parentId;

    @Schema(description = "是否配置")
    private Boolean configured;

    @Schema(description = "是否打开")
    private Boolean opened;

    @Schema(description = "是否可以选择")
    private Boolean selectable;

    private List<AlysConfigLeftTreeVo> children;
}

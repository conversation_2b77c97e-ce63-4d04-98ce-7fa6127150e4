package com.zjhh.aialys.vo;

import com.zjhh.comm.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.prompt.Prompt;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2025/6/4 17:02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AlysChatVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 7407599045871978225L;

    private ChatClient chatClient;

    private Prompt prompt;
}

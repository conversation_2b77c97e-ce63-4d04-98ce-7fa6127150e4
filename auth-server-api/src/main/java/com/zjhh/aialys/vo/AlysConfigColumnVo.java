package com.zjhh.aialys.vo;

import cn.hutool.core.util.StrUtil;
import com.zjhh.aialys.enume.ColumnType;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2025/6/3 10:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AlysConfigColumnVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 8884291420993813177L;

    private String columnId;

    @Schema(description = "列")
    private String field;

    @Schema(description = "字段名称")
    private String columnName;

    @Schema(description = "类型")
    private String columnType;

    @Schema(description = "类型名称")
    private String columnTypeName;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "描述")
    private String columnDesc;

    public String getColumnTypeName() {
        if (StrUtil.isBlank(this.columnType)) {
            return null;
        }
        return Enum.valueOf(ColumnType.class, this.columnType).getTitle();
    }
}

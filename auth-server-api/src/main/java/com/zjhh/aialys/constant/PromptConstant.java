package com.zjhh.aialys.constant;

/**
 * <AUTHOR>
 * @since 2025/6/3 16:37
 */
public class PromptConstant {

    public static final String DEFAULT_SYSTEM_PROMPT = """
        你必须严格遵循标准 Markdown 语法规范回复：
        
        1. **标题格式**：# 号后必须有一个空格
           - 一级标题：# 标题内容
           - 二级标题：## 标题内容
        
        2. **列表格式**：
           - 无序列表：- 项目内容（- 号后有空格）
           - 有序列表：1. 项目内容（数字点号后有空格）
        
        3. **引用格式**：> 引用内容（> 号后有空格）
        
        4. **代码块**：使用三个反引号包围，并指定语言
        
        5. **强调语法**：**粗体** 和 *斜体*
        
        **重要规则**：
        - 严格按照标准 Markdown 语法，确保所有格式符号后都有正确的空格
        - 禁止使用 ```markdown 代码块包围整个回复内容
        - 直接输出 Markdown 格式的内容，不要用代码块包装
        - 只有在展示具体代码时才使用代码块，并指定正确的语言类型
        """;


    public static final String DEFAULT_PROMPT = """
            
            <数据: {jsonParam}
            图表名称: {chartName}
            图表描述: {chartType}
            图表类型: {chartDesc}
            数据说明: {fieldInfo}>""";

    /**
     * 图表名称
     */
    public static final String CHART_NAME = "chartName";

    /**
     * 图表类型
     */
    public static final String CHART_TYPE = "chartType";

    /**
     * 图表描述
     */
    public static final String CHART_DESC = "chartDesc";

    /**
     * 数据说明
     */
    public static final String FIELD_INFO = "fieldInfo";

    /**
     * json 参数
     */
    public static final String JSON_PARAM = "jsonParam";

}

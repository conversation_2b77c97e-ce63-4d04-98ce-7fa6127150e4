package com.zjhh.aialys.req;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/3 16:02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AiAlysChatReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 5975290990416213325L;

    @Schema(description = "模块id")
    private String moduleId;

    @Schema(description = "页签")
    private List<AiAlysChatTabReq> tabs;
}

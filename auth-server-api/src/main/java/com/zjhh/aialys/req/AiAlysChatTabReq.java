package com.zjhh.aialys.req;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2025/6/4 14:47
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AiAlysChatTabReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 3682221406302396592L;

    private String tabId;

    @Schema(description = "json数据")
    private String json;
}

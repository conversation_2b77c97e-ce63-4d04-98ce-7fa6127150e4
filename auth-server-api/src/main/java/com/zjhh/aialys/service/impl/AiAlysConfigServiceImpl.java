package com.zjhh.aialys.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.aialys.dao.entity.*;
import com.zjhh.aialys.dao.mapper.*;
import com.zjhh.aialys.service.AiAlysConfigService;
import com.zjhh.aialys.vo.AlysConfigLeftTreeVo;
import com.zjhh.aialys.vo.AlysConfigVo;
import com.zjhh.comm.exception.BizException;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/3 10:05
 */
@Service
public class AiAlysConfigServiceImpl implements AiAlysConfigService {

    @Resource
    private ChatAlysModuleMapper chatAlysModuleMapper;

    @Resource
    private ChatAlysLlmSetMapper chatAlysLlmSetMapper;

    @Resource
    private UserSession userSession;

    @Resource
    private ChatAlysModuleSetMapper chatAlysModuleSetMapper;

    @Resource
    private ChatAlysTabSetMapper chatAlysTabSetMapper;

    @Resource
    private ChatAlysColumnSetMapper chatAlysColumnSetMapper;

    @Resource
    private ChatAlysPromptTemplateMapper chatAlysPromptTemplateMapper;


    @Override
    public List<AlysConfigLeftTreeVo> listAlysConfigLeftTree() {
        return createTree(chatAlysModuleMapper.listAlysConfigLeftTree(), "root");
    }

    @Override
    public AlysConfigVo getAlysConfig(String id) {
        return chatAlysModuleMapper.getAlysConfig(id);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void saveAlysConfig(AlysConfigVo configVo) {
        QueryWrapper<ChatAlysLlmSet> wrapper = new QueryWrapper<>();
        wrapper.lambda().last("limit 1");
        ChatAlysLlmSet llmSet = chatAlysLlmSetMapper.selectOne(wrapper);
        if (llmSet == null) {
            throw new BizException("没有默认的大模型！");
        }

        String userCode = userSession.getUserCode();
        LocalDateTime now = LocalDateTime.now();

        QueryWrapper<ChatAlysModuleSet> chatAlysModuleSetWrapper = new QueryWrapper<>();
        chatAlysModuleSetWrapper.lambda().eq(ChatAlysModuleSet::getModuleId, configVo.getId());
        chatAlysModuleSetMapper.delete(chatAlysModuleSetWrapper);

        ChatAlysModuleSet chatAlysModuleSet = new ChatAlysModuleSet();
        chatAlysModuleSet.setId(IdUtil.getSnowflakeNextIdStr());
        chatAlysModuleSet.setModuleId(configVo.getId());
        chatAlysModuleSet.setOpened(configVo.getOpened());
        chatAlysModuleSet.setPromptTemplateId(configVo.getPromptTemplateId());
        chatAlysModuleSet.setPrompt(configVo.getPrompt());
        chatAlysModuleSet.setLlmId(llmSet.getId());
        chatAlysModuleSet.setCreateUser(userCode);
        chatAlysModuleSet.setCreateTime(now);
        chatAlysModuleSet.setUpdateTime(now);
        chatAlysModuleSetMapper.insert(chatAlysModuleSet);

        if (CollUtil.isEmpty(configVo.getTabs())) {
            throw new BizException("页签不能为空！");
        }
        List<ChatAlysTabSet> tabSets = new ArrayList<>();
        List<ChatAlysColumnSet> columnSets = new ArrayList<>();

        configVo.getTabs().forEach(tab -> {
            QueryWrapper<ChatAlysTabSet> chatAlysTabSetWrapper = new QueryWrapper<>();
            chatAlysTabSetWrapper.lambda().eq(ChatAlysTabSet::getTabId, tab.getTabId());
            chatAlysTabSetMapper.delete(chatAlysTabSetWrapper);

            ChatAlysTabSet chatAlysTabSet = new ChatAlysTabSet();
            chatAlysTabSet.setId(IdUtil.getSnowflakeNextIdStr());
            chatAlysTabSet.setTabId(tab.getTabId());
            chatAlysTabSet.setInputLlm(tab.getInputLlm());
            chatAlysTabSet.setDescription(tab.getTabDesc());
            chatAlysTabSet.setCreateUser(userCode);
            chatAlysTabSet.setCreateTime(now);
            chatAlysTabSet.setUpdateTime(now);
            tabSets.add(chatAlysTabSet);

            tab.getColumns().forEach(column -> {
                QueryWrapper<ChatAlysColumnSet> chatAlysColumnSetWrapper = new QueryWrapper<>();
                chatAlysColumnSetWrapper.lambda().eq(ChatAlysColumnSet::getColumnId, column.getColumnId());
                chatAlysColumnSetMapper.delete(chatAlysColumnSetWrapper);

                ChatAlysColumnSet chatAlysColumnSet = new ChatAlysColumnSet();
                chatAlysColumnSet.setId(IdUtil.getSnowflakeNextIdStr());
                chatAlysColumnSet.setColumnId(column.getColumnId());
                chatAlysColumnSet.setColumnName(column.getColumnName());
                chatAlysColumnSet.setUnit(column.getUnit());
                chatAlysColumnSet.setDescription(column.getColumnDesc());
                chatAlysColumnSet.setCreateUser(userCode);
                chatAlysColumnSet.setCreateTime(now);
                chatAlysColumnSet.setUpdateTime(now);
                columnSets.add(chatAlysColumnSet);
            });
        });

        if (CollUtil.isNotEmpty(tabSets)) {
            chatAlysTabSetMapper.insertBatchSomeColumn(tabSets);
        }

        if (CollUtil.isNotEmpty(columnSets)) {
            chatAlysColumnSetMapper.insertBatchSomeColumn(columnSets);
        }
    }

    @Override
    public List<ChatAlysPromptTemplate> listAlysPromptTemplate() {
        return chatAlysPromptTemplateMapper.selectList(null);
    }

    private List<AlysConfigLeftTreeVo> createTree(List<AlysConfigLeftTreeVo> list, String parentId) {
        List<AlysConfigLeftTreeVo> res = list.stream().filter(item -> StrUtil.equals(parentId, item.getParentId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(res)) {
            return res;
        }
        res.forEach(item -> item.setChildren(createTree(list, item.getId())));
        return res;
    }
}

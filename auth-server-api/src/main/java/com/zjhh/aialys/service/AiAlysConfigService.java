package com.zjhh.aialys.service;

import com.zjhh.aialys.dao.entity.ChatAlysPromptTemplate;
import com.zjhh.aialys.vo.AlysConfigLeftTreeVo;
import com.zjhh.aialys.vo.AlysConfigVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/3 10:05
 */
public interface AiAlysConfigService {

    /**
     * 左侧树形结构
     *
     * @return
     */
    List<AlysConfigLeftTreeVo> listAlysConfigLeftTree();

    /**
     * 获取配置信息
     *
     * @param id
     * @return
     */
    AlysConfigVo getAlysConfig(String id);

    /**
     * 保存配置信息
     *
     * @param configVo
     */
    void saveAlysConfig(AlysConfigVo configVo);

    /**
     * 提示词模板
     *
     * @return
     */
    List<ChatAlysPromptTemplate> listAlysPromptTemplate();
}

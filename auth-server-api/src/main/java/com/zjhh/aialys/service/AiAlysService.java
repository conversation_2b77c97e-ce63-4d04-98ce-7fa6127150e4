package com.zjhh.aialys.service;

import com.zjhh.aialys.req.AiAlysChatReq;
import com.zjhh.aialys.vo.AlysChatVo;
import com.zjhh.aialys.vo.AlysModuleVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/3 15:16
 */
public interface AiAlysService {

    /**
     * 聊天
     *
     * @param question
     * @return
     */
    AlysChatVo chat(AiAlysChatReq req);

    /**
     * 获取AI解读模块信息
     *
     * @param component
     * @return
     */
    List<AlysModuleVo> listAlysModule(String component);
}

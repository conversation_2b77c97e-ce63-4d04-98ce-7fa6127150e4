package com.zjhh.aialys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.aialys.config.AiAlysChatComponent;
import com.zjhh.aialys.constant.PromptConstant;
import com.zjhh.aialys.dao.entity.ChatAlysModuleSet;
import com.zjhh.aialys.dao.mapper.ChatAlysModuleSetMapper;
import com.zjhh.aialys.req.AiAlysChatReq;
import com.zjhh.aialys.req.AiAlysChatTabReq;
import com.zjhh.aialys.service.AiAlysConfigService;
import com.zjhh.aialys.service.AiAlysService;
import com.zjhh.aialys.vo.AlysChatVo;
import com.zjhh.aialys.vo.AlysConfigVo;
import com.zjhh.aialys.vo.AlysModuleVo;
import com.zjhh.comm.exception.BizException;
import jakarta.annotation.Resource;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/6/3 15:16
 */
@Service
public class AiAlysServiceImpl implements AiAlysService {

    @Resource
    private ChatAlysModuleSetMapper chatAlysModuleSetMapper;

    @Resource
    private AiAlysConfigService aiAlysConfigService;

    @Resource
    private AiAlysChatComponent aiAlysChatComponent;


    @Override
    public AlysChatVo chat(AiAlysChatReq req) {
        QueryWrapper<ChatAlysModuleSet> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ChatAlysModuleSet::getModuleId, req.getModuleId());
        ChatAlysModuleSet chatAlysModuleSet = chatAlysModuleSetMapper.selectOne(wrapper);
        if (chatAlysModuleSet == null || !chatAlysModuleSet.getOpened()) {
            throw new BizException("该图表暂未开启AI解读功能！");
        }
        AlysConfigVo alysConfigVo = aiAlysConfigService.getAlysConfig(req.getModuleId());
        StringBuilder promptStr = new StringBuilder(alysConfigVo.getPrompt());
        alysConfigVo.getTabs().forEach(tab -> {
            if (!tab.getInputLlm()) {
                return;
            }
            PromptTemplate promptTemplate = new PromptTemplate(PromptConstant.DEFAULT_PROMPT);
            String json = "";
            for (AiAlysChatTabReq reqTab : req.getTabs()) {
                if (reqTab.getTabId().equals(tab.getTabId())) {
                    json = reqTab.getJson();
                    break;
                }
            }
            StringBuilder sb = new StringBuilder();
            tab.getColumns().forEach(column -> {
                sb.append("<")
                        .append(column.getField())
                        .append(":")
                        .append("字段名称 ")
                        .append(column.getColumnName())
                        .append(",")
                        .append("类型 ")
                        .append(column.getColumnTypeName());
                if (column.getUnit() != null) {
                    sb.append(",")
                            .append("单位 ")
                            .append(column.getUnit());
                }
                sb.append(",")
                        .append("字段描述 ")
                        .append(column.getColumnDesc())
                        .append(">");

            });
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(PromptConstant.JSON_PARAM, json);
            paramMap.put(PromptConstant.CHART_NAME, tab.getTabName());
            paramMap.put(PromptConstant.CHART_DESC, tab.getTabDesc());
            paramMap.put(PromptConstant.CHART_TYPE, tab.getTabTypeName());
            paramMap.put(PromptConstant.FIELD_INFO, sb.toString());
            promptStr.append("/n")
                    .append(promptTemplate.render(paramMap));
        });
        UserMessage userMessage = new UserMessage(promptStr.toString());

        AlysChatVo chatVo = new AlysChatVo();
        chatVo.setPrompt(new Prompt(userMessage));
        chatVo.setChatClient(aiAlysChatComponent.getChatClient(chatAlysModuleSet.getLlmId()));
        return chatVo;
    }

    public List<AlysModuleVo> listAlysModule(String component) {
        return chatAlysModuleSetMapper.listAlysModule(component);
    }
}

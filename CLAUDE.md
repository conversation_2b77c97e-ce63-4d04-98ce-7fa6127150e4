# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于Spring Boot 3的企业级Java平台项目，当前版本为springboot3-1.2.9，使用Java 21。项目主要提供认证服务和基础平台功能。

## 构建和运行命令

### 基础构建命令
```bash
# 清理编译
mvn clean compile

# 打包（跳过测试）
mvn clean package -Dmaven.test.skip=true

# 运行测试
mvn test

# 启动应用（在auth-server模块目录下）
java -jar target/auth-server-springboot3-1.2.9.jar

# 或使用Maven插件启动
mvn spring-boot:run
```

### 开发调试
```bash
# 开发模式启动（with debug）
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar target/auth-server-springboot3-1.2.9.jar
```

## 架构结构

### 模块划分
- **auth-server-api**: API接口定义模块，包含实体类、DTO、Service接口
- **auth-server**: Web服务实现模块，包含Controller、配置类、应用启动类

### 核心技术栈
- **Spring Boot 3.4.3** + **Java 21**
- **MyBatis-Plus ********** - ORM框架
- **Sa-Token 1.40.0** - 权限认证框架
- **JetCache 2.7.7** - 分布式缓存
- **Flowable 6.5.0** - 工作流引擎
- **Spring AI 1.0.0** - AI集成框架
- **Druid** - 数据库连接池，支持MySQL/PostgreSQL/Oracle

### 主要业务模块
- **system**: 系统管理（用户、角色、权限、字典等）
- **user**: 用户管理和认证
- **workflow**: 工作流引擎集成
- **aialys**: AI分析功能
- **comm**: 通用工具组件（加密、文件处理、导出等）

## 关键配置说明

### 数据库配置
- 支持多数据源动态切换
- 使用Jasypt进行配置文件加密
- 默认支持MySQL，可扩展PostgreSQL/Oracle

### 缓存配置
- JetCache提供方法级缓存
- Redis作为分布式缓存存储
- 支持本地缓存+远程缓存组合

### 认证授权
- Sa-Token实现JWT认证
- 支持多点登录控制
- 基于注解的权限验证：`@SaCheckPermission`

## 开发注意事项

### 包结构约定
```
com.zjhh/
├── comm/           # 通用组件（工具类、配置、异常处理）
├── system/         # 系统管理模块
├── user/           # 用户模块
├── workflow/       # 工作流模块
├── aialys/         # AI分析模块
├── cache/          # 缓存配置
├── db/             # 数据库配置
└── web/            # Web层配置
```

### 数据库操作
- 继承`BaseEntity`获得通用字段（创建时间、更新时间等）
- 使用`@DS("数据源名")`注解切换数据源
- Service层继承`IService<Entity>`获得基础CRUD方法

### 权限控制
- Controller方法使用`@SaCheckPermission("权限码")`
- 获取当前用户：`StpUtil.getLoginIdAsString()`
- 检查权限：`StpUtil.hasPermission("权限码")`

### 文件导出
- 使用`AsposeUtil`处理Office文档转换
- 使用`EasyExcel`处理Excel导入导出
- 导出字段配置在实体类的`@ExcelProperty`注解中

### 工作流集成
- Flowable引擎提供BPMN 2.0支持
- 流程定义位于`resources/processes/`目录
- 自定义任务监听器位于`workflow.listener`包

### AI功能
- Spring AI框架集成，当前配置为none
- AI分析功能在`aialys`模块中实现
- 相关配置在`application-ai.yml`中

## 测试指南

### 单元测试
- 测试类位于`src/test/java`目录
- 使用`@SpringBootTest`进行集成测试
- 数据库测试使用`@Transactional`保证数据回滚

### API测试
- 启动应用后访问：`http://localhost:8080/doc.html` (Knife4j文档)
- 使用Knife4j进行API测试和调试

## 配置文件说明

- `application.yml` - 主配置文件
- `application-dev.yml` - 开发环境配置
- `application-prod.yml` - 生产环境配置
- `application-ai.yml` - AI相关配置
- 敏感配置使用Jasypt加密，密钥通过环境变量或启动参数传入
# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)

# 忽略构建输出目录
target/
**/target/
build/
**/build/
out/
**/out/

# 忽略日志文件和目录
log/
logs/
*.log
spy.log

# 忽略IDE和编辑器配置
.idea/
.vscode/
*.iml
*.iws
*.ipr
.classpath
.project
.settings/
.springBeans
.sts4-cache/
.factorypath
.apt_generated/
.nb-gradle/
nbproject/
nbbuild/
nbdist/

# 忽略系统文件
.DS_Store
Thumbs.db

# 忽略Maven和Gradle文件
.mvn/
!.mvn/wrapper/maven-wrapper.jar
.gradle/
gradle/
gradlew
gradlew.bat

# 忽略临时文件
*.tmp
*.bak
*.swp
*.swo
*~

# 忽略大型数据文件
*.csv
*.xlsx
*.xls
*.parquet
*.sqlite
*.db

# 忽略压缩文件
*.zip
*.tar.gz
*.tgz
*.rar
*.7z
*.jar
!**/lib/*.jar

# 忽略配置文件（可能包含敏感信息）
**/application-*.yml
**/application-*.properties
**/*.env
**/*.p12
**/*.jks
**/*.keystore

# 忽略Node.js相关文件（如果项目中有前端部分）
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnp/
.pnp.js
.npm/

# 忽略Python相关文件（如果项目中有Python脚本）
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.venv/
venv/
ENV/
env/

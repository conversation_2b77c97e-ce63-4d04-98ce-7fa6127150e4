---
description: 
globs: 
alwaysApply: false
---
# 技术栈详解

本项目使用了多种现代Java技术和框架：

## 核心框架

- **Spring Boot 3.4.3**: 应用程序的基础框架，提供自动配置和依赖管理。
- **MyBatis-Plus **********: 增强的MyBatis框架，简化数据库操作。
- **Sa-Token 1.40.0**: 轻量级Java权限认证框架，用于处理认证和授权。

## 数据库相关

- **MySQL**: 主要数据库。
- **Druid 1.2.24**: 阿里巴巴开源的数据库连接池，提供监控功能。
- **Dynamic DataSource**: 支持多数据源管理。

## 缓存

- **JetCache 2.7.7**: 分布式缓存框架，支持方法级缓存。
- **Redis**: 用于分布式缓存存储。

## 工具库

- **Hutool 5.8.25**: 国产工具包，提供各种实用工具方法。
- **Fastjson2 2.0.56**: 阿里巴巴的JSON处理库。
- **Lombok**: 简化Java代码，自动生成getter、setter等。

## API文档

- **Knife4j 4.6.0**: 基于Swagger的API文档工具，提供更美观的界面。

## 其他

- **easyexcel**: 阿里巴巴开源的Excel处理库。
- **aspose**: 用于处理Word、Excel、PowerPoint等Office文档。

---
description: 
globs: 
alwaysApply: false
---
# 项目结构

本项目是一个基于Spring Boot的平台，主要包含以下模块：

- [auth-server](mdc:auth-server): 认证服务器模块
- [auth-server-api](mdc:auth-server-api): 认证服务API模块

主要入口点是[WebBaseApplication.java](mdc:auth-server/src/main/java/com/zjhh/web/WebBaseApplication.java)。

## 技术栈

该项目使用了以下主要技术：

- Spring Boot 3.4.3
- MyBatis-Plus ********
- Sa-Token 1.40.0 (认证框架)
- JetCache 2.7.7 (缓存框架)
- Knife4j 4.6.0 (API文档)
- Hutool 5.8.25 (工具库)
- MySQL (数据库)

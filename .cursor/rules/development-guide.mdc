---
description: 
globs: 
alwaysApply: false
---
# 开发指南

## 项目结构约定

- 遵循标准Maven项目结构
- 使用模块化设计，将API接口和实现分离
- 包结构遵循`com.zjhh`命名空间

## 编码规范

- 使用Lombok简化实体类编写
- 控制器类使用`@RestController`注解
- API接口使用Swagger注解进行文档化
- 使用Sa-Token进行权限控制，接口权限通过注解方式配置

## 缓存使用

通过JetCache框架实现方法级缓存：

```java
@Cached(name = "userCache", key = "#userId", expire = 3600)
public UserDTO getUserById(String userId) {
    // 查询逻辑
}
```

## 数据库操作

使用MyBatis-Plus进行数据库操作：

```java
// 实体类定义
@Data
@TableName("t_user")
public class User {
    @TableId
    private String id;
    private String username;
    // 其他字段
}

// Mapper定义
public interface UserMapper extends BaseMapper<User> {
    // 自定义方法
}
```

## API文档

访问`http://localhost:{port}/{path}/doc.html`查看Swagger文档。

## 最佳实践

- 使用统一的返回格式封装API响应
- 遵循RESTful API设计原则
- 事务处理使用Spring的`@Transactional`注解
- 使用全局异常处理统一处理异常

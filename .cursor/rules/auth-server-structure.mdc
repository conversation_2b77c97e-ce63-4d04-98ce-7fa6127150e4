---
description: 
globs: 
alwaysApply: false
---
# Auth-Server 模块结构

auth-server是认证服务器模块，负责处理身份验证和授权功能。

## 包结构

```
com.zjhh.web
├── WebBaseApplication.java   # 应用程序入口点
├── config/                   # 配置类
├── controller/               # 控制器
└── interceptor/              # 拦截器
```

## 主要组件

- [WebBaseApplication.java](mdc:auth-server/src/main/java/com/zjhh/web/WebBaseApplication.java): 应用程序的主入口点，初始化Spring Boot应用。
- 使用Sa-Token进行认证和授权处理。
- 使用JetCache进行缓存管理，支持方法级缓存。

## 服务访问

- 本地访问: http://localhost:{port}/{path}/
- Swagger文档: http://localhost:{port}/{path}/doc.html

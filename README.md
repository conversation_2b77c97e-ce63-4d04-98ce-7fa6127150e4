# Base Platform 基础平台

企业级基础平台，为其他业务系统提供统一的认证、授权、系统管理等基础功能。

## 🚀 项目概述

本项目是基于Spring Boot 3的企业级基础平台，提供以下核心功能：

- **统一认证授权** - 基于Sa-Token的JWT认证体系
- **系统管理** - 用户、角色、权限、字典等基础数据管理
- **工作流引擎** - 集成Flowable提供业务流程支持
- **AI分析功能** - 集成Spring AI框架
- **多数据源支持** - 支持MySQL、PostgreSQL、Oracle
- **分布式缓存** - 基于JetCache + Redis的缓存方案

## 📋 技术栈

| 组件 | 版本 | 说明 |
|------|------|------|
| Java | 21 | 基础运行环境 |
| Spring Boot | 3.4.3 | 应用框架 |
| MyBatis-Plus | 3.5.10.1 | ORM框架 |
| Sa-Token | 1.40.0 | 认证授权框架 |
| JetCache | 2.7.7 | 分布式缓存 |
| Flowable | 6.5.0 | 工作流引擎 |
| Spring AI | 1.0.0 | AI集成框架 |
| Knife4j | 4.6.0 | API文档工具 |
| Druid | 1.2.24 | 数据库连接池 |

## 🏗️ 项目结构

```
base-platform/
├── auth-server-api/     # API接口定义模块
│   ├── aialys/         # AI分析功能
│   ├── comm/           # 通用组件
│   ├── system/         # 系统管理
│   ├── user/           # 用户管理
│   └── workflow/       # 工作流
└── auth-server/        # Web服务实现模块
    ├── config/         # 配置类
    ├── controller/     # 控制器层
    └── interceptor/    # 拦截器
```

## 🛠️ 快速开始

### 环境要求

- Java 21+
- Maven 3.6+
- MySQL 8.0+ / PostgreSQL 12+ / Oracle 19c+
- Redis 6.0+

### 构建运行

```bash
# 克隆项目
git clone <repository-url>
cd base-platform

# 编译项目
mvn clean compile

# 打包项目
mvn clean package -Dmaven.test.skip=true

# 启动应用
java -jar auth-server/target/auth-server-springboot3-1.2.9.jar
```

### 配置说明

1. **数据库配置** - 修改 `application-dev.yml` 中的数据库连接信息
2. **Redis配置** - 配置Redis连接参数
3. **加密配置** - 使用Jasypt加密敏感配置信息

### 访问地址

- 应用地址：http://localhost:8080
- API文档：http://localhost:8080/doc.html
- 健康检查：http://localhost:8080/actuator/health

## 📚 使用指南

### 认证授权

```java
// 登录验证
@SaCheckLogin
public void userInfo() {
    String userId = StpUtil.getLoginIdAsString();
}

// 权限验证
@SaCheckPermission("user:list")
public void getUserList() {
    // 业务逻辑
}
```

### 多数据源使用

```java
@Service
public class UserService {
    
    @DS("master")
    public User getMasterUser(Long id) {
        // 使用主数据源
    }
    
    @DS("slave")
    public List<User> getSlaveUsers() {
        // 使用从数据源
    }
}
```

### 缓存使用

```java
@Service
public class DictService {
    
    @Cached(name = "dict:", key = "#type", expire = 3600)
    public List<Dict> getDictByType(String type) {
        // 缓存1小时
    }
}
```

## 🔧 开发指南

### 代码规范

- 遵循阿里巴巴Java开发规范
- 使用Lombok简化代码
- Controller层统一返回`R<T>`结果包装类
- Service层继承`IService<Entity>`获得基础CRUD方法

### 数据库设计

- 实体类继承`BaseEntity`获得通用字段
- 使用逻辑删除，避免物理删除数据
- 字段命名使用下划线分隔

### API设计

- RESTful风格接口设计
- 统一异常处理和返回格式
- 使用Knife4j生成API文档

## 🏷️ 版本历史

### v1.2.9 (当前版本)
- 升级到Spring Boot 3.4.3
- 配置文件全面加密
- 优化多数据源配置

### v1.2.8
- 升级Spring AI到稳定版本
- 剔除暂不使用的spring-ai-alibaba


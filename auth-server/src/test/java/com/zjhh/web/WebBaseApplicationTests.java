package com.zjhh.web;

import com.zjhh.user.dao.entity.Menu;
import com.zjhh.user.dao.mapper.MenuMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
class WebBaseApplicationTests {

    @Resource
    private MenuMapper menuMapper;

    @Test
    void contextLoads() {
        List<Menu> list = menuMapper.selectList(null);
        list.forEach(menu -> {
            Menu newMenu = new Menu();
            newMenu.setId(menu.getId());
            newMenu.setPath(menu.getRouter().toLowerCase() + System.currentTimeMillis());
            menuMapper.updateById(newMenu);
        });
    }

}

package com.zjhh.impl;

import cn.hutool.system.SystemUtil;
import cn.hutool.system.oshi.OshiUtil;
import org.junit.jupiter.api.Test;


/**
 * <AUTHOR>
 * @since 2021/3/4 17:13
 */
class MonitorServiceImplTest {


    @Test
    void getRedisInfo() {
        System.out.println(SystemUtil.getJvmInfo());
        System.out.println(SystemUtil.getJvmSpecInfo());
        System.out.println(SystemUtil.getJavaSpecInfo());
        System.out.println(SystemUtil.getJavaInfo());
        System.out.println(SystemUtil.getJavaRuntimeInfo());
        System.out.println(SystemUtil.getUserInfo());
        System.out.println(SystemUtil.getHostInfo());
        System.out.println(SystemUtil.getRuntimeInfo());
        System.out.println(SystemUtil.getOsInfo());
        System.out.println(OshiUtil.getCpuInfo());
    }
}
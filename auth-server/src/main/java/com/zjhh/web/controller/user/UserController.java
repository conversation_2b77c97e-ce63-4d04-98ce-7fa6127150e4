package com.zjhh.web.controller.user;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.system.annotation.ApiLog;
import com.zjhh.user.dao.entity.User;
import com.zjhh.user.request.*;
import com.zjhh.user.service.UserService;
import com.zjhh.user.service.ZwddApiService;
import com.zjhh.user.vo.*;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2020/6/15 15:49
 */
@Tag(name = "系统管理-用户管理")
@ApiSupport(order = 100)
@SaCheckLogin
@RequestMapping("user")
@RestController
public class UserController extends BaseController {

    @Resource
    private UserService userService;
    @Resource
    private ZwddApiService zwddApiService;

    @Operation(summary = "1.用户列表")
    @ApiOperationSupport(order = 1)
    @SaCheckPermission(value = {"organization", "super-manager"}, mode = SaMode.OR)
    @PostMapping("page")
    public ReData<Page<UserVo>> pageUser(@RequestBody @Validated PageUserReq req) {
        return ReData.success(userService.pageUser(req));
    }

    @Operation(summary = "2.新增用户")
    @ApiOperationSupport(order = 2)
    @ApiLog("新增用户")
    @SaCheckPermission(value = {"organization", "super-manager"}, mode = SaMode.OR)
    @PostMapping("add")
    public ReData<String> addUser(@RequestBody @Validated AddUserReq req) {
        userService.addUser(req);
        return ReData.success();
    }

    @Operation(summary = "3.用户详情")
    @ApiOperationSupport(order = 3)
    @SaCheckPermission(value = {"organization", "super-manager"}, mode = SaMode.OR)
    @PostMapping("get")
    public ReData<UserDetailVo> getUser(@RequestBody @Validated UserIdReq req) {
        return ReData.success(userService.getUserDetail(req.getUserId()));
    }

    @Operation(summary = "4.修改用户")
    @ApiOperationSupport(order = 4)
    @ApiLog("修改用户")
    @SaCheckPermission(value = {"organization", "super-manager"}, mode = SaMode.OR)
    @PostMapping("update")
    public ReData<String> updateUser(@RequestBody @Validated UpdateUserReq req) {
        userService.updateUser(req);
        return ReData.success();
    }

    @Operation(summary = "5.删除用户")
    @ApiOperationSupport(order = 5)
    @ApiLog("删除用户")
    @SaCheckPermission(value = {"organization", "super-manager"}, mode = SaMode.OR)
    @PostMapping("delete/batch")
    public ReData<String> deleteBatchUser(@RequestBody @Validated ListUserIdReq req) {
        userService.deleteBatchUser(req.getUserIds());
        return ReData.success();
    }

    @Operation(summary = "6.锁定用户")
    @ApiOperationSupport(order = 6)
    @ApiLog("锁定用户")
    @SaCheckPermission(value = {"organization", "super-manager"}, mode = SaMode.OR)
    @PostMapping("lock")
    public ReData<String> lockUser(@RequestBody @Validated UserIdReq req) {
        userService.lockUser(req.getUserId());
        return ReData.success();
    }

    @Operation(summary = "7.解锁用户")
    @ApiOperationSupport(order = 7)
    @ApiLog("解锁用户")
    @SaCheckPermission(value = {"organization", "super-manager"}, mode = SaMode.OR)
    @PostMapping("unlock")
    public ReData<String> unlockUser(@RequestBody @Validated UserIdReq req) {
        userService.unlockUser(req.getUserId());
        return ReData.success();
    }

    @Operation(summary = "8.重置用户密码")
    @ApiOperationSupport(order = 8)
    @ApiLog("重置用户密码")
    @SaCheckPermission(value = {"organization", "super-manager"}, mode = SaMode.OR)
    @PostMapping("password/reset")
    public ReData<String> resetPassword(@RequestBody @Validated ResetPasswordReq req) {
        userService.resetPassword(req);
        return ReData.success();
    }

    @Operation(summary = "9.登录名确认")
    @ApiOperationSupport(order = 9)
    @SaCheckPermission(value = {"organization", "super-manager"}, mode = SaMode.OR)
    @PostMapping("loginName/check")
    public ReData<Boolean> checkLoginName(@RequestBody @Validated LoginNameReq req) {
        return ReData.success(userService.checkLoginName(req.getLoginName()));
    }

    @Operation(summary = "10.修改密码")
    @ApiOperationSupport(order = 10)
    @ApiLog("修改密码")
    @SaCheckRole("common-user")
    @PostMapping("password/update")
    public ReData<String> updatePassword(@RequestBody @Validated UpdatePasswordReq req) {
        userService.updatePassword(req);
        return ReData.success();
    }

    @Operation(summary = "11.保存主题")
    @ApiOperationSupport(order = 11)
    @ApiLog("保存主题")
    @SaCheckRole("common-user")
    @PostMapping("save/theme")
    public ReData<String> saveTheme(@RequestBody @Validated SaveUserThemeReq req) {
        userService.saveTheme(req.getThemeConfig());
        return ReData.success();
    }

    @Operation(summary = "12.获取个人中心")
    @ApiOperationSupport(order = 12)
    @SaCheckRole("common-user")
    @PostMapping("get/profile")
    public ReData<User> getUserProfile() {
        return ReData.success(userService.getUserProfile());
    }

    @Operation(summary = "13.保存个人中心")
    @ApiOperationSupport(order = 13)
    @PostMapping("save/profile")
    @SaCheckRole("common-user")
    @ApiLog("保存个人中心")
    public ReData<LoginVo> saveUserProfile(@RequestBody @Validated SaveProfileReq req) {
        return ReData.success(userService.saveUserProfile(req));
    }

    @Operation(summary = "14.同步浙政钉")
    @ApiOperationSupport(order = 14)
    @PostMapping("synchronism/zwdd")
    @SaCheckPermission(value = {"organization", "super-manager"}, mode = SaMode.OR)
    @ApiLog("同步浙政钉")
    public ReData<SyncZwddInfoVO> syncZwddInfoVO(@RequestBody @Validated ListUserIdReq req) {
        return ReData.success(zwddApiService.listEmployeeByUserIds(req.getUserIds()));
    }

    @Operation(summary = "15.修改移动端信息")
    @ApiOperationSupport(order = 15)
    @SaCheckRole("common-user")
    @PostMapping("update/mobile/user")
    public ReData<String> updateMobileUser(@RequestBody @Validated UpdateMobileUserReq req) {
        userService.updateMobileUser(req);
        return ReData.success();
    }

    @Operation(summary = "16.移动端用户详情")
    @ApiOperationSupport(order = 16)
    @SaCheckRole("common-user")
    @PostMapping("get/mobile/user/detail")
    public ReData<MobileUserDetailVo> getMobileUserDetail() {
        return ReData.success(userService.getMobileUserDetail());
    }

    @Operation(summary = "17.用户列表导出")
    @ApiOperationSupport(order = 17)
    @SaCheckPermission(value = {"organization", "super-manager"}, mode = SaMode.OR)
    @PostMapping("export/user")
    public void exportUser(@RequestBody @Validated PageUserReq req, HttpServletResponse response) throws IOException {
        Page<UserVo> page = userService.pageUser(req);
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(
                        new ThreeTableVo.TopTitleOneVo("用户列表", null, null),
                        new ThreeTableVo.MidContentTwoVo(UserVo.class, page.getRecords())
                ));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, "用户列表", "用户列表");
    }
}

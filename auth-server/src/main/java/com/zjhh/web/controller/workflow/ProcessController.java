package com.zjhh.web.controller.workflow;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.response.ReData;
import com.zjhh.web.base.BaseController;
import com.zjhh.workflow.request.AddModelReq;
import com.zjhh.workflow.service.IFlowableBpmnModelService;
import com.zjhh.workflow.service.IFlowableDeployService;
import com.zjhh.workflow.service.IFlowableProcessDefinitionService;
import com.zjhh.workflow.service.IFlowableProcessInstanceService;
import com.zjhh.workflow.vo.ProcessDefinitionVo;
import com.zjhh.workflow.vo.StartProcessInstanceVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 部署流程定义
 * </p>
 */

@Tag(name = "流程定义")
@ApiSupport(order = 101)
@SaCheckLogin
@RestController
@RequestMapping("flow/process")
public class ProcessController extends BaseController {


    @Resource
    private IFlowableDeployService iFlowableDeployService;

    @Resource
    private IFlowableProcessDefinitionService iFlowableProcessDefinitionService;

    @Resource
    private IFlowableBpmnModelService iFlowableBpmnModelService;


    @Resource
    private IFlowableProcessInstanceService iFlowableProcessInstanceService;

    @Resource
    private TaskService taskService;

    @PostMapping("/deploy")
    @Operation(summary = "部署")
    public ReData<String> deploy(AddModelReq req) {
        return iFlowableDeployService.deploy(req);
    }

    @PostMapping("/info")
    @Operation(summary = "根据流程id获取流程信息")
    public ReData<ProcessDefinitionVo> getInfo(String processID) {
        return ReData.success(iFlowableProcessDefinitionService.getById(processID));
    }

    @PostMapping("/start")
    @Operation(summary = "根据流程id获取流程信息")
    public ReData<Integer> startProcess(String processID, String userCode) {
        StartProcessInstanceVo req = new StartProcessInstanceVo();
        req.setProcessDefinitionKey(processID);
        req.setBusinessKey("test");
        req.setFormName("监督建议");
        req.setVariables(new HashMap<>());
        iFlowableProcessInstanceService.startProcessInstanceByKey(req, userCode);
        return ReData.success();
    }


    @PostMapping("/delete")
    @Operation(summary = "删除")
    public ReData<Integer> delete(String processID) {
        iFlowableProcessInstanceService.deleteProcessInstanceById(processID);
        return ReData.success();
    }

    @PostMapping("/taskId")
    @Operation(summary = "ceshi")
    public ReData<Integer> taskId(String processID) {
//		Map<String, Object> variables = new HashMap<>();
//		Task task = taskService.createTaskQuery().processInstanceId("7970f7fe78d011ec92fdf2913f8b9936").singleResult();
//		Map<String,Object> map =  taskService.getVariables(task.getId());
//		Map<String,Object> map1 = taskService.getVariablesLocal(task.getId());
        List<FlowNode> list = iFlowableBpmnModelService.findFlowNodes(processID);
//		variables.put("deptNo","330402");
//		taskService.complete(task.getId(),variables);
        return ReData.success();
    }


    @PostMapping("/complete")
    @Operation(summary = "完成")
    public ReData<Integer> complete(String processID) {
        Map<String, Object> variables = new HashMap<>();

        variables.put("deptNo", "33040200_101");

        Task task = taskService.createTaskQuery().processInstanceId("bfa6fa5678ce11ec9f37f2913f8b9936").singleResult();
        taskService.complete(task.getId(), variables);
        return ReData.success();
    }

}


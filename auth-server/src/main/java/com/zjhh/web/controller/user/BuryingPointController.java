package com.zjhh.web.controller.user;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.user.request.AddPointReq;
import com.zjhh.user.request.PageBuryingPointReq;
import com.zjhh.user.service.BuryingPointService;
import com.zjhh.user.vo.BuryingPointVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2022/6/6 14:30
 */
@Slf4j
@Tag(name = "系统管理-数据埋点")
@SaCheckLogin
@RestController
@RequestMapping("burying/point")
public class BuryingPointController extends BaseController {

    @Resource
    private BuryingPointService buryingPointService;

    @Operation(summary = "1.添加埋点")
    @ApiOperationSupport(order = 1)
    @PostMapping("add/point")
    public ReData<String> addPoint(@RequestBody @Validated AddPointReq req, HttpServletRequest request) {
        buryingPointService.addPoint(req, request);
        return ReData.success();
    }

    @Operation(summary = "2.埋点信息分页")
    @ApiOperationSupport(order = 2)
    @PostMapping("page")
    public ReData<Page<BuryingPointVo>> page(@RequestBody @Validated PageBuryingPointReq req) {
        return ReData.success(buryingPointService.page(req));
    }

    @Operation(summary = "3.导出埋点信息")
    @ApiOperationSupport(order = 3)
    @PostMapping("export")
    public void export(@RequestBody @Validated PageBuryingPointReq req, HttpServletResponse response) throws IOException {
        Page<BuryingPointVo> page = buryingPointService.page(req);
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(
                        new ThreeTableVo.TopTitleOneVo("数据埋点", null, null),
                        new ThreeTableVo.MidContentTwoVo(BuryingPointVo.class, page.getRecords())
                ));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, "数据埋点", "数据埋点");
    }
}

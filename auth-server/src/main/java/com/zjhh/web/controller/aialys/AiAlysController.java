package com.zjhh.web.controller.aialys;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.aialys.req.AiAlysChatReq;
import com.zjhh.aialys.service.AiAlysService;
import com.zjhh.aialys.vo.AlysChatVo;
import com.zjhh.aialys.vo.AlysModuleVo;
import com.zjhh.aialys.vo.ChatResponse;
import com.zjhh.comm.response.ReData;
import com.zjhh.user.request.ComponentReq;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/4 16:04
 */
@Tag(name = "AI解读")
@ApiSupport(order = 203)
@SaCheckLogin
@RequestMapping("ai/alys/chat")
@RestController
public class AiAlysController extends BaseController {

    @Resource
    private AiAlysService aiAlysService;

    @Operation(summary = "Ai解读")
    @PostMapping(value = "chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<ChatResponse>> chat(@RequestBody @Validated AiAlysChatReq req, HttpServletResponse response) {
        AlysChatVo chatVo = aiAlysService.chat(req);
        return chatVo.getChatClient().prompt(chatVo.getPrompt())
                .stream()
                .content()
                .map(content -> {
                    ChatResponse chatResponse = new ChatResponse("content", content);

                    return ServerSentEvent.<ChatResponse>builder()
                            .event("message")
                            .data(chatResponse)
                            .build();
                });
    }

    @Operation(summary = "获取AI解读模块信息")
    @PostMapping("list/alys/module")
    public ReData<List<AlysModuleVo>> listAlysModule(@RequestBody @Validated ComponentReq req) {
        return ReData.success(aiAlysService.listAlysModule(req.getComponent()));
    }
}

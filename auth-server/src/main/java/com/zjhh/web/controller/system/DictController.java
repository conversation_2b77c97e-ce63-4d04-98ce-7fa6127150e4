package com.zjhh.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.system.annotation.ApiLog;
import com.zjhh.system.dao.entity.Dict;
import com.zjhh.system.request.PageSearchReq;
import com.zjhh.system.request.UpdateDictReq;
import com.zjhh.system.service.DictService;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2021/3/16 17:33
 */
@Tag(name = "系统管理-基础数据")
@ApiSupport(order = 105)
@SaCheckLogin
@RequestMapping("dict")
@RestController
public class DictController extends BaseController {

    @Resource
    private DictService dictService;

    @Operation(summary = "1.获取基础数据列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("page")
    public ReData<Page<Dict>> pageDict(@Validated @RequestBody PageSearchReq req) {
        return ReData.success(dictService.pageDict(req));
    }

    @ApiLog("修改基础数据")
    @Operation(summary = "2.修改基础数据")
    @ApiOperationSupport(order = 2)
    @PostMapping("update")
    public ReData<String> update(@Validated @RequestBody UpdateDictReq req) {
        dictService.update(req);
        return ReData.success();
    }

//    @ApiLog("新增基础数据")
//   @Operation(summary = "新增基础数据")
//    @PostMapping("add")
//    public ReData<String> add(@Validated @RequestBody AddDictReq req) {
//        dictService.add(req);
//        return ReData.success();
//    }

//    @ApiLog("删除基础数据")
//   @Operation(summary = "删除基础数据")
//    @PostMapping("delete")
//    public ReData<String> delete(@Validated @RequestBody IdReq req) {
//        dictService.delete(req.getId());
//        return ReData.success();
//    }

    @ApiLog("3.刷新基础数据缓存")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "刷新缓存")
    @PostMapping("flush/cache")
    public ReData<String> flushCache() {
        dictService.flushCache();
        return ReData.success();
    }

    @Operation(summary = "4.系统配置列表")
    @ApiOperationSupport(order = 4)
    @PostMapping("export/dict")
    public void exportDict(@Validated @RequestBody PageSearchReq req, HttpServletResponse response) throws IOException {
        Page<Dict> page = dictService.pageDict(req);
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(
                        new ThreeTableVo.TopTitleOneVo("系统配置列表", null, null),
                        new ThreeTableVo.MidContentTwoVo(Dict.class, page.getRecords())
                ));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, "系统配置列表", "系统配置列表");
    }
}

package com.zjhh.web.controller.user;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.BooleanUtil;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.system.annotation.ApiLog;
import com.zjhh.user.dao.entity.Component;
import com.zjhh.user.request.*;
import com.zjhh.user.service.MenuService;
import com.zjhh.user.vo.*;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/2 17:49
 */
@Tag(name = "系统管理-菜单管理")
@ApiSupport(order = 102)
@SaCheckLogin
@RequestMapping("menu")
@RestController
public class MenuController extends BaseController {

    @Resource
    private MenuService menuService;

    @Operation(summary = "1.获取菜单路由")
    @ApiOperationSupport(order = 1)
    @PostMapping("get/router")
    @SaCheckRole("common-user")
    public ReData<List<Tree<String>>> getRouter() {
        return ReData.success(menuService.getRouter().getChildren());
    }

    @Operation(summary = "2.添加菜单")
    @ApiOperationSupport(order = 2)
    @ApiLog("添加菜单")
    @SaCheckRole("super-manager")
    @PostMapping("add")
    public ReData<String> add(@RequestBody @Validated AddMenuReq req) {
        menuService.addMenu(req);
        return ReData.success();
    }

    @Operation(summary = "3.获取所有菜单列表")
    @ApiOperationSupport(order = 3)
    @PostMapping("list")
    @SaCheckPermission(value = {"permissionList", "super-manager"}, mode = SaMode.OR)
    public ReData<List<Tree<String>>> list() {
        return ReData.success(menuService.allMenus());
    }

    @Operation(summary = "4.获取上级菜单列表")
    @ApiOperationSupport(order = 4)
    @PostMapping("list/parent")
    @SaCheckPermission(value = {"permissionList", "super-manager"}, mode = SaMode.OR)
    public ReData<List<Tree<String>>> listParent() {
        return ReData.success(menuService.listParentMenus());
    }

    @Operation(summary = "5.编辑菜单")
    @ApiOperationSupport(order = 5)
    @ApiLog("编辑菜单")
    @SaCheckRole("super-manager")
    @PostMapping("update")
    public ReData<String> update(@RequestBody @Validated UpdateMenuReq req) {
        menuService.updateMenu(req);
        return ReData.success();
    }

    @Operation(summary = "6.删除菜单")
    @ApiOperationSupport(order = 6)
    @ApiLog("删除菜单")
    @SaCheckRole("super-manager")
    @PostMapping("delete")
    public ReData<String> delete(@RequestBody @Validated MenuIdReq req) {
        menuService.deleteMenu(req.getMenuId());
        return ReData.success();
    }

    @Operation(summary = "7.批量删除菜单")
    @ApiOperationSupport(order = 7)
    @ApiLog("批量删除菜单")
    @SaCheckRole("super-manager")
    @PostMapping("batch/delete")
    public ReData<String> batchDelete(@RequestBody @Validated MenuIdsReq req) {
        menuService.deleteBatch(req.getMenuIds());
        return ReData.success();
    }

    @Operation(summary = "8.获取菜单详情")
    @ApiOperationSupport(order = 8)
    @PostMapping("get")
    @SaCheckPermission(value = {"permissionList", "super-manager"}, mode = SaMode.OR)
    public ReData<MenuVo> getMenu(@RequestBody @Validated MenuIdReq req) {
        return ReData.success(menuService.getMenu(req.getMenuId()));
    }

    @Operation(summary = "9.获取所有前端组件")
    @ApiOperationSupport(order = 9)
    @PostMapping("list/component")
    @SaCheckPermission(value = {"permissionList", "webComponents", "super-manager"}, mode = SaMode.OR)
    public ReData<List<ComponentVo>> listComponents() {
        return ReData.success(menuService.listComponents());
    }

    @Operation(summary = "10.菜单选择前端组件")
    @ApiOperationSupport(order = 10)
    @PostMapping("list/menu/component")
    @SaCheckPermission(value = {"permissionList", "webComponents", "super-manager"}, mode = SaMode.OR)
    public ReData<List<ComponentVo>> listMenuSelect() {
        return ReData.success(menuService.listMenuSelect());
    }

    @Operation(summary = "11.按钮选择前端组件")
    @ApiOperationSupport(order = 11)
    @PostMapping("list/button/component")
    @SaCheckPermission(value = {"permissionList", "webComponents", "super-manager"}, mode = SaMode.OR)
    public ReData<List<Component>> listButtonSelect(@RequestBody @Validated MenuIdReq req) {
        return ReData.success(menuService.listButtonSelect(req.getMenuId()));
    }

    @Operation(summary = "12.获取前端组件url")
    @ApiOperationSupport(order = 12)
    @PostMapping("get/full_path")
    @SaCheckRole("common-user")
    public ReData<FullPathVo> getFullPath(@RequestBody @Validated RouterReq req) {
        return ReData.success(menuService.getFullPath(req.getRouter()));
    }

    @Operation(summary = "13.获取驾驶舱菜单")
    @ApiOperationSupport(order = 13)
    @PostMapping("list/cockpit/menu")
    @SaCheckRole("common-user")
    public ReData<List<CockpitMenuVo>> listCockpitMenu() {
        return ReData.success(menuService.listCockpitMenu());
    }

    @Operation(summary = "14.根据组件菜单信息")
    @ApiOperationSupport(order = 14)
    @PostMapping("get/menu/info")
    @SaCheckRole("common-user")
    public ReData<MenuInfoVo> getMenuCodeByRouter(@RequestBody @Validated RouterReq req) {
        return ReData.success(menuService.getMenuInfo(req.getRouter()));
    }

    @Operation(summary = "15.获取移动端首页菜单")
    @ApiOperationSupport(order = 15)
    @PostMapping("list/show/home/<USER>")
    @SaCheckRole("common-user")
    public ReData<List<ShowHomeMenuVo>> listShowHomeMenu() {
        return ReData.success(menuService.listShowHomeMenu());
    }

    @Operation(summary = "16.获取数据说明的树形结构")
    @ApiOperationSupport(order = 16)
    @PostMapping("get/desc/menu/tree")
    @SaCheckRole("common-user")
    public ReData<TreeVo> getDescMenuTree() {
        return ReData.success(menuService.getDescMenuTree());
    }

    @Operation(summary = "17.获取映射的菜单")
    @ApiOperationSupport(order = 17)
    @PostMapping("list/select/map/menu")
    @SaCheckRole("common-user")
    public ReData<List<TreeSelectVo>> listSelectMapMenu() {
        return ReData.success(menuService.listSelectMapMenu());
    }

    @Operation(summary = "18.根据menuCode获取链接")
    @ApiOperationSupport(order = 18)
    @PostMapping("get/full/path/by/code")
    @SaCheckRole("common-user")
    public ReData<FullPathVo> getFullPathByMenuCode(@RequestBody @Validated CodeReq req) {
        return ReData.success(menuService.getFullPathByMenuCode(req.getCode()));
    }

    @Operation(summary = "19.菜单列表导出")
    @ApiOperationSupport(order = 19)
    @PostMapping("export/menu")
    @SaCheckPermission(value = {"permissionList", "webComponents", "super-manager"}, mode = SaMode.OR)
    public void exportMenu(HttpServletResponse response) throws IOException {
        List<Tree<String>> list = menuService.allMenus();
        List<List<String>> head = new ArrayList<>();
        head.add(CollUtil.newArrayList("菜单名称"));
        head.add(CollUtil.newArrayList("组件"));
        head.add(CollUtil.newArrayList("菜单编码"));
        head.add(CollUtil.newArrayList("是否外链"));
        head.add(CollUtil.newArrayList("排序"));
        List<List<Object>> resultList = new ArrayList<>();
        list.forEach(tree -> createExportValue(tree, resultList, 0));
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(
                        new ThreeTableVo.TopTitleOneVo("菜单列表", null, null),
                        new ThreeTableVo.MidContentTwoVo(head, resultList)
                ));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, "菜单列表", "菜单列表");
    }

    private void createExportValue(Tree<String> tree, List<List<Object>> resultList, int level) {
        List<Object> result = new ArrayList<>();
        result.add(getSpace(level) + tree.getName());
        result.add(tree.get("router"));
        result.add(tree.get("menuCode"));
        result.add(BooleanUtil.isTrue((Boolean) tree.get("externalLink")) ? "是" : "否");
        result.add(tree.get("sort"));
        resultList.add(result);
        if (CollUtil.isNotEmpty(tree.getChildren())) {
            tree.getChildren().forEach(child -> createExportValue(child, resultList, level + 1));
        }
    }

    private String getSpace(int level) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < level; i++) {
            sb.append("    ");
        }
        return sb.toString();
    }
}

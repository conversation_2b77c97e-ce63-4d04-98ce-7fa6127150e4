package com.zjhh.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.system.oshi.OshiUtil;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.system.dao.entity.LogApi;
import com.zjhh.system.request.PageLogReq;
import com.zjhh.system.service.LogService;
import com.zjhh.system.service.MonitorService;
import com.zjhh.system.vo.JvmInfoVo;
import com.zjhh.system.vo.RedisInfoVo;
import com.zjhh.system.vo.ServerInfoVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import oshi.hardware.GlobalMemory;
import oshi.hardware.HWDiskStore;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/4 15:06
 */
@Tag(name = "系统监控")
@ApiSupport(order = 106)
@SaCheckLogin
@RequestMapping("monitor")
@RestController
public class MonitorController extends BaseController {

    @Resource
    private LogService logService;

    @Resource
    private MonitorService monitorService;

    @Operation(summary = "1.日志列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("log/page")
    public ReData<Page<LogApi>> pageLog(@Validated @RequestBody PageLogReq req) {
        return ReData.success(logService.pageLog(req));
    }

    @Operation(summary = "2.redis信息")
    @ApiOperationSupport(order = 2)
    @PostMapping("redis/get")
    public ReData<List<RedisInfoVo>> getRedisInfo() {
        return ReData.success(monitorService.listAllRedisInfo());
    }

    @Operation(summary = "3.获取redis内存信息")
    @ApiOperationSupport(order = 3)
    @PostMapping("redis/get/memory")
    public ReData<RedisInfoVo> getRedisMemory() {
        return ReData.success(monitorService.getRedisMemory());
    }

    @Operation(summary = "4.获取redis keys数量")
    @ApiOperationSupport(order = 4)
    @PostMapping("redis/count/key")
    public ReData<RedisInfoVo> countRedisKey() {
        return ReData.success(monitorService.countRedisKey());
    }

    @Operation(summary = "5.获取服务器信息")
    @ApiOperationSupport(order = 5)
    @PostMapping("server/get")
    public ReData<ServerInfoVo> getCpuInfo() {
        ServerInfoVo serverInfo = BeanUtil.copyProperties(OshiUtil.getCpuInfo(), ServerInfoVo.class);
        serverInfo.setToTal(serverInfo.getToTal() / 100);
        GlobalMemory memory = OshiUtil.getMemory();
        serverInfo.setMemoryTotal(memory.getTotal() / (1024 * 1024));
        serverInfo.setMemoryAvailable(memory.getAvailable() / (1024 * 1024));
        serverInfo.setCurrentTime(LocalDateTime.now());
        List<HWDiskStore> list = OshiUtil.getDiskStores();
        List<ServerInfoVo.DiskInfo> diskInfos = new ArrayList<>();
        list.forEach(store -> {
            ServerInfoVo.DiskInfo diskInfo = new ServerInfoVo.DiskInfo();
            diskInfo.setModel(store.getModel());
            diskInfo.setSize(store.getSize() / (1024 * 1024 * 1024));
            List<ServerInfoVo.PartitionInfoVo> partInfos = new ArrayList<>();
            store.getPartitions().forEach(part -> {
                ServerInfoVo.PartitionInfoVo partitionInfoVo = new ServerInfoVo.PartitionInfoVo();
                partitionInfoVo.setMountPoint(part.getMountPoint());
                partitionInfoVo.setSize(part.getSize() / (1024 * 1024 * 1024));
                partInfos.add(partitionInfoVo);
            });
            diskInfo.setPartitionInfoVos(partInfos);
            diskInfos.add(diskInfo);
        });
        serverInfo.setDiskInfos(diskInfos);
        return ReData.success(serverInfo);
    }

    @Operation(summary = "6.获取JVM信息")
    @ApiOperationSupport(order = 6)
    @PostMapping("jvm/get")
    public ReData<JvmInfoVo> getJvmInfo() {
        return ReData.success(monitorService.getJvmInfo());
    }

    @Operation(summary = "7.导出日志列表")
    @ApiOperationSupport(order = 7)
    @PostMapping("export/log")
    public void exportLog(@Validated @RequestBody PageLogReq req, HttpServletResponse response) throws IOException {
        Page<LogApi> page = logService.pageLog(req);
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(
                        new ThreeTableVo.TopTitleOneVo("日志列表", null, null),
                        new ThreeTableVo.MidContentTwoVo(LogApi.class, page.getRecords())
                ));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, "日志列表", "日志列表");
    }
}

package com.zjhh.web.controller.aialys;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.aialys.dao.entity.ChatAlysPromptTemplate;
import com.zjhh.aialys.service.AiAlysConfigService;
import com.zjhh.aialys.vo.AlysConfigLeftTreeVo;
import com.zjhh.aialys.vo.AlysConfigVo;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/3 15:11
 */
@Tag(name = "AI解读-配置")
@ApiSupport(order = 202)
@SaCheckLogin
@RequestMapping("ai/alys/chat/config")
@RestController
public class AiAlysChatConfigController extends BaseController {

    @Resource
    private AiAlysConfigService aiAlysConfigService;

    @Operation(summary = "左侧树形结构")
    @PostMapping("list/alys/config/left/tree")
    public ReData<List<AlysConfigLeftTreeVo>> listAlysConfigLeftTree() {
        return ReData.success(aiAlysConfigService.listAlysConfigLeftTree());
    }

    @Operation(summary = "获取配置信息")
    @PostMapping("get/alys/config")
    public ReData<AlysConfigVo> getAlysConfig(@RequestBody @Validated IdReq req) {
        return ReData.success(aiAlysConfigService.getAlysConfig(req.getId()));
    }

    @Operation(summary = "保存配置信息")
    @PostMapping("save/alys/config")
    public ReData<String> saveAlysConfig(@RequestBody @Validated AlysConfigVo configVo) {
        aiAlysConfigService.saveAlysConfig(configVo);
        return ReData.success();
    }

    @Operation(summary = "提示词模板")
    @PostMapping("list/alys/prompt/template")
    public ReData<List<ChatAlysPromptTemplate>> listAlysPromptTemplate(){
        return ReData.success(aiAlysConfigService.listAlysPromptTemplate());
    }
}

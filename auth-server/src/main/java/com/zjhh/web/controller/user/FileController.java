package com.zjhh.web.controller.user;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.io.FileUtil;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.response.ReData;
import com.zjhh.user.request.UploadFileReq;
import com.zjhh.user.utils.BaseFileUtil;
import com.zjhh.user.vo.FileVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/1/18 18:34
 */
@Slf4j
@Tag(name = "系统管理-文件管理")
@ApiSupport(order = 108)
@SaCheckLogin
@RequestMapping("file")
@RestController
public class FileController extends BaseController {

    @Resource
    private BaseFileUtil baseFileUtil;

    @Operation(summary = "1.上传文件")
    @ApiOperationSupport(order = 1)
    @PostMapping("upload")
    public ReData<FileVo> uploadFile(@Validated UploadFileReq req) {
        return ReData.success(baseFileUtil.upload(req.getFile()));
    }

    @Operation(summary = "2.上传图片文件")
    @ApiOperationSupport(order = 2)
    @PostMapping("upload/img")
    public ReData<FileVo> uploadImgFile(@Validated UploadFileReq req) {
        String fileType = FileUtil.getSuffix(req.getFile().getOriginalFilename());
        if (!"jpg".equalsIgnoreCase(fileType) && !"png".equalsIgnoreCase(fileType) && !"jpeg".equalsIgnoreCase(fileType) && !"gif".equalsIgnoreCase(fileType)) {
            throw new BizException("上传图片格式不正确！");
        }
        return ReData.success(baseFileUtil.upload(req.getFile()));
    }

}

package com.zjhh.web.controller.user;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.constant.CommConstants;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.exception.NeedLoginException;
import com.zjhh.comm.request.LicenceApplyReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.LicenceValidVo;
import com.zjhh.system.annotation.ApiLog;
import com.zjhh.user.service.LicenceService;
import com.zjhh.user.vo.LicenceVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/3/23 16:25
 */
@Slf4j
@Tag(name = "系统管理-证书管理")
@ApiSupport(order = 109)
@RestController
@RequestMapping("licence")
@SaCheckRole("super-manager")
public class LicenceController extends BaseController {

    @jakarta.annotation.Resource
    private LicenceService licenceService;

    @SaCheckLogin
    @Operation(summary = "1.上传")
    @ApiOperationSupport(order = 1)
    @PostMapping("upload")
    @ApiLog("上传证书")
    public ReData<String> upload(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            throw new BizException("文件不能为空！");
        }
        File dest = new File(CommConstants.LICENCE_PATH + CommConstants.TEMP_XML);
        try {
            file.transferTo(dest);
            LicenceValidVo validVo = licenceService.refreshLicence(dest, false);
            if (!validVo.getValid()) {
                FileUtil.del(dest);
                throw new BizException("该证书无效，请重新上传！");
            }
            FileUtil.move(dest, new File(CommConstants.LICENCE_PATH + CommConstants.LICENCE_XML), true);
            return ReData.success("该证书有效，有效期为 " + validVo.getLicenceData().getStartDate() + " 至 " + validVo.getLicenceData().getExpireDate());
        } catch (IOException e) {
            log.error("上传的文件有误，请重新上传！", e);
            throw new BizException("该证书无效，请重新上传！");
        }
    }

    @Operation(summary = "2.下载")
    @ApiOperationSupport(order = 2)
    @GetMapping("download")
    public ResponseEntity<Resource> download(HttpServletRequest request) {
        String token = request.getParameter("token");
        if (StrUtil.isBlank(token) || Objects.isNull(StpUtil.getLoginIdByToken(token))) {
            throw new NeedLoginException();
        }
        Resource resource = new FileSystemResource(CommConstants.LICENCE_PATH + CommConstants.LICENCE_XML);
        if (!resource.exists()) {
            throw new BizException("不存在证书文件！");
        }
        String contentType;
        try {
            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            throw new BizException("不存在证书文件！");
        }
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }

    @Operation(summary = "3.获取密钥申请信息")
    @ApiOperationSupport(order = 3)
    @SaCheckLogin
    @PostMapping("get/licence_apply")
    public ReData<LicenceVo> getLicenceApply() {
        return ReData.success(licenceService.getLicenceApply());
    }

    @Operation(summary = "4.保存密钥申请信息")
    @ApiOperationSupport(order = 4)
    @SaCheckLogin
    @PostMapping("save/licence_apply")
    @ApiLog("保存密钥申请信息")
    public ReData<String> licenceApplySave(@Validated @RequestBody LicenceApplyReq req) {
        licenceService.licenceApplySave(req);
        return ReData.success();
    }

    @Operation(summary = "5.下载密钥申请文件")
    @ApiOperationSupport(order = 5)
    @SaCheckLogin
    @GetMapping("apply/download")
    @ApiLog("下载密钥申请文件")
    public ResponseEntity<Resource> applyDownload(HttpServletRequest request) {
        String token = request.getParameter("token");
        if (StrUtil.isBlank(token) || Objects.isNull(StpUtil.getLoginIdByToken(token))) {
            throw new NeedLoginException();
        }
        Resource resource = new FileSystemResource(CommConstants.LICENCE_PATH + CommConstants.LICENCE_APPLY);
        if (!resource.exists()) {
            throw new BizException("不存在申请文件！");
        }
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }

}

package com.zjhh.web.controller.login;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.response.ReData;
import com.zjhh.system.annotation.ApiLog;
import com.zjhh.system.enume.LogApiTypeEnum;
import com.zjhh.user.request.LoginReq;
import com.zjhh.user.request.QrCodeLoginReq;
import com.zjhh.user.request.StThirdLoginReq;
import com.zjhh.user.service.LoginService;
import com.zjhh.user.vo.*;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2020/6/11 17:08
 */
@Tag(name = "登录")
@ApiSupport(order = 1)
@RestController
public class LoginController extends BaseController {

    @Resource
    private LoginService loginService;

    @Operation(summary = "1.登录")
    @ApiOperationSupport(order = 1)
    @ApiLog(value = "登录", type = LogApiTypeEnum.LOGIN)
    @PostMapping("login")
    public ReData<LoginVo> login(@RequestBody @Validated LoginReq req) {
        return ReData.success(loginService.login(req));
    }

    @Operation(summary = "2.扫码登录")
    @ApiOperationSupport(order = 2)
    @ApiLog(value = "扫码登录", type = LogApiTypeEnum.LOGIN)
    @PostMapping("qr_code/login")
    public ReData<ZwddLoginVo> qrCodelogin(@RequestBody @Validated QrCodeLoginReq req) {
        System.out.println("扫码登陆 ---------------------------------- ");
        return ReData.success(loginService.getEmployeeCodeByQrCode(req.getCode()));
    }

    @Operation(summary = "3.免登录")
    @ApiOperationSupport(order = 3)
    @ApiLog(value = "免登登录", type = LogApiTypeEnum.LOGIN)
    @PostMapping("auth_code/login")
    public ReData<ZwddLoginVo> authCodeLogin(@RequestBody @Validated QrCodeLoginReq req) {
        return ReData.success(loginService.getEmployeeCodeByAuthCode(req.getCode()));
    }

    @SaCheckLogin
    @Operation(summary = "4.退出登录")
    @ApiOperationSupport(order = 4)
    @ApiLog(value = "退出登录", type = LogApiTypeEnum.LOGOUT)
    @PostMapping("logout")
    public ReData<String> logout() {
        loginService.logout();
        return ReData.success("退出登录成功！");
    }

    @Operation(summary = "5.获取图形验证码")
    @ApiOperationSupport(order = 5)
    @PostMapping("get/captcha")
    public ReData<CaptchaVo> createCaptcha() {
        return ReData.success(loginService.createCaptcha());
    }

    @Operation(summary = "6.获取初始化配置")
    @ApiOperationSupport(order = 6)
    @RequestMapping("get/init/config")
    public ReData<InitConfigVo> getInitConfig() {
        return ReData.success(loginService.getInitConfig());
    }

    @Operation(summary = "7.获取系统行政区划")
    @ApiOperationSupport(order = 7)
    @PostMapping("get/init/system")
    public ReData<InitSystemVo> getInitSystem() {
        return ReData.success(loginService.getInitSystem());
    }

    @Operation(summary = "8.第三方登录")
    @ApiOperationSupport(order = 8)
    @PostMapping("third/login")
    public ReData<LoginVo> thirdLogin(@RequestBody @Validated StThirdLoginReq req) {
        return ReData.success(loginService.thirdLogin(req));
    }
}

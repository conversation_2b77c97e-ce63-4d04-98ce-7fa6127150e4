package com.zjhh.web.controller.user;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.system.annotation.ApiLog;
import com.zjhh.user.dao.entity.User;
import com.zjhh.user.request.*;
import com.zjhh.user.service.RoleService;
import com.zjhh.user.vo.RoleButtonVo;
import com.zjhh.user.vo.RoleMenuAllVo;
import com.zjhh.user.vo.RoleVo;
import com.zjhh.user.vo.UserVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/6/15 17:39
 */
@Tag(name = "系统管理-角色管理")
@ApiSupport(order = 101)
@SaCheckLogin
@RestController
@RequestMapping("role")
public class RoleController extends BaseController {

    @Resource
    private RoleService roleService;

    @Operation(summary = "1.角色列表分页")
    @ApiOperationSupport(order = 1)
    @SaCheckPermission(value = {"roleUserList", "super-manager"}, mode = SaMode.OR)
    @PostMapping("page")
    public ReData<Page<RoleVo>> pageRole(@RequestBody @Validated PageRoleReq req) {
        return ReData.success(roleService.pageRole(req));
    }

    @Operation(summary = "2.角色列表")
    @ApiOperationSupport(order = 2)
    @SaCheckPermission(value = {"roleUserList", "super-manager"}, mode = SaMode.OR)
    @PostMapping("list")
    public ReData<List<RoleVo>> listRole() {
        return ReData.success(roleService.listRole());
    }

    @Operation(summary = "3.添加角色")
    @ApiOperationSupport(order = 3)
    @ApiLog("添加角色")
    @PostMapping("add")
    @SaCheckRole("system-manager")
    public ReData<String> addRole(@RequestBody @Validated AddRoleReq req) {
        roleService.addRole(req);
        return ReData.success();
    }

    @Operation(summary = "4.修改角色")
    @ApiOperationSupport(order = 4)
    @ApiLog("修改角色")
    @PostMapping("update")
    @SaCheckRole("system-manager")
    public ReData<String> updateRole(@RequestBody @Validated UpdateRoleReq req) {
        roleService.updateRole(req);
        return ReData.success();
    }

    @Operation(summary = "5.删除角色")
    @ApiOperationSupport(order = 5)
    @ApiLog("删除角色")
    @PostMapping("delete")
    @SaCheckRole("system-manager")
    public ReData<String> deleteRole(@RequestBody @Validated RoleIdReq req) {
        roleService.deleteRole(req.getRoleId());
        return ReData.success();
    }

    @Operation(summary = "6.新增用户角色")
    @ApiOperationSupport(order = 6)
    @ApiLog("新增用户角色")
    @PostMapping("add/role-user")
    @SaCheckRole("system-manager")
    public ReData<String> addRoleUser(@RequestBody @Validated RoleUserReq req) {
        roleService.addRoleUser(req.getRoleId(), req.getUserIds());
        return ReData.success();
    }

    @Operation(summary = "7.删除用户角色")
    @ApiOperationSupport(order = 7)
    @ApiLog("删除用户角色")
    @PostMapping("delete/role-user")
    @SaCheckRole("system-manager")
    public ReData<String> deleteRoleUser(@RequestBody @Validated RoleUserReq req) {
        roleService.deleteRoleUser(req.getRoleId(), req.getUserIds());
        return ReData.success();
    }

    @Operation(summary = "8.角色分配权限")
    @ApiOperationSupport(order = 8)
    @ApiLog("角色分配权限")
    @PostMapping("save/menu")
    @SaCheckRole("system-manager")
    public ReData<String> saveRolePermission(@RequestBody @Validated AddRoleMenuReq req) {
        roleService.saveRoleMenu(req);
        return ReData.success();
    }

    @Operation(summary = "9.验证角色名称")
    @ApiOperationSupport(order = 9)
    @PostMapping("check/role-name")
    @SaCheckPermission(value = {"roleUserList", "super-manager"}, mode = SaMode.OR)
    public ReData<Boolean> checkRole(@RequestBody @Validated AddRoleReq req) {
        return ReData.success(roleService.checkRoleName(req.getName()));
    }

    @Operation(summary = "10.获取角色对应用户")
    @ApiOperationSupport(order = 10)
    @PostMapping("page/user")
    @SaCheckPermission(value = {"roleUserList", "super-manager"}, mode = SaMode.OR)
    public ReData<Page<UserVo>> pageRoleUsers(@RequestBody @Validated PageRoleKeyReq req) {
        return ReData.success(roleService.pageRoleUsers(req));
    }

    @Operation(summary = "11.获取角色对应菜单列表")
    @ApiOperationSupport(order = 11)
    @PostMapping("list/menu")
    @SaCheckPermission(value = {"roleUserList", "super-manager"}, mode = SaMode.OR)
    public ReData<RoleMenuAllVo> listRolePermissions(@RequestBody @Validated RoleIdReq req) {
        return ReData.success(roleService.listRoleMenus(req.getRoleId()));
    }

    @Operation(summary = "12.获取没有分配该角色的用户列表")
    @ApiOperationSupport(order = 12)
    @PostMapping("page/all_user")
    @SaCheckPermission(value = {"roleUserList", "super-manager"}, mode = SaMode.OR)
    public ReData<Page<User>> pageAllRoleUsers(@RequestBody @Validated PageRoleKeyReq req) {
        return ReData.success(roleService.pageAllRoleUsers(req));
    }

    @Operation(summary = "13.获取角色对应按钮列表")
    @ApiOperationSupport(order = 13)
    @PostMapping("list/button")
    @SaCheckPermission(value = {"roleUserList", "super-manager"}, mode = SaMode.OR)
    public ReData<RoleButtonVo> listRoleButtons(@RequestBody @Validated RoleMenuIdReq req) {
        return ReData.success(roleService.listRoleButtons(req.getRoleId(), req.getMenuCode()));
    }

    @Operation(summary = "14.角色列表导出")
    @ApiOperationSupport(order = 14)
    @PostMapping("export/role")
    @SaCheckPermission(value = {"roleUserList", "super-manager"}, mode = SaMode.OR)
    public void exportRole(@RequestBody @Validated PageRoleReq req, HttpServletResponse response) throws IOException {
        Page<RoleVo> page = roleService.pageRole(req);
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(
                        new ThreeTableVo.TopTitleOneVo("角色列表", null, null),
                        new ThreeTableVo.MidContentTwoVo(RoleVo.class, page.getRecords())
                ));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, "角色列表", "角色列表");
    }
}

package com.zjhh.web.controller.user;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.alibaba.fastjson2.JSONObject;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.response.ReData;
import com.zjhh.user.dao.entity.MobileDefaultSet;
import com.zjhh.user.request.CodeReq;
import com.zjhh.user.request.ComponentReq;
import com.zjhh.user.request.MenuCodeReq;
import com.zjhh.user.request.systemshow.*;
import com.zjhh.user.service.SystemShowService;
import com.zjhh.user.vo.FullPathVo;
import com.zjhh.user.vo.TreeVo;
import com.zjhh.user.vo.systemshow.SysButtonVo;
import com.zjhh.user.vo.systemshow.SysSearchVo;
import com.zjhh.user.vo.systemshow.SysTabVo;
import com.zjhh.user.vo.systemshow.SystemShowVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/26 17:31
 */
@Tag(name = "系统显示配置")
@ApiSupport(order = 201)
@RestController
@RequestMapping("system/show")
@SaCheckLogin
public class SystemShowController extends BaseController {

    @Resource
    private SystemShowService systemShowService;

    @Operation(summary = "1.获取tab列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("list/sys/tab")
    public ReData<List<SysTabVo>> listSysTab(@RequestBody @Validated MenuCodeReq req) {
        return ReData.success(systemShowService.listSysTab(req.getMenuCode()));
    }

    @Operation(summary = "2.获取查询条件列表")
    @ApiOperationSupport(order = 2)
    @PostMapping("list/sys/search")
    public ReData<List<SysSearchVo>> listSysSearch(@RequestBody @Validated MenuCodeReq req) {
        return ReData.success(systemShowService.listSysSearch(req.getMenuCode()));
    }

    @Operation(summary = "3.获取按钮列表")
    @ApiOperationSupport(order = 3)
    @PostMapping("list/sys/button")
    public ReData<List<SysButtonVo>> listSysButton(@RequestBody @Validated MenuCodeReq req) {
        return ReData.success(systemShowService.listSysButton(req.getMenuCode()));
    }

    @Operation(summary = "4.修改页签设置")
    @ApiOperationSupport(order = 4)
    @PostMapping("update/sys/tab")
    public ReData<String> updateSysTab(@RequestBody @Validated UpdateSysTabReq req) {
        systemShowService.updateSysTab(req);
        return ReData.success();
    }

    @Operation(summary = "5.修改查询条件")
    @ApiOperationSupport(order = 5)
    @PostMapping("update/sys/search")
    public ReData<String> updateSysSearch(@RequestBody @Validated UpdateSysSearchReq req) {
        systemShowService.updateSysSearch(req);
        return ReData.success();
    }

    @Operation(summary = "6.修改按钮")
    @ApiOperationSupport(order = 6)
    @PostMapping("update/sys/button")
    public ReData<String> updateSysButton(@RequestBody @Validated UpdateSysButtonReq req) {
        systemShowService.updateSysButton(req);
        return ReData.success();
    }

    @Operation(summary = "7.获取菜单树形结构")
    @ApiOperationSupport(order = 7)
    @PostMapping("get/menu/tree")
    public ReData<TreeVo> getMenuTree() {
        return ReData.success(systemShowService.getMenuTree());
    }

    @Operation(summary = "8.获取系统显示配置")
    @ApiOperationSupport(order = 8)
    @PostMapping("get/system/show")
    public ReData<SystemShowVo> getSystemShow(@RequestBody @Validated GetSystemShowReq req) {
        return ReData.success(systemShowService.getSystemShow(req));
    }

    @Operation(summary = "9.获取字段跳转链接")
    @ApiOperationSupport(order = 9)
    @PostMapping("get/column/link")
    public ReData<FullPathVo> getColumnLinkMenu(@RequestBody @Validated ColumnLinkMenuReq req) {
        return ReData.success(systemShowService.getColumnLinkMenu(req));
    }

    @Operation(summary = "10.获取移动端默认配置")
    @ApiOperationSupport(order = 10)
    @PostMapping("get/mobile/default/set")
    public ReData<List<MobileDefaultSet>> getMobileDefaultSet() {
        return ReData.success(systemShowService.getMobileDefaultSet());
    }

    @Operation(summary = "11.获取移动端配置的值")
    @ApiOperationSupport(order = 11)
    @PostMapping("get/mobile/default/set/value")
    public ReData<JSONObject> getMobileDefaultSetValue(@RequestBody @Validated CodeReq req) {
        return ReData.success(systemShowService.getMobileDefaultSetValue(req.getCode()));
    }

    @Operation(summary = "12.根据组件获取系统显示配置")
    @ApiOperationSupport(order = 12)
    @PostMapping("get/system/show/component")
    public ReData<SystemShowVo> getSystemShowByComponent(@RequestBody @Validated ComponentReq req) {
        return ReData.success(systemShowService.getSystemShowByComponent(req.getComponent()));
    }

    @Operation(summary = "13.获取系统显示配置-列跳转菜单")
    @ApiOperationSupport(order = 13)
    @PostMapping("get/jump/menu/tree")
    public ReData<TreeVo> getJumpMenuTree() {
        return ReData.success(systemShowService.getJumpMenuTree());
    }

}

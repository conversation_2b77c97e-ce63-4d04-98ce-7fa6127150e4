package com.zjhh.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.request.IdsReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.system.annotation.ApiLog;
import com.zjhh.system.dao.entity.QuartzJob;
import com.zjhh.system.request.AddQuartzJobReq;
import com.zjhh.system.request.PageQuartzJobReq;
import com.zjhh.system.request.UpdateQuartzJobReq;
import com.zjhh.system.service.QuartzService;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2021/3/10 18:33
 */
@Tag(name = "系统监控-定时任务")
@ApiSupport(order = 107)
@SaCheckLogin
@RequestMapping("quartz")
@RestController
public class QuartzController extends BaseController {

    @Resource
    private QuartzService quartzService;

    @Operation(summary = "1.新增定时任务")
    @ApiOperationSupport(order = 1)
    @ApiLog("新增定时任务")
    @PostMapping("add")
    public ReData<String> add(@Validated @RequestBody AddQuartzJobReq req) {
        quartzService.add(req);
        return ReData.success();
    }

    @Operation(summary = "2.修改定时任务")
    @ApiOperationSupport(order = 2)
    @ApiLog("修改定时任务")
    @PostMapping("update")
    public ReData<String> update(@Validated @RequestBody UpdateQuartzJobReq req) {
        quartzService.update(req);
        return ReData.success();
    }

    @Operation(summary = "3.立刻运行一次定时任务")
    @ApiOperationSupport(order = 3)
    @ApiLog("立刻运行一次定时任务")
    @PostMapping("runOnce")
    public ReData<String> runOnce(@Validated @RequestBody IdReq req) {
        quartzService.runOnce(req.getId());
        return ReData.success();
    }

    @Operation(summary = "4.删除任务定时任务")
    @ApiOperationSupport(order = 4)
    @ApiLog("删除任务定时任务")
    @PostMapping("delete")
    public ReData<String> delete(@Validated @RequestBody IdReq req) {
        quartzService.delete(req.getId());
        return ReData.success();
    }

    @Operation(summary = "5.停止定时任务")
    @ApiOperationSupport(order = 5)
    @ApiLog("停止定时任务")
    @PostMapping("pause")
    public ReData<String> pause(@Validated @RequestBody IdReq req) {
        quartzService.pause(req.getId());
        return ReData.success();
    }

    @Operation(summary = "6.重启定时任务")
    @ApiOperationSupport(order = 6)
    @ApiLog("重启定时任务")
    @PostMapping("resume")
    public ReData<String> resume(@Validated @RequestBody IdReq req) {
        quartzService.resume(req.getId());
        return ReData.success();
    }

    @Operation(summary = "7.定时任务列表")
    @ApiOperationSupport(order = 7)
    @PostMapping("page")
    public ReData<Page<QuartzJob>> pageQuartzJob(@Validated @RequestBody PageQuartzJobReq req) {
        return ReData.success(quartzService.pageQuartzJob(req));
    }

    @Operation(summary = "8.批量删除任务定时任务")
    @ApiOperationSupport(order = 8)
    @ApiLog("批量删除任务定时任务")
    @PostMapping("delete/batch")
    public ReData<String> deleteBatch(@Validated @RequestBody IdsReq req) {
        quartzService.deleteBatch(req.getIds());
        return ReData.success();
    }
}

package com.zjhh.web.controller.user;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.lang.tree.Tree;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.request.IdsReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.system.request.PageSearchReq;
import com.zjhh.user.dao.entity.Area;
import com.zjhh.user.dao.entity.Unit;
import com.zjhh.user.request.*;
import com.zjhh.user.service.OrganizeService;
import com.zjhh.user.vo.SelectAreaVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/10 9:15
 */
@Tag(name = "系统管理-行政区划-单位")
@ApiSupport(order = 103)
@SaCheckLogin
@RequestMapping("organize")
@RestController
public class OrganizeController extends BaseController {

    @Resource
    private OrganizeService organizeService;


    @Operation(summary = "1.获取行政区划分页")
    @ApiOperationSupport(order = 1)
    @PostMapping("page/area")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<Page<Area>> pageAreas(@RequestBody @Validated PageSearchReq req) {
        return ReData.success(organizeService.pageAreas(req));
    }

    @Operation(summary = "2.获取单位分页")
    @ApiOperationSupport(order = 2)
    @PostMapping("page/unit")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<Page<Unit>> pageUnits(@RequestBody @Validated PageSearchReq req) {
        return ReData.success(organizeService.pageUnits(req));
    }

    @Operation(summary = "3.添加行政区划")
    @ApiOperationSupport(order = 3)
    @PostMapping("add/area")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<String> addArea(@RequestBody @Validated AddAreaReq req) {
        organizeService.addArea(req);
        return ReData.success();
    }

    @Operation(summary = "4.添加预算单位")
    @ApiOperationSupport(order = 4)
    @PostMapping("add/unit")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<String> addUnit(@RequestBody @Validated AddUnitReq req) {
        organizeService.addUnit(req);
        return ReData.success();
    }

    @Operation(summary = "5.编辑行政区划")
    @ApiOperationSupport(order = 5)
    @PostMapping("update/area")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<String> updateArea(@RequestBody @Validated UpdateAreaReq req) {
        organizeService.updateArea(req);
        return ReData.success();
    }

    @Operation(summary = "6.编辑单位")
    @ApiOperationSupport(order = 6)
    @PostMapping("update/unit")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<String> updateUnit(@RequestBody @Validated UpdateUnitReq req) {
        organizeService.updateUnit(req);
        return ReData.success();
    }

    @Operation(summary = "7.批量删除行政区划")
    @ApiOperationSupport(order = 7)
    @PostMapping("delete/batch/area")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<String> deleteBatchArea(@RequestBody @Validated IdsReq req) {
        organizeService.deleteBatchArea(req.getIds());
        return ReData.success();
    }

    @Operation(summary = "8.批量删除单位")
    @ApiOperationSupport(order = 8)
    @PostMapping("delete/batch/unit")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<String> deleteBatchUnit(@RequestBody @Validated IdsReq req) {
        organizeService.deleteBatchUnit(req.getIds());
        return ReData.success();
    }

    @Operation(summary = "9.获取树形可选择的行政区划")
    @ApiOperationSupport(order = 9)
    @PostMapping("tree/area")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<List<Tree<String>>> treeArea() {
        return ReData.success(organizeService.treeArea());
    }

    @Operation(summary = "10.新建单位-获取可供选择的上级")
    @ApiOperationSupport(order = 10)
    @PostMapping("tree/select/organize")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<List<Tree<String>>> treeSelectArea() {
        return ReData.success(organizeService.treeSelectOrganize());
    }

    @Operation(summary = "11.获取组织结构树形")
    @ApiOperationSupport(order = 11)
    @PostMapping("tree/organize")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<List<Tree<String>>> treeOrganize() {
        return ReData.success(organizeService.treeOrganize());
    }

    @Operation(summary = "12.获取切换的行政区划选择")
    @ApiOperationSupport(order = 12)
    @PostMapping("get/select/area")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<SelectAreaVo> getSelectArea() {
        return ReData.success(organizeService.getSelectArea());
    }

    @Operation(summary = "13.切换行政区划")
    @ApiOperationSupport(order = 13)
    @PostMapping("change/area")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<String> changeArea(@RequestBody @Validated AreaCodeReq req) {
        organizeService.changeArea(req.getAreaCode());
        return ReData.success();
    }

    @Operation(summary = "14.行政区划导出")
    @ApiOperationSupport(order = 14)
    @PostMapping("export/area")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public void exportArea(@RequestBody @Validated PageSearchReq req, HttpServletResponse response) throws IOException {
        Page<Area> page = organizeService.pageAreas(req);
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(
                        new ThreeTableVo.TopTitleOneVo("行政区划", null, null),
                        new ThreeTableVo.MidContentTwoVo(Area.class, page.getRecords())
                ));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, "行政区划", "行政区划");
    }

    @Operation(summary = "15.预算单位导出")
    @ApiOperationSupport(order = 15)
    @PostMapping("export/unit")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public void exportUnit(@RequestBody @Validated PageSearchReq req, HttpServletResponse response) throws IOException {
        Page<Unit> page = organizeService.pageUnits(req);
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(
                        new ThreeTableVo.TopTitleOneVo("预算单位", null, null),
                        new ThreeTableVo.MidContentTwoVo(Unit.class, page.getRecords())
                ));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, "预算单位", "预算单位");
    }

    @Operation(summary = "17.获取预算单位选择")
    @ApiOperationSupport(order = 17)
    @PostMapping("tree/unit/select")
    @SaCheckPermission(value = {"AreaList", "UnitList", "super-manager"}, mode = SaMode.OR)
    public ReData<TreeSelectVo> treeUnitSelect() {
        return ReData.success(organizeService.listUnitSelect());
    }
}

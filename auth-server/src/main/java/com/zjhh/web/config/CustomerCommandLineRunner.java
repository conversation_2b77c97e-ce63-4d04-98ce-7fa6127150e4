package com.zjhh.web.config;

import cn.hutool.core.io.FileUtil;
import com.zjhh.comm.constant.CommConstants;
import com.zjhh.system.service.QuartzService;
import com.zjhh.user.service.LicenceService;
import jakarta.annotation.Resource;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/3/10 18:26
 */
@Component
public class CustomerCommandLineRunner implements CommandLineRunner {

    @Resource
    private QuartzService quartzService;

    @Resource
    private LicenceService licenceService;

    @Override
    public void run(String... args) throws Exception {
        quartzService.initJob();
        licenceService.refreshLicence(FileUtil.file(CommConstants.LICENCE_PATH + CommConstants.LICENCE_XML), true);
    }
}

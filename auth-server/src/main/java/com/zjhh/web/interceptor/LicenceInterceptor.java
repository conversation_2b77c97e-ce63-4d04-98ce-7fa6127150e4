package com.zjhh.web.interceptor;

import cn.hutool.extra.spring.SpringUtil;
import com.zjhh.comm.exception.BizException;
import com.zjhh.user.service.impl.UserSession;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @since 2023/11/1 16:17
 */
public class LicenceInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        UserSession userSession = SpringUtil.getBean(UserSession.class);
        if (userSession.checkLicence()) {
            return true;
        }
        throw new BizException("没有权限！");
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }
}

package com.zjhh.web;

import cn.dev33.satoken.SaManager;
import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 * @since 2020/6/12 14:00
 */
@Slf4j
@SpringBootApplication
@ComponentScan("com.zjhh")
@EnableMethodCache(basePackages = "com.zjhh")
public class WebBaseApplication {


    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext application = SpringApplication.run(WebBaseApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        if (StrUtil.isBlank(path)) {
            path = "";
        }
        log.info("\n----------------------------------------------------------\n\t" +
                "Application Zjhh-platform is running! Access URLs:\n\t" +
                "Local: \t\thttp://localhost:" + port + path + "/\n\t" +
                "External: \thttp://localhost:" + port + path + "/\n\t" +
                "Swagger-ui: \thttp://localhost:" + port + path + "/doc.html\n\t" +
                "sa-token配置如下：" + SaManager.getConfig() +
                "----------------------------------------------------------");
    }

}
